.ax_default {
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  line-height:normal;
  text-transform:none;
}
._图像 {
}
._标题_1 {
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:bold;
  font-style:normal;
  font-size:32px;
  text-align:left;
}
._标题_2 {
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:bold;
  font-style:normal;
  font-size:24px;
  text-align:left;
}
._标题_3 {
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:bold;
  font-style:normal;
  font-size:18px;
  text-align:left;
}
._标题_4 {
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:bold;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
._标题_5 {
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:bold;
  font-style:normal;
  text-align:left;
}
._标题_6 {
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:bold;
  font-style:normal;
  font-size:10px;
  text-align:left;
}
._文本 {
  text-align:left;
}
.text_field {
  color:#000000;
  text-align:left;
}
.text_area {
  color:#000000;
  text-align:left;
}
.form_hint {
  color:#999999;
}
.form_disabled {
}
._表单提示 {
  color:#999999;
}
._表单禁用 {
}
._流程形状 {
}
.paragraph {
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
  text-align:left;
}
._形状 {
  font-family:"Noto Sans CJK", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
.box_2 {
}
.box_1 {
}
.label {
  font-family:"苹方", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:center;
  line-height:22px;
}
.droplist {
  color:#000000;
  text-align:left;
}
.form_disabled1 {
}
.box_3 {
}
._列表字段1 {
  font-family:"苹方", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
  line-height:56px;
}
.box_11 {
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
}
.text_area1 {
  color:#000000;
  text-align:left;
}
.text_field1 {
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
  color:#000000;
  text-align:left;
}
.form_hint1 {
  color:#999999;
}
.checkbox {
  text-align:left;
}
.shape {
}
.primary_button {
  color:#FFFFFF;
}
.radio_button {
  text-align:left;
}
textarea, select, input, button, div, svg { outline: none; }
