{"name": "amis-boilerplate", "version": "1.0.0", "description": "基于 amis 的项目模板", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js", "gh-pages": "rm -rf gh-pages && fis3 release gh-pages -c", "deploy-gh-pages": "git subtree push --prefix gh-pages origin gh-pages"}, "keywords": ["amis", "boilerplate", "admin", "react"], "author": "fex", "license": "MIT", "devDependencies": {"body-parser": "^1.19.0", "express": "^4.17.1", "morgan": "^1.10.0", "nodemon": "^2.0.7", "reload": "^3.1.1"}}