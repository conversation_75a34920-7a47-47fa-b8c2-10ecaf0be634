@font-face {
  font-family: "Source Sans Pro";
  font-style: normal;
  font-weight: 200;
  font-display: swap;
  src:
    local("Source Sans Pro ExtraLight"),
    local("SourceSansPro-ExtraLight"),
    url("source-sans-pro-200.woff2") format("woff2");
}

@font-face {
  font-family: "Source Sans Pro";
  font-style: italic;
  font-weight: 200;
  font-display: swap;
  src:
    local("Source Sans Pro ExtraLight Italic"),
    local("SourceSansPro-ExtraLightItalic"),
    url("source-sans-pro-200italic.woff2") format("woff2");
}

@font-face {
  font-family: "Source Sans Pro";
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: local("Source Sans Pro Light"), local("SourceSansPro-Light"), url("source-sans-pro-300.woff2") format("woff2");
}

@font-face {
  font-family: "Source Sans Pro";
  font-style: italic;
  font-weight: 300;
  font-display: swap;
  src:
    local("Source Sans Pro Light Italic"),
    local("SourceSansPro-LightItalic"),
    url("source-sans-pro-300italic.woff2") format("woff2");
}

@font-face {
  font-family: "Source Sans Pro";
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src:
    local("Source Sans Pro Regular"),
    local("SourceSansPro-Regular"),
    url("source-sans-pro-regular.woff2") format("woff2");
}

@font-face {
  font-family: "Source Sans Pro";
  font-style: italic;
  font-weight: 400;
  font-display: swap;
  src:
    local("Source Sans Pro Italic"),
    local("SourceSansPro-Italic"),
    url("source-sans-pro-italic.woff2") format("woff2");
}

@font-face {
  font-family: "Source Sans Pro";
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src:
    local("Source Sans Pro SemiBold"),
    local("SourceSansPro-SemiBold"),
    url("source-sans-pro-600.woff2") format("woff2");
}

@font-face {
  font-family: "Source Sans Pro";
  font-style: italic;
  font-weight: 600;
  font-display: swap;
  src:
    local("Source Sans Pro SemiBold Italic"),
    local("SourceSansPro-SemiBoldItalic"),
    url("source-sans-pro-600italic.woff2") format("woff2");
}
