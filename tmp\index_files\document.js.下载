$axure.loadDocument(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,_(c,d,e,f,g,d,h,d,i,d,j,k,l,d,m,f,n,f,o,d,p,d,q,r,s,d),t,_(u,[_(v,w,x,y,z,A,B,w,C,[_(v,D,x,E,z,F,B,G),_(v,H,x,I,z,F,B,J),_(v,w,x,K,z,A,B,w,C,[_(v,L,x,M,z,F,B,N,C,[_(v,O,x,P,z,F,B,Q,C,[_(v,R,x,S,z,F,B,T,C,[_(v,U,x,V,z,F,B,W),_(v,X,x,Y,z,F,B,Z)]),_(v,ba,x,bb,z,F,B,bc)])])]),_(v,w,x,bd,z,A,B,w,C,[_(v,be,x,bf,z,F,B,bg),_(v,bh,x,V,z,F,B,bi)]),_(v,w,x,bj,z,A,B,w,C,[_(v,bk,x,bj,z,F,B,bl)]),_(v,w,x,bm,z,A,B,w,C,[_(v,bn,x,bo,z,F,B,bp,C,[_(v,bq,x,br,z,F,B,bs)])])]),_(v,w,x,bt,z,A,B,w,C,[_(v,bu,x,E,z,F,B,bv),_(v,w,x,K,z,A,B,w,C,[_(v,bw,x,M,z,F,B,bx,C,[_(v,by,x,P,z,F,B,bz,C,[_(v,bA,x,S,z,F,B,bB,C,[_(v,bC,x,V,z,F,B,bD)])])])])])]),bE,[bF,bG,bH,bI],bJ,[bK,bL,bM],bN,_(bO,w),bP,_(bQ,_(v,bR,bS,bT,bU,bV,bW,bX,bY,bZ,ca,_(cb,cc,cd,ce),cf,cg,ch,f,ci,cj,ck,bX,cl,bX,cm,cn,co,f,cp,_(cq,cr,cs,cr),ct,_(cu,cr,cv,cr),cw,d,cx,f,cy,bR,cz,_(cb,cc,cd,cA),cB,_(cb,cc,cd,cC),cD,cE,cF,cc,cG,[cH],cI,cE,cJ,cK,cL,cM,cN,cM,cO,cP,cQ,cR,cS,cR,cT,cR,cU,cR,cV,_(),cW,null,cX,null,cY,cK,cZ,_(da,f,db,dc,dd,dc,de,dc,df,cr,cd,_(dg,cH,dh,cH,di,cH,dj,dk)),dl,_(da,f,db,cr,dd,dc,de,dc,df,cr,cd,_(dg,cH,dh,cH,di,cH,dj,dk)),dm,_(da,f,db,dn,dd,dn,de,dc,df,cr,cd,_(dg,cH,dh,cH,di,cH,dj,dp)),dq,_(da,f,dr,ds),dt,_(da,f,dr,ds),du,dv,dw,_(dx,cH,dy,cr),dz,_(dA,dn,dB,dn,dC,cr,dD,cr,dE,cr),dF,_(cq,dG,cs,dG)),dH,_(dI,_(v,dJ,cD,cK),dK,_(v,dL,cf,dM,bU,dN,cD,cK,cz,_(cb,cc,cd,dO,cI,cr),ci,dP,cO,dQ,cQ,cK,cS,cK,cT,cK,cU,cK),dR,_(v,dS,cf,dT,bU,dN,cD,cK,cz,_(cb,cc,cd,dO,cI,cr),ci,dP,cO,dQ,cQ,cK,cS,cK,cT,cK,cU,cK),dU,_(v,dV,cf,dW,bU,dN,cD,cK,cz,_(cb,cc,cd,dO,cI,cr),ci,dP,cO,dQ,cQ,cK,cS,cK,cT,cK,cU,cK),dX,_(v,dY,cf,dZ,bU,dN,cD,cK,cz,_(cb,cc,cd,dO,cI,cr),ci,dP,cO,dQ,cQ,cK,cS,cK,cT,cK,cU,cK),ea,_(v,eb,bU,dN,cD,cK,cz,_(cb,cc,cd,dO,cI,cr),ci,dP,cO,dQ,cQ,cK,cS,cK,cT,cK,cU,cK),ec,_(v,ed,cf,ee,bU,dN,cD,cK,cz,_(cb,cc,cd,dO,cI,cr),ci,dP,cO,dQ,cQ,cK,cS,cK,cT,cK,cU,cK),ef,_(v,eg,cD,cK,cz,_(cb,cc,cd,dO,cI,cr),ci,dP,cO,dQ,cQ,cK,cS,cK,cT,cK,cU,cK),eh,_(v,ei,ca,_(cb,cc,cd,ej),ci,dP,cO,cP),ek,_(v,el,ca,_(cb,cc,cd,ej),ci,dP,cO,dQ),em,_(v,en,ca,_(cb,cc,cd,eo)),ep,_(v,eq,cz,_(cb,cc,cd,er)),es,_(v,et,ca,_(cb,cc,cd,eo)),eu,_(v,ev,cz,_(cb,cc,cd,er)),ew,_(v,ex,cz,_(cb,ey,ez,_(cq,eA,cs,cr),eB,_(cq,eA,cs,dn),eC,[_(cd,cA,eD,cr,cI,dn),_(cd,eE,eD,cr,cI,dn),_(cd,eF,eD,dn,cI,dn),_(cd,cA,eD,dn,cI,dn)])),eG,_(v,eH,bS,eI,ci,dP,cz,_(cb,cc,cd,dO,cI,cr),cD,cK,cO,dQ,cQ,cK,cS,cK,cT,cK,cU,cK),eJ,_(v,eK,bS,eL,cf,dZ,cz,_(cb,cc,cd,eo),cB,_(cb,cc,cd,dO,cI,cr),cD,cK,cZ,_(da,f,db,cr,dd,cr,de,eM,df,cr,cd,_(dg,cH,dh,cH,di,cH,dj,eN)),dl,_(da,f,db,cr,dd,cr,de,eM,df,cr,cd,_(dg,cH,dh,cH,di,cH,dj,eN))),eO,_(v,eP,cz,_(cb,cc,cd,eE),cD,cK),eQ,_(v,eR),eS,_(v,eT,bS,eU,bU,bV,bW,bX,bY,bZ,cf,dZ,ci,cj,ck,eV,cz,_(cb,cc,cd,dO,cI,cr),cD,cK,cO,cP,cQ,cK,cS,cK,cT,cK,cU,cK),eW,_(v,eX,ca,_(cb,cc,cd,ej),ci,dP,cO,dQ),eY,_(v,eZ,cz,_(cb,cc,cd,er)),fa,_(v,fb,cz,_(cb,cc,cd,fc),cD,cK),fd,_(v,fe,bS,eU,cf,dZ,ci,dP,ck,ff,cz,_(cb,cc,cd,dO,cI,cr),cD,cK,cO,dQ,cQ,cK,cS,cK,cT,cK,cU,cK),fg,_(v,fh,bS,eI),fi,_(v,fj,ca,_(cb,cc,cd,ej),ci,dP,cO,dQ),fk,_(v,fl,bS,eI,ca,_(cb,cc,cd,ej),ci,dP,cO,cP),fm,_(v,fn,ca,_(cb,cc,cd,eo)),fo,_(v,fp,ci,dP,cO,dQ,cS,cK,cU,cK),fq,_(v,fr),fs,_(v,ft,ca,_(cb,cc,cd,cA),cz,_(cb,cc,cd,fu),cD,cK,cJ,bZ),fv,_(v,fw,ci,dP,cO,dQ,cS,cK,cU,cK)),fx,_()));}; 
var b="configuration",c="showPageNotes",d=true,e="showPageNoteNames",f=false,g="showAnnotations",h="showAnnotationsSidebar",i="showConsole",j="linkStyle",k="displayMultipleTargetsOnly",l="linkFlowsToPages",m="linkFlowsToPagesNewWindow",n="useLabels",o="useViews",p="loadFeedbackPlugin",q="prototypeId",r="VBQ4F3",s="isAxshare",t="sitemap",u="rootNodes",v="id",w="",x="pageName",y="原型-浅色",z="type",A="Folder",B="url",C="children",D="iu1cwq",E="index",F="Wireframe",G="index.html",H="nb8af3",I="index-收起",J="index-__.html",K="项目管理",L="4mq6bs",M="项目列表",N="项目列表.html",O="teu8wx",P="任务列表",Q="任务列表.html",R="29o5gh",S="任务概览",T="任务概览.html",U="th2bb0",V="缺陷详情",W="缺陷详情.html",X="gpf7a4",Y="AI分析流程全屏",Z="ai分析流程全屏.html",ba="jnppyf",bb="任务概览全",bc="任务概览全.html",bd="缺陷管理",be="aov9n7",bf="缺陷列表",bg="缺陷列表.html",bh="sjlhng",bi="缺陷详情_1.html",bj="报告管理",bk="odbxyv",bl="报告管理.html",bm="模型配置",bn="g36l5t",bo="配置列表",bp="配置列表.html",bq="r59ynl",br="配置详情",bs="配置详情.html",bt="原型-深色",bu="58vxju",bv="index_1.html",bw="e40m82",bx="项目列表_1.html",by="pfn3y7",bz="任务列表_1.html",bA="o23a1m",bB="任务概览_1.html",bC="yf20mj",bD="缺陷详情_2.html",bE="additionalJs",bF="plugins/debug/debug.js",bG="plugins/sitemap/sitemap.js",bH="plugins/page_notes/page_notes.js",bI="resources/scripts/hintmanager.js",bJ="additionalCss",bK="plugins/debug/styles/debug.css",bL="plugins/sitemap/styles/sitemap.css",bM="plugins/page_notes/styles/page_notes.css",bN="globalVariables",bO="onloadvariable",bP="stylesheet",bQ="defaultStyle",bR="627587b6038d43cca051c114ac41ad32",bS="fontName",bT="\"Arial Normal\", \"Arial\", sans-serif",bU="fontWeight",bV="400",bW="fontStyle",bX="normal",bY="fontStretch",bZ="5",ca="foreGroundFill",cb="fillType",cc="solid",cd="color",ce=0xFF333333,cf="fontSize",cg="13px",ch="underline",ci="horizontalAlignment",cj="center",ck="lineSpacing",cl="characterSpacing",cm="letterCase",cn="none",co="strikethrough",cp="location",cq="x",cr=0,cs="y",ct="size",cu="width",cv="height",cw="visible",cx="limbo",cy="baseStyle",cz="fill",cA=0xFFFFFFFF,cB="borderFill",cC=0xFF797979,cD="borderWidth",cE="1",cF="linePattern",cG="linePatternArray",cH=0,cI="opacity",cJ="cornerRadius",cK="0",cL="borderVisibility",cM="top right bottom left",cN="cornerVisibility",cO="verticalAlignment",cP="middle",cQ="paddingLeft",cR="2",cS="paddingTop",cT="paddingRight",cU="paddingBottom",cV="stateStyles",cW="image",cX="imageFilter",cY="rotation",cZ="outerShadow",da="on",db="offsetX",dc=5,dd="offsetY",de="blurRadius",df="spread",dg="r",dh="g",di="b",dj="a",dk=0.34901960784313724,dl="innerShadow",dm="textShadow",dn=1,dp=0.6470588235294118,dq="widgetBlur",dr="radius",ds=4,dt="backdropBlur",du="viewOverride",dv="19e82109f102476f933582835c373474",dw="transition",dx="easing",dy="duration",dz="transform",dA="scaleX",dB="scaleY",dC="translateX",dD="translateY",dE="rotate",dF="transformOrigin",dG=50,dH="customStyles",dI="_图像",dJ="75a91ee5b9d042cfa01b8d565fe289c0",dK="_标题_1",dL="1111111151944dfba49f67fd55eb1f88",dM="32px",dN="bold",dO=0xFFFFFF,dP="left",dQ="top",dR="_标题_2",dS="b3a15c9ddde04520be40f94c8168891e",dT="24px",dU="_标题_3",dV="8c7a4c5ad69a4369a5f7788171ac0b32",dW="18px",dX="_标题_4",dY="e995c891077945c89c0b5fe110d15a0b",dZ="14px",ea="_标题_5",eb="386b19ef4be143bd9b6c392ded969f89",ec="_标题_6",ed="fc3b9a13b5574fa098ef0a1db9aac861",ee="10px",ef="_文本",eg="4988d43d80b44008a4a415096f1632af",eh="text_field",ei="44157808f2934100b68f2394a66b2bba",ej=0xFF000000,ek="text_area",el="42ee17691d13435b8256d8d0a814778f",em="form_hint",en="3c35f7f584574732b5edbd0cff195f77",eo=0xFF999999,ep="form_disabled",eq="2829faada5f8449da03773b96e566862",er=0xFFF0F0F0,es="_表单提示",et="4889d666e8ad4c5e81e59863039a5cc0",eu="_表单禁用",ev="9bd0236217a94d89b0314c8c7fc75f16",ew="_流程形状",ex="df01900e3c4e43f284bafec04b0864c4",ey="linearGradient",ez="startPoint",eA=0.5,eB="endPoint",eC="stops",eD="offset",eE=0xFFF2F2F2,eF=0xFFE4E4E4,eG="paragraph",eH="e8d4f4edf91c43ceb65c5cbc5a977efd",eI="\"PingFang SC\", sans-serif",eJ="_形状",eK="056311d3cbfc4e2a9ace87a100441879",eL="\"Noto Sans CJK\", sans-serif",eM=10,eN=0.3137254901960784,eO="box_2",eP="********************************",eQ="box_1",eR="********************************",eS="label",eT="a75e7bb832094f68aee40a308a0ac86c",eU="\"苹方\", sans-serif",eV="22px",eW="droplist",eX="4fe1008cfe9148dabb360ae2526a12a3",eY="form_disabled1",eZ="78779f71cd194342b500561ae85e4884",fa="box_3",fb="********************************",fc=0xFFD7D7D7,fd="_列表字段1",fe="216d271e409f4afc973f8efd888bf7da",ff="56px",fg="box_11",fh="********************************",fi="text_area1",fj="f15e776f8ef74d28b46974ae86440549",fk="text_field1",fl="6299480621684230b98617cb697f785f",fm="form_hint1",fn="936842456cf944d2af0f336d43a4d022",fo="checkbox",fp="********************************",fq="shape",fr="d64951247d18402b9c63ab2a91a1e08a",fs="primary_button",ft="ce71cf6dda474ea9a8b9f5d31e11fdf0",fu=0xFF1E98D7,fv="radio_button",fw="88215c69e7a84c4fb2b25339a75012e5",fx="duplicateStyles";
return _creator();
})());