# Pages 目录结构说明

## 目录组织

```
pages/
├── common/              # 公共配置模块
│   ├── site.json       # 站点配置文件（菜单、路由）
│   └── jsonp.js        # 公共脚本文件
├── project/            # 项目管理模块
│   ├── project-list.json      # 项目列表页面配置
│   ├── project-data.json      # 项目数据源
│   ├── project-final.json     # 项目页面最终版本
│   ├── project-simple.json    # 项目页面简化版本
│   └── project-test.json      # 项目页面测试版本
├── task/               # 任务管理模块
│   ├── task-list.json         # 任务列表页面配置
│   └── task-data.json         # 任务数据源
├── vulnerability/      # 漏洞管理模块
│   ├── vulnerability-list.json   # 漏洞列表页面配置
│   └── vulnerability-data.json   # 漏洞数据源
├── report/             # 报告管理模块
│   ├── report-list.json       # 报告列表页面配置
│   └── report-data.json       # 报告数据源
├── model/              # 模型配置模块
│   ├── model-config.json      # 模型配置页面
│   └── model-config-data.json # 模型配置数据源
└── test/               # 测试文件
    └── view-test.json         # 视图测试页面
```

## 模块说明

### common/ - 公共配置模块
- **site.json**: 应用主配置文件，定义菜单结构和路由
- **jsonp.js**: 公共JavaScript脚本

### project/ - 项目管理模块
- 项目列表展示（表格/卡片视图）
- 项目数据管理
- 项目操作功能

### task/ - 任务管理模块
- 任务列表管理
- 任务状态跟踪

### vulnerability/ - 漏洞管理模块
- 漏洞信息展示
- 漏洞分类管理

### report/ - 报告管理模块
- 报告生成和展示
- 报告数据统计

### model/ - 模型配置模块
- AI模型参数配置
- 模型性能设置

### test/ - 测试模块
- 功能测试页面
- 开发调试文件

## 路径更新说明

重构后，所有文件路径都已更新：
- 站点配置: `/pages/common/site.json`
- 项目管理: `/pages/project/project-list.json`
- 数据源: `/pages/project/project-data.json`
- 其他模块类似

## 开发规范

1. **文件命名**: 使用模块前缀 + 功能描述
2. **数据文件**: 以 `-data.json` 结尾
3. **页面配置**: 以 `-list.json` 或功能名结尾
4. **测试文件**: 放在 `test/` 目录下
