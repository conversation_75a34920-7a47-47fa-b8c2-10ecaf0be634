{"status": 0, "msg": "success", "data": {"items": [{"id": "01", "modelName": "GPT-4 通用模型", "supplier": "OpenAI", "modelId": "gpt-4-turbo", "weight": 30, "testStatus": "tested", "updateTime": "2024-03-12 12:00:00"}, {"id": "02", "modelName": "文心一言安全版", "supplier": "百度", "modelId": "ERNIE-Bot-Security", "weight": 20, "testStatus": "untested", "updateTime": "2024-03-12 12:00:00"}, {"id": "03", "modelName": "Claude 3", "supplier": "Anthropic", "modelId": "claude-3-opus", "weight": 50, "testStatus": "tested", "updateTime": "2024-03-12 12:00:00"}, {"id": "04", "modelName": "通义千问", "supplier": "阿里云", "modelId": "qwen-max", "weight": 25, "testStatus": "testing", "updateTime": "2024-03-12 11:30:00"}, {"id": "05", "modelName": "GPT-3.5 Turbo", "supplier": "OpenAI", "modelId": "gpt-3.5-turbo", "weight": 15, "testStatus": "tested", "updateTime": "2024-03-12 10:45:00"}], "total": 5, "page": 1, "perPage": 10}}