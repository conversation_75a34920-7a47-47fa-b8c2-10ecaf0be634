{"type": "page", "title": "报告管理", "remark": null, "name": "report-list-page", "body": [{"type": "container", "className": "bg-white p-3 m-b-sm", "body": [{"type": "flex", "justify": "space-between", "items": [{"type": "container", "body": [{"type": "input-text", "name": "keywords", "placeholder": "请输入报告名称", "clearable": true, "className": "w-md", "addOn": {"type": "button", "icon": "fa fa-search", "level": "light"}}]}, {"type": "container", "body": [{"type": "select", "name": "reportType", "placeholder": "选择报告类型", "clearable": true, "options": [{"label": "项目报告", "value": "project"}, {"label": "漏洞报告", "value": "vulnerability"}, {"label": "统计报告", "value": "statistics"}, {"label": "合规报告", "value": "compliance"}]}]}, {"type": "button-group", "buttons": [{"type": "button", "label": "生成报告", "level": "primary", "icon": "fa fa-plus", "actionType": "dialog", "dialog": {"title": "生成新报告", "size": "lg", "body": {"type": "form", "api": "post:/api/reports/generate", "body": [{"type": "input-text", "name": "reportName", "label": "报告名称", "required": true, "placeholder": "请输入报告名称"}, {"type": "select", "name": "reportType", "label": "报告类型", "required": true, "options": [{"label": "项目报告", "value": "project"}, {"label": "漏洞报告", "value": "vulnerability"}, {"label": "统计报告", "value": "statistics"}, {"label": "合规报告", "value": "compliance"}]}, {"type": "select", "name": "projectId", "label": "关联项目", "source": "/api/projects/list", "labelField": "projectName", "valueField": "id", "visibleOn": "${reportType === 'project' || reportType === 'vulnerability'}"}, {"type": "input-date-range", "name": "date<PERSON><PERSON><PERSON>", "label": "时间范围", "required": true}, {"type": "checkboxes", "name": "includeItems", "label": "包含内容", "options": [{"label": "漏洞详情", "value": "vulnerabilities"}, {"label": "修复建议", "value": "solutions"}, {"label": "风险评估", "value": "risk_assessment"}, {"label": "统计图表", "value": "charts"}]}]}}}]}]}]}, {"type": "crud", "name": "reportList", "api": "/pages/report/report-data.json", "syncLocation": false, "columns": [{"name": "id", "label": "序号", "width": 80, "type": "text"}, {"name": "reportName", "label": "报告名称", "sortable": true, "searchable": true}, {"name": "reportType", "label": "报告类型", "type": "mapping", "map": {"project": "<span class='label label-primary'>项目报告</span>", "vulnerability": "<span class='label label-danger'>漏洞报告</span>", "statistics": "<span class='label label-info'>统计报告</span>", "compliance": "<span class='label label-success'>合规报告</span>"}}, {"name": "projectName", "label": "关联项目"}, {"name": "status", "label": "生成状态", "type": "mapping", "map": {"generating": "<span class='label label-warning'>生成中</span>", "completed": "<span class='label label-success'>已完成</span>", "failed": "<span class='label label-danger'>生成失败</span>"}}, {"name": "fileSize", "label": "文件大小", "type": "tpl", "tpl": "${fileSize} MB"}, {"name": "createTime", "label": "生成时间", "type": "datetime", "format": "YYYY-MM-DD HH:mm:ss"}, {"type": "operation", "label": "操作", "width": 200, "buttons": [{"type": "button", "label": "预览", "level": "link", "icon": "fa fa-eye", "actionType": "dialog", "dialog": {"title": "报告预览", "size": "xl", "body": {"type": "iframe", "src": "/api/reports/${id}/preview", "height": 600}}, "visibleOn": "${status === 'completed'}"}, {"type": "button", "label": "下载", "level": "link", "icon": "fa fa-download", "actionType": "url", "url": "/api/reports/${id}/download", "blank": true, "visibleOn": "${status === 'completed'}"}, {"type": "button", "label": "重新生成", "level": "link", "icon": "fa fa-refresh", "actionType": "ajax", "api": "post:/api/reports/${id}/regenerate", "confirmText": "确定要重新生成这个报告吗？", "visibleOn": "${status === 'failed'}"}, {"type": "button", "label": "分享", "level": "link", "icon": "fa fa-share", "actionType": "dialog", "dialog": {"title": "分享报告", "body": {"type": "form", "api": "post:/api/reports/${id}/share", "body": [{"type": "input-email", "name": "email", "label": "接收邮箱", "required": true, "multiple": true, "placeholder": "请输入邮箱地址，多个邮箱用逗号分隔"}, {"type": "textarea", "name": "message", "label": "附加消息", "placeholder": "可选的附加消息"}]}}, "visibleOn": "${status === 'completed'}"}, {"type": "button", "label": "删除", "level": "link", "className": "text-danger", "icon": "fa fa-trash", "actionType": "ajax", "api": "delete:/api/reports/${id}", "confirmText": "确定要删除这个报告吗？"}]}], "affixHeader": true, "columnsTogglable": false, "placeholder": "暂无数据", "tableClassName": "table-db table-striped", "headerClassName": "crud-table-header", "footerClassName": "crud-table-footer", "toolbarClassName": "crud-table-toolbar", "bodyClassName": "panel-default", "defaultParams": {"perPage": 10}, "perPageAvailable": [10, 20, 50, 100], "messages": {"fetchFailed": "获取数据失败", "saveOrderFailed": "保存顺序失败", "saveOrderSuccess": "保存顺序成功", "quickSaveSuccess": "保存成功", "quickSaveFailed": "保存失败"}}]}