{"type": "page", "title": "报告管理", "remark": null, "name": "report-list-page", "body": [{"type": "container", "className": "bg-white p-3 m-b-sm", "body": [{"type": "flex", "justify": "space-between", "items": [{"type": "container", "body": [{"type": "input-text", "name": "keyword", "placeholder": "请输入项目名称", "clearable": true, "className": "w-md", "addOn": {"type": "button", "icon": "fa fa-search", "level": "light"}}]}, {"type": "button-group", "buttons": [{"type": "button", "label": "生成报告", "level": "primary", "icon": "fa fa-plus"}, {"type": "button", "label": "导出报告", "level": "default", "icon": "fa fa-download"}]}]}]}, {"type": "crud", "name": "reportList", "api": {"method": "get", "url": "http://localhost:21001/sast/reports/list", "headers": {"Authorization": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiIxIiwicm5TdHIiOiJ2blJ3dmx2M1lQVkQycFJ0a3ZqU2JFcmtNSVRnYVR6MyIsInVzZXJJZCI6IjEiLCJ1c2VyTmFtZSI6ImFkbWluIiwib3JnSWQiOiIxMDAiLCJvcmdOYW1lIjoi6YeN5bqG5biC6L2o6YGT5Lqk6YCa77yI6ZuG5Zui77yJ5pyJ6ZmQ5YWs5Y-4In0.0bueZdo9qQB6U3l-JBzllgdpa6GUxpaK0Zaj__vMZ_0"}, "adaptor": "\nreturn {\n    ...payload,\n    status: payload.code === 200 ? 0 : payload.code\n}"}, "syncLocation": false, "filter": {"title": "", "body": [{"type": "input-text", "name": "keyword", "placeholder": "搜索项目名称、任务名称", "clearable": true, "submitOnChange": true}]}, "headerToolbar": ["filter-toggler", "pagination"], "columns": [{"name": "id", "label": "序号", "width": 80, "type": "text"}, {"name": "reportName", "label": "报告名称", "width": 200, "sortable": false}, {"name": "projectName", "label": "项目名称", "width": 150}, {"name": "taskName", "label": "任务名称", "width": 150}, {"name": "defectCount", "label": "缺陷数量", "width": 100, "type": "text"}, {"name": "vulnerabilityStats", "label": "危险分类", "width": 200, "type": "tpl", "tpl": "<div class='text-xs'><span class='label label-danger m-r-xs'>${vulnerabilityStats.highRisk}</span><span class='label label-warning m-r-xs'>${vulnerabilityStats.mediumRisk}</span><span class='label label-success'>${vulnerabilityStats.lowRisk}</span></div>"}, {"name": "verificationSuccess", "label": "验证成功", "width": 100, "type": "text"}, {"name": "verificationFailure", "label": "验证失败", "width": 100, "type": "text"}, {"name": "reportGenerationTime", "label": "报告生成时间", "width": 160, "type": "datetime", "format": "YYYY-MM-DD HH:mm:ss"}, {"type": "operation", "label": "操作", "width": 120, "buttons": [{"type": "button", "label": "", "icon": "fa fa-eye", "level": "link", "size": "sm", "tooltip": "查看", "actionType": "dialog", "dialog": {"title": "报告详情", "size": "lg", "body": {"type": "form", "mode": "horizontal", "disabled": true, "body": [{"type": "input-text", "name": "reportName", "label": "报告名称"}, {"type": "input-text", "name": "projectName", "label": "项目名称"}, {"type": "input-text", "name": "taskName", "label": "任务名称"}, {"type": "input-number", "name": "defectCount", "label": "缺陷数量"}, {"type": "static", "name": "vulnerabilityStats", "label": "危险分类", "tpl": "高危: ${vulnerabilityStats.highRisk} | 中危: ${vulnerabilityStats.mediumRisk} | 低危: ${vulnerabilityStats.lowRisk}"}, {"type": "input-number", "name": "verificationSuccess", "label": "验证成功"}, {"type": "input-number", "name": "verificationFailure", "label": "验证失败"}, {"type": "datetime", "name": "reportGenerationTime", "label": "生成时间", "format": "YYYY-MM-DD HH:mm:ss"}]}}}, {"type": "button", "label": "", "icon": "fa fa-download", "level": "link", "size": "sm", "tooltip": "下载", "actionType": "url", "url": "/api/reports/${id}/download", "blank": true}, {"type": "button", "label": "", "icon": "fa fa-trash", "level": "link", "size": "sm", "tooltip": "删除", "className": "text-danger", "actionType": "ajax", "api": {"method": "delete", "url": "/api/reports/${id}", "headers": {"Authorization": "Bearer ${ls:token}"}}, "confirmText": "确定要删除这个报告吗？"}]}], "affixHeader": true, "columnsTogglable": false, "placeholder": "暂无数据", "tableClassName": "table-db table-striped", "headerClassName": "crud-table-header", "footerClassName": "crud-table-footer", "toolbarClassName": "crud-table-toolbar", "bodyClassName": "panel-default", "defaultParams": {"page": 1, "size": 10}, "pageField": "page", "perPageField": "size", "perPageAvailable": [10, 20, 50, 100], "messages": {"fetchFailed": "获取数据失败", "saveOrderFailed": "保存顺序失败", "saveOrderSuccess": "保存顺序成功", "quickSaveSuccess": "保存成功", "quickSaveFailed": "保存失败"}}]}