{"status": 0, "msg": "", "data": {"pages": [{"label": "Home", "url": "/", "redirect": "/dashboard"}, {"children": [{"label": "首页", "url": "/dashboard", "icon": "fa fa-home", "schema": {"type": "page", "title": "大模型漏洞挖掘平台", "body": {"type": "tpl", "tpl": "<div class='text-center p-5'><h2>欢迎使用大模型漏洞挖掘平台</h2><p>请从左侧菜单选择功能模块</p></div>"}}}, {"label": "项目管理", "url": "/project", "icon": "fa fa-folder-open", "schemaApi": "get:/pages/project/project-list.json"}, {"label": "缺陷管理", "url": "/vulnerability", "icon": "fa fa-bug", "schemaApi": "get:/pages/vulnerability/vul-management.json"}, {"label": "POC管理", "url": "/poc", "icon": "fa fa-code", "schema": {"type": "page", "title": "POC管理", "body": "POC管理功能开发中..."}}, {"label": "模型配置", "url": "/model-config", "icon": "fa fa-cogs", "schemaApi": "get:/pages/model/model-config.json"}, {"label": "报告管理", "url": "/report", "icon": "fa fa-file-text", "schemaApi": "get:/pages/report/report-list.json"}, {"label": "系统管理", "url": "/system", "icon": "fa fa-cog", "schema": {"type": "page", "title": "系统管理", "body": "系统管理功能开发中..."}}]}]}}