{"type": "page", "title": "模型配置", "remark": null, "name": "model-config-page", "body": [{"type": "container", "className": "bg-white p-3 m-b-sm", "body": [{"type": "flex", "justify": "space-between", "items": [{"type": "container", "body": [{"type": "input-text", "name": "keywords", "placeholder": "请输入项目名称", "clearable": true, "className": "w-md", "addOn": {"type": "button", "icon": "fa fa-search", "level": "light"}}]}, {"type": "button-group", "buttons": [{"type": "button", "label": "新增配置", "level": "primary", "icon": "fa fa-plus", "actionType": "dialog", "dialog": {"title": "新增模型配置", "size": "lg", "body": {"type": "form", "api": "post:/api/model-config", "body": [{"type": "input-text", "name": "modelName", "label": "模型名称", "required": true, "placeholder": "请输入模型名称"}, {"type": "select", "name": "supplier", "label": "供应商", "required": true, "options": [{"label": "OpenAI", "value": "OpenAI"}, {"label": "百度", "value": "百度"}, {"label": "Anthropic", "value": "Anthropic"}, {"label": "阿里云", "value": "阿里云"}]}, {"type": "input-text", "name": "modelId", "label": "模型标识", "required": true, "placeholder": "请输入模型标识"}, {"type": "input-range", "name": "weight", "label": "权重设置", "min": 0, "max": 100, "step": 10, "value": 50, "showInput": true, "unit": "%"}]}}}, {"type": "button", "label": "权重设置", "level": "default", "icon": "fa fa-sliders", "actionType": "dialog", "dialog": {"title": "批量权重设置", "size": "md", "body": {"type": "form", "api": "post:/api/model-config/batch-weight", "body": [{"type": "static", "label": "说明", "value": "调整所有模型的权重分配，总权重为100%"}, {"type": "input-range", "name": "gpt4Weight", "label": "GPT-4 权重", "min": 0, "max": 100, "step": 5, "value": 30, "showInput": true, "unit": "%"}, {"type": "input-range", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "文心一言 权重", "min": 0, "max": 100, "step": 5, "value": 20, "showInput": true, "unit": "%"}, {"type": "input-range", "name": "clau<PERSON><PERSON><PERSON><PERSON>", "label": "Claude 3 权重", "min": 0, "max": 100, "step": 5, "value": 50, "showInput": true, "unit": "%"}]}}}, {"type": "button", "label": "模型检测", "level": "info", "icon": "fa fa-check-circle", "actionType": "ajax", "api": "post:/api/model-config/test-all", "confirmText": "确定要检测所有模型的连通性吗？"}]}]}]}, {"type": "crud", "name": "modelConfigList", "api": "/pages/model/model-config-data.json", "syncLocation": false, "columns": [{"name": "id", "label": "序号", "width": 80, "type": "text"}, {"name": "modelName", "label": "模型名称", "sortable": true, "searchable": true}, {"name": "supplier", "label": "供应商", "type": "mapping", "map": {"OpenAI": "<span class='label label-info'>OpenAI</span>", "百度": "<span class='label label-success'>百度</span>", "Anthropic": "<span class='label label-primary'>Anthropic</span>", "阿里云": "<span class='label label-warning'>阿里云</span>"}}, {"name": "modelId", "label": "模型标识", "type": "text"}, {"name": "weight", "label": "权重设置", "type": "tpl", "tpl": "<div class='progress' style='margin-bottom: 0;'><div class='progress-bar progress-bar-info' style='width: ${weight}%;'>${weight}%</div></div>"}, {"name": "testStatus", "label": "模型测试状态", "type": "mapping", "map": {"tested": "<span class='label label-success'>已测试</span>", "untested": "<span class='label label-danger'>未测试</span>", "testing": "<span class='label label-info'>测试中</span>"}}, {"name": "updateTime", "label": "更新时间", "type": "datetime", "format": "YYYY-MM-DD HH:mm:ss"}, {"type": "operation", "label": "操作", "width": 150, "buttons": [{"type": "button", "label": "编辑", "level": "link", "icon": "fa fa-edit", "actionType": "dialog", "dialog": {"title": "编辑模型配置", "size": "lg", "body": {"type": "form", "api": "put:/api/model-config/${id}", "initApi": "get:/api/model-config/${id}", "body": [{"type": "input-text", "name": "modelName", "label": "模型名称", "required": true}, {"type": "select", "name": "supplier", "label": "供应商", "required": true, "options": [{"label": "OpenAI", "value": "OpenAI"}, {"label": "百度", "value": "百度"}, {"label": "Anthropic", "value": "Anthropic"}, {"label": "阿里云", "value": "阿里云"}]}, {"type": "input-text", "name": "modelId", "label": "模型标识", "required": true}, {"type": "input-range", "name": "weight", "label": "权重设置", "min": 0, "max": 100, "step": 10, "showInput": true, "unit": "%"}]}}}, {"type": "button", "label": "删除", "level": "link", "className": "text-danger", "icon": "fa fa-trash", "actionType": "ajax", "api": "delete:/api/model-config/${id}", "confirmText": "确定要删除这个模型配置吗？"}]}], "affixHeader": true, "columnsTogglable": false, "placeholder": "暂无数据", "tableClassName": "table-db table-striped", "headerClassName": "crud-table-header", "footerClassName": "crud-table-footer", "toolbarClassName": "crud-table-toolbar", "bodyClassName": "panel-default", "defaultParams": {"perPage": 10}, "perPageAvailable": [10, 20, 50, 100], "messages": {"fetchFailed": "获取数据失败", "saveOrderFailed": "保存顺序失败", "saveOrderSuccess": "保存顺序成功", "quickSaveSuccess": "保存成功", "quickSaveFailed": "保存失败"}}]}