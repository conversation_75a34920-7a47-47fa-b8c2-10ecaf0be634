{"type": "page", "title": "项目管理", "remark": null, "name": "project-list-page", "css": {"cssText": "@import url('/static/css/project-cards.css');"}, "initApi": "/pages/project-data.json", "data": {"viewMode": "table"}, "body": [{"type": "container", "className": "bg-white p-3 m-b-sm", "body": [{"type": "flex", "justify": "space-between", "items": [{"type": "flex", "items": [{"type": "input-text", "name": "keywords", "placeholder": "请输入项目名称", "clearable": true, "className": "w-md m-r-sm"}, {"type": "button-group", "buttons": [{"type": "button", "icon": "fa fa-list", "level": "${viewMode === 'table' ? 'primary' : 'default'}", "actionType": "setValue", "args": {"value": {"viewMode": "table"}}, "tooltip": "列表视图"}, {"type": "button", "icon": "fa fa-th-large", "level": "${viewMode === 'card' ? 'primary' : 'default'}", "actionType": "setValue", "args": {"value": {"viewMode": "card"}}, "tooltip": "卡片视图"}]}]}, {"type": "button", "label": "新建项目", "level": "primary", "icon": "fa fa-plus"}]}]}, {"type": "container", "visibleOn": "${viewMode === 'table'}", "body": [{"type": "table", "source": "${items}", "columns": [{"name": "id", "label": "序号", "width": 80}, {"name": "projectName", "label": "项目名称", "width": 150}, {"name": "description", "label": "项目描述", "width": 200}, {"name": "completedTasks", "label": "任务数", "type": "tpl", "tpl": "${completedTasks || 5}/${totalTasks || 8}", "width": 100}, {"name": "vulnerabilityCount", "label": "缺陷总数", "width": 100}, {"name": "priority", "label": "严重程度", "type": "mapping", "width": 120, "map": {"high": "<span class='label label-danger'>高</span>", "medium": "<span class='label label-warning'>中</span>", "low": "<span class='label label-info'>低</span>"}}, {"name": "updateTime", "label": "创建时间", "type": "datetime", "format": "YYYY-MM-DD HH:mm:ss", "width": 160}, {"type": "operation", "label": "操作", "width": 200, "buttons": [{"type": "button", "label": "查看任务", "level": "link", "icon": "fa fa-tasks"}, {"type": "button", "label": "编辑", "level": "link", "icon": "fa fa-edit"}, {"type": "button", "label": "删除", "level": "link", "className": "text-danger", "icon": "fa fa-trash"}]}]}]}, {"type": "container", "visibleOn": "${viewMode === 'card'}", "body": [{"type": "cards", "source": "${items}", "columnsCount": 3, "card": {"type": "card", "className": "project-card m-b-md", "style": {"border": "1px solid #e8e8e8", "borderRadius": "8px", "padding": "20px", "minHeight": "240px", "position": "relative", "backgroundColor": "#fff", "boxShadow": "0 2px 8px #00000020", "transition": "all 0.3s ease"}, "body": [{"type": "flex", "justify": "space-between", "align": "center", "className": "m-b-sm", "items": [{"type": "tpl", "tpl": "${projectName}", "style": {"fontSize": "16px", "fontWeight": "600", "color": "#333"}}, {"type": "dropdown-button", "level": "link", "icon": "fa fa-ellipsis-v", "hideCaret": true, "size": "sm", "buttons": [{"type": "button", "label": "查看任务", "icon": "fa fa-tasks"}, {"type": "button", "label": "编辑", "icon": "fa fa-edit"}, {"type": "button", "label": "删除", "className": "text-danger", "icon": "fa fa-trash"}]}]}, {"type": "tpl", "tpl": "${description}", "className": "m-b-sm", "style": {"color": "#666", "fontSize": "14px", "lineHeight": "1.5", "minHeight": "42px", "overflow": "hidden", "textOverflow": "ellipsis", "display": "-webkit-box", "-webkit-line-clamp": "2", "-webkit-box-orient": "vertical"}}, {"type": "flex", "justify": "space-between", "className": "m-b-sm", "items": [{"type": "flex", "align": "center", "items": [{"type": "icon", "icon": "fa fa-tasks", "style": {"color": "#1890ff", "marginRight": "8px"}}, {"type": "container", "body": [{"type": "tpl", "tpl": "任务进度", "style": {"fontSize": "12px", "color": "#999"}}, {"type": "tpl", "tpl": "${completedTasks || 5}/${totalTasks || 8}", "style": {"fontSize": "16px", "fontWeight": "600", "color": "#333"}}]}]}, {"type": "flex", "align": "center", "items": [{"type": "icon", "icon": "fa fa-bug", "style": {"color": "#ff4d4f", "marginRight": "8px"}}, {"type": "container", "body": [{"type": "tpl", "tpl": "缺陷总数", "style": {"fontSize": "12px", "color": "#999"}}, {"type": "tpl", "tpl": "${vulnerabilityCount || 1243}", "style": {"fontSize": "16px", "fontWeight": "600", "color": "#333"}}]}]}]}, {"type": "flex", "justify": "space-between", "align": "center", "style": {"borderTop": "1px solid #f0f0f0", "paddingTop": "12px", "marginTop": "auto"}, "items": [{"type": "tpl", "tpl": "${updateTime}", "style": {"fontSize": "12px", "color": "#999"}}, {"type": "tpl", "tpl": "${priority === 'high' ? '高' : priority === 'medium' ? '中' : '低'}", "className": "${priority === 'high' ? 'label label-danger' : priority === 'medium' ? 'label label-warning' : 'label label-info'}", "style": {"fontSize": "12px"}}]}]}}]}]}