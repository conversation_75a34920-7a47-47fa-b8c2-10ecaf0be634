<!DOCTYPE html>
<!-- saved from url=(0036)http://***********/VBQ4F3/index.html -->
<html zoom="100"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="robots" content="noindex, nofollow">
    <title>index</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    
    <link href="./axure_rp_page.css" type="text/css" rel="stylesheet">
    <link href="./styles.css" type="text/css" rel="stylesheet">
    <link href="./styles(1).css" type="text/css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/" rel="preconnect">
    <link href="https://fonts.gstatic.com/" rel="preconnect">
    <link href="./css2" rel="stylesheet">
    <link rel="icon" href="http://***********/app/favicon.ico"><meta property="og:site_name" content="Axure Cloud">
<meta property="og:type" content="website">
<meta property="og:title" content="大模型漏洞挖掘平台_RP11_B1.0 (1) - index">
<meta property="og:description" content="Created with Axure RP">
<meta property="og:url" content="http://***********/VBQ4F3/index.html">
<meta property="og:image" content="http://***********/gsc/VBQ4F3/thumbnails/iu1cwq.png">
<link rel="alternate" type="application/json+oembed" href="http://localhost/oembed?url=http%3A%2F%2F***********%2FVBQ4F3%2Findex.html" title="oEmbed"><script type="text/javascript">
        AXSHARE_HOST_URL = 'http://***********';
        AXSHARE_HOST_SECURE_URL = 'http://***********';
        ACCOUNT_SERVICE_URL = 'http://***********';
        ACCOUNT_SERVICE_SECURE_URL = 'http://***********';
        ON_PREM_LDAP_ENABLED = 'false';
        AXSHARE_CLIENT_URL = 'http://***********/app/'
</script><script src="./jquery-3.7.1.min.js.下载"></script>
    <script src="./axQuery.js.下载"></script>
    <script src="./globals.js.下载"></script>
    <script src="./axutils.js.下载"></script>
    <script src="./annotation.js.下载"></script>
    <script src="./axQuery.std.js.下载"></script>
    <script src="./doc.js.下载"></script>
    <script src="./messagecenter.js.下载"></script>
    <script src="./events.js.下载"></script>
    <script src="./recording.js.下载"></script>
    <script src="./action.js.下载"></script>
    <script src="./expr.js.下载"></script>
    <script src="./geometry.js.下载"></script>
    <script src="./flyout.js.下载"></script>
    <script src="./model.js.下载"></script>
    <script src="./repeater.js.下载"></script>
    <script src="./sto.js.下载"></script>
    <script src="./utils.temp.js.下载"></script>
    <script src="./variables.js.下载"></script>
    <script src="./drag.js.下载"></script>
    <script src="./move.js.下载"></script>
    <script src="./visibility.js.下载"></script>
    <script src="./style.js.下载"></script>
    <script src="./adaptive.js.下载"></script>
    <script src="./tree.js.下载"></script>
    <script src="./init.temp.js.下载"></script>
    <script src="./legacy.js.下载"></script>
    <script src="./viewer.js.下载"></script>
    <script src="./math.js.下载"></script>
    <script src="./jquery.nicescroll.min.js.下载"></script>
    <script src="./document.js.下载"></script>
    <script src="./data.js.下载"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'http://***********/gsc/VBQ4F3/1c/74/19/1c74195a2a8d4aacad90f09e97e1cc57/resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  <style data-id="immersive-translate-input-injected-css">.immersive-translate-input {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  z-index: 2147483647;
  display: flex;
  justify-content: center;
  align-items: center;
}
.immersive-translate-attach-loading::after {
  content: " ";

  --loading-color: #f78fb6;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  display: block;
  margin: 12px auto;
  position: relative;
  color: white;
  left: -100px;
  box-sizing: border-box;
  animation: immersiveTranslateShadowRolling 1.5s linear infinite;

  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-2000%, -50%);
  z-index: 100;
}

.immersive-translate-loading-spinner {
  vertical-align: middle !important;
  width: 10px !important;
  height: 10px !important;
  display: inline-block !important;
  margin: 0 4px !important;
  border: 2px rgba(221, 244, 255, 0.6) solid !important;
  border-top: 2px rgba(0, 0, 0, 0.375) solid !important;
  border-left: 2px rgba(0, 0, 0, 0.375) solid !important;
  border-radius: 50% !important;
  padding: 0 !important;
  -webkit-animation: immersive-translate-loading-animation 0.6s infinite linear !important;
  animation: immersive-translate-loading-animation 0.6s infinite linear !important;
}

@-webkit-keyframes immersive-translate-loading-animation {
  from {
    -webkit-transform: rotate(0deg);
  }

  to {
    -webkit-transform: rotate(359deg);
  }
}

@keyframes immersive-translate-loading-animation {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(359deg);
  }
}

.immersive-translate-input-loading {
  --loading-color: #f78fb6;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  display: block;
  margin: 12px auto;
  position: relative;
  color: white;
  left: -100px;
  box-sizing: border-box;
  animation: immersiveTranslateShadowRolling 1.5s linear infinite;
}

@keyframes immersiveTranslateShadowRolling {
  0% {
    box-shadow: 0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0),
      0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
  }

  12% {
    box-shadow: 100px 0 var(--loading-color), 0px 0 rgba(255, 255, 255, 0),
      0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
  }

  25% {
    box-shadow: 110px 0 var(--loading-color), 100px 0 var(--loading-color),
      0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
  }

  36% {
    box-shadow: 120px 0 var(--loading-color), 110px 0 var(--loading-color),
      100px 0 var(--loading-color), 0px 0 rgba(255, 255, 255, 0);
  }

  50% {
    box-shadow: 130px 0 var(--loading-color), 120px 0 var(--loading-color),
      110px 0 var(--loading-color), 100px 0 var(--loading-color);
  }

  62% {
    box-shadow: 200px 0 rgba(255, 255, 255, 0), 130px 0 var(--loading-color),
      120px 0 var(--loading-color), 110px 0 var(--loading-color);
  }

  75% {
    box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0),
      130px 0 var(--loading-color), 120px 0 var(--loading-color);
  }

  87% {
    box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0),
      200px 0 rgba(255, 255, 255, 0), 130px 0 var(--loading-color);
  }

  100% {
    box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0),
      200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0);
  }
}

.immersive-translate-toast {
  display: flex;
  position: fixed;
  z-index: 2147483647;
  left: 0;
  right: 0;
  top: 1%;
  width: fit-content;
  padding: 12px 20px;
  margin: auto;
  overflow: auto;
  background: #fef6f9;
  box-shadow: 0px 4px 10px 0px rgba(0, 10, 30, 0.06);
  font-size: 15px;
  border-radius: 8px;
  color: #333;
}

.immersive-translate-toast-content {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.immersive-translate-toast-hidden {
  margin: 0 20px 0 72px;
  text-decoration: underline;
  cursor: pointer;
}

.immersive-translate-toast-close {
  color: #666666;
  font-size: 20px;
  font-weight: bold;
  padding: 0 10px;
  cursor: pointer;
}

@media screen and (max-width: 768px) {
  .immersive-translate-toast {
    top: 0;
    padding: 12px 0px 0 10px;
  }
  .immersive-translate-toast-content {
    flex-direction: column;
    text-align: center;
  }
  .immersive-translate-toast-hidden {
    margin: 10px auto;
  }
}

.immersive-translate-modal {
  display: none;
  position: fixed;
  z-index: 2147483647;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgb(0, 0, 0);
  background-color: rgba(0, 0, 0, 0.4);
  font-size: 15px;
}

.immersive-translate-modal-content {
  background-color: #fefefe;
  margin: 10% auto;
  padding: 40px 24px 24px;
  border-radius: 12px;
  width: 350px;
  font-family: system-ui, -apple-system, "Segoe UI", "Roboto", "Ubuntu",
    "Cantarell", "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
    "Segoe UI Symbol", "Noto Color Emoji";
  position: relative;
}

@media screen and (max-width: 768px) {
  .immersive-translate-modal-content {
    margin: 50% auto !important;
  }
}

.immersive-translate-modal .immersive-translate-modal-content-in-input {
  max-width: 500px;
}
.immersive-translate-modal-content-in-input .immersive-translate-modal-body {
  text-align: left;
  max-height: unset;
}

.immersive-translate-modal-title {
  text-align: center;
  font-size: 16px;
  font-weight: 700;
  color: #333333;
}

.immersive-translate-modal-body {
  text-align: center;
  font-size: 14px;
  font-weight: 400;
  color: #333333;
  margin-top: 24px;
}

@media screen and (max-width: 768px) {
  .immersive-translate-modal-body {
    max-height: 250px;
    overflow-y: auto;
  }
}

.immersive-translate-close {
  color: #666666;
  position: absolute;
  right: 16px;
  top: 16px;
  font-size: 20px;
  font-weight: bold;
}

.immersive-translate-close:hover,
.immersive-translate-close:focus {
  text-decoration: none;
  cursor: pointer;
}

.immersive-translate-modal-footer {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 24px;
}

.immersive-translate-btn {
  width: fit-content;
  color: #fff;
  background-color: #ea4c89;
  border: none;
  font-size: 14px;
  margin: 0 8px;
  padding: 9px 30px;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.immersive-translate-btn-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.immersive-translate-btn:hover {
  background-color: #f082ac;
}
.immersive-translate-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
.immersive-translate-btn:disabled:hover {
  background-color: #ea4c89;
}

.immersive-translate-link-btn {
  background-color: transparent;
  color: #ea4c89;
  border: none;
  cursor: pointer;
  height: 30px;
  line-height: 30px;
}

.immersive-translate-cancel-btn {
  /* gray color */
  background-color: rgb(89, 107, 120);
}

.immersive-translate-cancel-btn:hover {
  background-color: hsl(205, 20%, 32%);
}

.immersive-translate-action-btn {
  background-color: transparent;
  color: #ea4c89;
  border: 1px solid #ea4c89;
}

.immersive-translate-btn svg {
  margin-right: 5px;
}

.immersive-translate-link {
  cursor: pointer;
  user-select: none;
  -webkit-user-drag: none;
  text-decoration: none;
  color: #ea4c89;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
}

.immersive-translate-primary-link {
  cursor: pointer;
  user-select: none;
  -webkit-user-drag: none;
  text-decoration: none;
  color: #ea4c89;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
}

.immersive-translate-modal input[type="radio"] {
  margin: 0 6px;
  cursor: pointer;
}

.immersive-translate-modal label {
  cursor: pointer;
}

.immersive-translate-close-action {
  position: absolute;
  top: 2px;
  right: 0px;
  cursor: pointer;
}

.imt-image-status {
  background-color: rgba(0, 0, 0, 0.5) !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 16px !important;
}
.imt-image-status img,
.imt-image-status svg,
.imt-img-loading {
  width: 28px !important;
  height: 28px !important;
  margin: 0 0 8px 0 !important;
  min-height: 28px !important;
  min-width: 28px !important;
  position: relative !important;
}
.imt-img-loading {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADgAAAA4CAMAAACfWMssAAAAtFBMVEUAAAD////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////oK74hAAAAPHRSTlMABBMIDyQXHwyBfFdDMSw+OjXCb+5RG51IvV/k0rOqlGRM6KKMhdvNyZBz9MaupmxpWyj437iYd/yJVNZeuUC7AAACt0lEQVRIx53T2XKiUBCA4QYOiyCbiAsuuGBcYtxiYtT3f6/pbqoYHVFO5r+iivpo6DpAWYpqeoFfr9f90DsYAuRSWkFnPO50OgR9PwiCUFcl2GEcx+N/YBh6pvKaefHlUgZd1zVe0NbYcQjGBfzrPE8Xz8aF+71D8gG6DHFPpc4a7xFiCDuhaWgKgGIJQ3d5IMGDrpS4S5KgpIm+en9f6PlAhKby4JwEIxlYJV9h5k5nee9GoxHJ2IDSNB0dwdad1NAxDJ/uXDHYmebdk4PdbkS58CIVHdYSUHTYYRWOJblWSyu2lmy3KNFVJNBhxcuGW4YBVCbYGRZwIooipHsNqjM4FbgOQqQqSKQQU9V8xmi1QlgHqQQ6DDBvRUVCDirs+EzGDGOQTCATgtYTnbCVLgsVgRE0T1QE0qHCFAht2z6dLvJQs3Lo2FQoDxWNUiBhaP4eRgwNkI+dAjVOA/kUrIDwf3CG8NfNOE0eiFotSuo+rBiq8tD9oY4Qzc6YJw99hl1wzpQvD7ef2M8QgnOGJfJw+EltQc+oX2yn907QB22WZcvlUpd143dqQu+8pCJZuGE4xCuPXJqqcs5sNpsI93Rmzym1k4Npk+oD1SH3/a3LOK/JpUBpWfqNySxWzCfNCUITuDG5dtuphrUJ1myeIE9bIsPiKrfqTai5WZxbhtNphYx6GEIHihyGFTI69lje/rxajdh0s0msZ0zYxyPLhYCb1CyHm9Qsd2H37Y3lugVwL9kNh8Ot8cha6fUNQ8nuXi5z9/ExsAO4zQrb/ev1yrCB7lGyQzgYDGuxq1toDN/JGvN+HyWNHKB7zEoK+PX11e12G431erGYzwmytAWU56fkMHY5JJnDRR2eZji3AwtIcrEV8Cojat/BdQ7XOwGV1e1hDjGGjXbdArm8uJZtCH5MbcctVX8A1WpqumJHwckAAAAASUVORK5CYII=");
  background-size: 28px 28px;
  animation: image-loading-rotate 1s linear infinite !important;
}

.imt-image-status span {
  color: var(--bg-2, #fff) !important;
  font-size: 14px !important;
  line-height: 14px !important;
  font-weight: 500 !important;
  font-family: "PingFang SC", Arial, sans-serif !important;
}

@keyframes image-loading-rotate {
  from {
    transform: rotate(360deg);
  }
  to {
    transform: rotate(0deg);
  }
}
</style></head>
  <body class="">
    <div id="base" class="">

      <!-- 左侧菜单背景 (Shape) -->
      <div id="u0" class="ax_default box_2 transition" data-label="左侧菜单背景">
        <svg data="http://***********/gsc/VBQ4F3/1c/74/19/1c74195a2a8d4aacad90f09e97e1cc57/images/index/左侧菜单背景_u0.svg" id="u0_img" class="img generatedImage">

  <defs>
    <pattern id="u0_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imagerepeat="None"></pattern>
    <mask fill="white" id="u0_img_cl1">
      <path d="M 243.64123633328006 95.43293345246703  C 242.70537980403122 96.00090736548859  241.05764261747805 96.94861243728934  239.40358796399119 98.38974416926902  C 236.11239731748947 100.7279850945458  234 104.57593836761534  234 109  C 234 112.19692535361371  235.10305777945803 115.09301676737522  237.00920423089514 117.2927460959533  C 237.32317664747154 117.69112101642749  238.15927629735427 118.55470828959616  238.49891136247038 118.90117354183778  C 241.02754669055662 121.24683883347838  243.9016763780768 122.32092078862878  244.6710608198864 122.87357780668471  C 245.483530223211 123.45718321030695  246.0778559495525 123.94879391159832  246.5347336709916 124.46952547258465  C 246.88139448775974 124.86463606734007  247.1489240611649 125.27651205521364  247.37257283322134 125.75806057466909  C 247.37257283322134 125.75806057466909  247.90428243760027 127.08919347501038  248 127.99999999999986  C 248.1721947426139 129.6385299751654  248 142.39999999999998  248 142.39999999999998  L 248 918  L 0 918  L 0 0  L 248 0  L 248 71.75989085948167  C 248 71.75989085948167  248 79.99780824828908  248 86.19999970016303  C 248 86.56574190690175  247.97563013869063 87.3424080380828  247.92895004822367 88.10177961738412  C 247.88189891475918 88.86718716414359  247.4946024719261 90.56871408094393  247.34542114815252 90.90793150678141  C 246.57810729286518 92.65269569184152  245.21008035491002 94.4807976816127  243.64123633328006 95.43293345246703  Z M 238.49891136247038 118.90117354183778  C 238.49891136247038 118.90117354183778  238.66554383854816 119.07115705881111  238.49891136247038 118.90117354183778  Z " fill-rule="evenodd"></path>
    </mask>
  </defs>
  <g>
    <path d="M 243.64123633328006 95.43293345246703  C 242.70537980403122 96.00090736548859  241.05764261747805 96.94861243728934  239.40358796399119 98.38974416926902  C 236.11239731748947 100.7279850945458  234 104.57593836761534  234 109  C 234 112.19692535361371  235.10305777945803 115.09301676737522  237.00920423089514 117.2927460959533  C 237.32317664747154 117.69112101642749  238.15927629735427 118.55470828959616  238.49891136247038 118.90117354183778  C 241.02754669055662 121.24683883347838  243.9016763780768 122.32092078862878  244.6710608198864 122.87357780668471  C 245.483530223211 123.45718321030695  246.0778559495525 123.94879391159832  246.5347336709916 124.46952547258465  C 246.88139448775974 124.86463606734007  247.1489240611649 125.27651205521364  247.37257283322134 125.75806057466909  C 247.37257283322134 125.75806057466909  247.90428243760027 127.08919347501038  248 127.99999999999986  C 248.1721947426139 129.6385299751654  248 142.39999999999998  248 142.39999999999998  L 248 918  L 0 918  L 0 0  L 248 0  L 248 71.75989085948167  C 248 71.75989085948167  248 79.99780824828908  248 86.19999970016303  C 248 86.56574190690175  247.97563013869063 87.3424080380828  247.92895004822367 88.10177961738412  C 247.88189891475918 88.86718716414359  247.4946024719261 90.56871408094393  247.34542114815252 90.90793150678141  C 246.57810729286518 92.65269569184152  245.21008035491002 94.4807976816127  243.64123633328006 95.43293345246703  Z M 238.49891136247038 118.90117354183778  C 238.49891136247038 118.90117354183778  238.66554383854816 119.07115705881111  238.49891136247038 118.90117354183778  Z " fill-rule="nonzero" fill="rgba(30, 30, 30, 1)" stroke="none" class="fill"></path>
    <path d="M 243.64123633328006 95.43293345246703  C 242.70537980403122 96.00090736548859  241.05764261747805 96.94861243728934  239.40358796399119 98.38974416926902  C 236.11239731748947 100.7279850945458  234 104.57593836761534  234 109  C 234 112.19692535361371  235.10305777945803 115.09301676737522  237.00920423089514 117.2927460959533  C 237.32317664747154 117.69112101642749  238.15927629735427 118.55470828959616  238.49891136247038 118.90117354183778  C 241.02754669055662 121.24683883347838  243.9016763780768 122.32092078862878  244.6710608198864 122.87357780668471  C 245.483530223211 123.45718321030695  246.0778559495525 123.94879391159832  246.5347336709916 124.46952547258465  C 246.88139448775974 124.86463606734007  247.1489240611649 125.27651205521364  247.37257283322134 125.75806057466909  C 247.37257283322134 125.75806057466909  247.90428243760027 127.08919347501038  248 127.99999999999986  C 248.1721947426139 129.6385299751654  248 142.39999999999998  248 142.39999999999998  L 248 918  L 0 918  L 0 0  L 248 0  L 248 71.75989085948167  C 248 71.75989085948167  248 79.99780824828908  248 86.19999970016303  C 248 86.56574190690175  247.97563013869063 87.3424080380828  247.92895004822367 88.10177961738412  C 247.88189891475918 88.86718716414359  247.4946024719261 90.56871408094393  247.34542114815252 90.90793150678141  C 246.57810729286518 92.65269569184152  245.21008035491002 94.4807976816127  243.64123633328006 95.43293345246703  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(226, 228, 226, 0)" fill="none" class="stroke" mask="url(#u0_img_cl1)"></path>
    <path d="M 238.49891136247038 118.90117354183778  C 238.49891136247038 118.90117354183778  238.66554383854816 119.07115705881111  238.49891136247038 118.90117354183778  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(226, 228, 226, 0)" fill="none" class="stroke" mask="url(#u0_img_cl1)"></path>
  </g>
        </svg>
        <div id="u0_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (顶部导航) -->
      <div id="u1" class="nopointer  ax_default">
      </div>

      <!-- Unnamed (Logo) -->
      <div id="u2" class="nopointer  ax_default">

        <!-- Unnamed (Group) -->
        <div id="u3" class="ax_default" data-left="46" data-top="25" data-width="187" data-height="38" layer-opacity="1">

          <!-- Unnamed (Rectangle) -->
          <div id="u4" class="ax_default paragraph transition">
            <div id="u4_div" class=""></div>
            <div id="u4_text" class="text ">
              <p><span>大模型漏洞挖掘平台</span></p>
            </div>
          </div>

          <!-- Unnamed (Rectangle) -->
          <div id="u5" class="ax_default paragraph transition">
            <div id="u5_div" class=""></div>
            <div id="u5_text" class="text ">
              <p><span>Large Model Vulnerability Detection Platform</span></p>
            </div>
          </div>

          <!-- Unnamed (Group) -->
          <div id="u6" class="ax_default" data-left="0" data-top="0" data-width="0" data-height="0" layer-opacity="1">
          </div>
        </div>

        <!-- Unnamed (Shape) -->
        <div id="u7" class="ax_default _形状 transition">
          <svg data="http://***********/gsc/VBQ4F3/1c/74/19/1c74195a2a8d4aacad90f09e97e1cc57/images/index/u7.svg" id="u7_img" class="img generatedImage">

  <defs>
    <lineargradient gradientUnits="userSpaceOnUse" x1="6.516516516516516" y1="3.8348348348348376" x2="24.83483483483484" y2="22.45345345345346" id="u7_img_lg2">
      <stop id="Stop3" stop-color="#1777ff" offset="0"></stop>
      <stop id="Stop4" stop-color="#0062f5" offset="1"></stop>
    </lineargradient>
    <pattern id="u7_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imagerepeat="None"></pattern>
    <mask fill="white" id="u7_img_cl5">
      <path d="M 16.23240279328428 19.013700027326138  L 16.231005263046605 19.013700027326138  C 16.527447339318304 18.95002576322219  16.794574484572838 18.789144801882752  16.990589822803013 18.5562298811313  L 21.388375600524405 13.20938509359202  C 21.58449381832957 12.963281510694362  21.691387002195754 12.657127342597548  21.691387002195754 12.34152616894183  C 21.691387002195754 11.575908637892523  21.075317488370768 10.95525299865445  20.31535722971838 10.95525299865445  C 19.924627829112513 10.95525299865445  19.5523218596482 11.122600145939549  19.291327727734078 11.415542196110582  L 15.91177710606445 15.521672496242658  L 12.544643003044913 11.426643211732019  C 12.283194059435706 11.116223929557263  11.89952041382448 10.93727438916845  11.495420301530915 10.93727438916845  C 10.735458089522492 10.93727438916845  10.11938699218957 11.55793162370236  10.11938699218957 12.323551122648814  C 10.11938699218957 12.652089240296919  10.235207199102508 12.969949362753884  10.446197600016916 13.220459033565607  L 14.84398337773831 18.567330896752743  C 15.184163286645841 18.971087715746876  15.722957350878149 19.144307014840948  16.23240279328428 19.013700027326138  Z M 29.696854116975384 8.722002595983058  C 30.891258709718347 10.172065992621942  30.629813052947643 12.263930546067312  29.71061441470016 13.379880448148654  C 28.334584642222783 14.99488869153345  25.82470096210776 17.90330063305552  22.859356802419015 21.337088445598216  L 17.82034652583947 27.177468142278094  C 16.92315898883532 28.21855387803434  16.237912287490484 28.3627317028738  15.63245918760044 28.3627317028738  C 15.055897337816123 28.3627317028738  14.345871350334095 28.221342669763633  13.444571849361413 27.177468142278094  L 8.573426454791509 21.53116668943845  C 5.4388198828554515 17.899130983285517  2.7844530766302973 14.825774195017535  1.563925418675463 13.393743179851528  L 1.4951239300515944 13.302254565742132  C 0.4617309460373848 11.735765883317391  0.48788088683075365 9.980749464863141  1.563925418675463 8.726172245753062  C 2.183138816290281 7.983135241608598  3.5027298676308893 6.448514596711751  4.801734223547323 4.937476841098509  C 5.822737564492938 3.750832422462083  6.829980607713779 2.578050735528531  7.479482785671996 1.8128387757890407  C 8.36425380402605 0.768964248303502  9.166489911612958 0.6483693127476428  9.693493189122897 0.6483693127476428  C 9.690778755392033 0.6483693127476428  14.663755728241563 0.6372682971262008  18.183650636471285 0.6372682971262008  C 21.27971762454538 0.6372682971262008  21.526005453353633 0.6469613790590697  21.605825930389916 0.6497230951405015  C 22.710788587921844 0.6927192239376961  23.280443413262283 1.312372500797012  23.73867207772985 1.8114308421004688  C 24.849117353886488 3.0161076422097737  27.258566985959572 5.853825067176754  28.697888752854603 7.550601767090221  C 29.176730988327833 8.113450334745185  29.54000822337816 8.541787083845698  29.696880992556878 8.722002595983058  Z " fill-rule="evenodd"></path>
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -10 -31 )">
    <path d="M 16.23240279328428 19.013700027326138  L 16.231005263046605 19.013700027326138  C 16.527447339318304 18.95002576322219  16.794574484572838 18.789144801882752  16.990589822803013 18.5562298811313  L 21.388375600524405 13.20938509359202  C 21.58449381832957 12.963281510694362  21.691387002195754 12.657127342597548  21.691387002195754 12.34152616894183  C 21.691387002195754 11.575908637892523  21.075317488370768 10.95525299865445  20.31535722971838 10.95525299865445  C 19.924627829112513 10.95525299865445  19.5523218596482 11.122600145939549  19.291327727734078 11.415542196110582  L 15.91177710606445 15.521672496242658  L 12.544643003044913 11.426643211732019  C 12.283194059435706 11.116223929557263  11.89952041382448 10.93727438916845  11.495420301530915 10.93727438916845  C 10.735458089522492 10.93727438916845  10.11938699218957 11.55793162370236  10.11938699218957 12.323551122648814  C 10.11938699218957 12.652089240296919  10.235207199102508 12.969949362753884  10.446197600016916 13.220459033565607  L 14.84398337773831 18.567330896752743  C 15.184163286645841 18.971087715746876  15.722957350878149 19.144307014840948  16.23240279328428 19.013700027326138  Z M 29.696854116975384 8.722002595983058  C 30.891258709718347 10.172065992621942  30.629813052947643 12.263930546067312  29.71061441470016 13.379880448148654  C 28.334584642222783 14.99488869153345  25.82470096210776 17.90330063305552  22.859356802419015 21.337088445598216  L 17.82034652583947 27.177468142278094  C 16.92315898883532 28.21855387803434  16.237912287490484 28.3627317028738  15.63245918760044 28.3627317028738  C 15.055897337816123 28.3627317028738  14.345871350334095 28.221342669763633  13.444571849361413 27.177468142278094  L 8.573426454791509 21.53116668943845  C 5.4388198828554515 17.899130983285517  2.7844530766302973 14.825774195017535  1.563925418675463 13.393743179851528  L 1.4951239300515944 13.302254565742132  C 0.4617309460373848 11.735765883317391  0.48788088683075365 9.980749464863141  1.563925418675463 8.726172245753062  C 2.183138816290281 7.983135241608598  3.5027298676308893 6.448514596711751  4.801734223547323 4.937476841098509  C 5.822737564492938 3.750832422462083  6.829980607713779 2.578050735528531  7.479482785671996 1.8128387757890407  C 8.36425380402605 0.768964248303502  9.166489911612958 0.6483693127476428  9.693493189122897 0.6483693127476428  C 9.690778755392033 0.6483693127476428  14.663755728241563 0.6372682971262008  18.183650636471285 0.6372682971262008  C 21.27971762454538 0.6372682971262008  21.526005453353633 0.6469613790590697  21.605825930389916 0.6497230951405015  C 22.710788587921844 0.6927192239376961  23.280443413262283 1.312372500797012  23.73867207772985 1.8114308421004688  C 24.849117353886488 3.0161076422097737  27.258566985959572 5.853825067176754  28.697888752854603 7.550601767090221  C 29.176730988327833 8.113450334745185  29.54000822337816 8.541787083845698  29.696880992556878 8.722002595983058  Z " fill-rule="nonzero" fill="url(#u7_img_lg2)" stroke="none" transform="matrix(1 0 0 1 10 31 )" class="fill"></path>
    <path d="M 16.23240279328428 19.013700027326138  L 16.231005263046605 19.013700027326138  C 16.527447339318304 18.95002576322219  16.794574484572838 18.789144801882752  16.990589822803013 18.5562298811313  L 21.388375600524405 13.20938509359202  C 21.58449381832957 12.963281510694362  21.691387002195754 12.657127342597548  21.691387002195754 12.34152616894183  C 21.691387002195754 11.575908637892523  21.075317488370768 10.95525299865445  20.31535722971838 10.95525299865445  C 19.924627829112513 10.95525299865445  19.5523218596482 11.122600145939549  19.291327727734078 11.415542196110582  L 15.91177710606445 15.521672496242658  L 12.544643003044913 11.426643211732019  C 12.283194059435706 11.116223929557263  11.89952041382448 10.93727438916845  11.495420301530915 10.93727438916845  C 10.735458089522492 10.93727438916845  10.11938699218957 11.55793162370236  10.11938699218957 12.323551122648814  C 10.11938699218957 12.652089240296919  10.235207199102508 12.969949362753884  10.446197600016916 13.220459033565607  L 14.84398337773831 18.567330896752743  C 15.184163286645841 18.971087715746876  15.722957350878149 19.144307014840948  16.23240279328428 19.013700027326138  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 10 31 )" class="stroke" mask="url(#u7_img_cl5)"></path>
    <path d="M 29.696854116975384 8.722002595983058  C 30.891258709718347 10.172065992621942  30.629813052947643 12.263930546067312  29.71061441470016 13.379880448148654  C 28.334584642222783 14.99488869153345  25.82470096210776 17.90330063305552  22.859356802419015 21.337088445598216  L 17.82034652583947 27.177468142278094  C 16.92315898883532 28.21855387803434  16.237912287490484 28.3627317028738  15.63245918760044 28.3627317028738  C 15.055897337816123 28.3627317028738  14.345871350334095 28.221342669763633  13.444571849361413 27.177468142278094  L 8.573426454791509 21.53116668943845  C 5.4388198828554515 17.899130983285517  2.7844530766302973 14.825774195017535  1.563925418675463 13.393743179851528  L 1.4951239300515944 13.302254565742132  C 0.4617309460373848 11.735765883317391  0.48788088683075365 9.980749464863141  1.563925418675463 8.726172245753062  C 2.183138816290281 7.983135241608598  3.5027298676308893 6.448514596711751  4.801734223547323 4.937476841098509  C 5.822737564492938 3.750832422462083  6.829980607713779 2.578050735528531  7.479482785671996 1.8128387757890407  C 8.36425380402605 0.768964248303502  9.166489911612958 0.6483693127476428  9.693493189122897 0.6483693127476428  C 9.690778755392033 0.6483693127476428  14.663755728241563 0.6372682971262008  18.183650636471285 0.6372682971262008  C 21.27971762454538 0.6372682971262008  21.526005453353633 0.6469613790590697  21.605825930389916 0.6497230951405015  C 22.710788587921844 0.6927192239376961  23.280443413262283 1.312372500797012  23.73867207772985 1.8114308421004688  C 24.849117353886488 3.0161076422097737  27.258566985959572 5.853825067176754  28.697888752854603 7.550601767090221  C 29.176730988327833 8.113450334745185  29.54000822337816 8.541787083845698  29.696880992556878 8.722002595983058  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 10 31 )" class="stroke" mask="url(#u7_img_cl5)"></path>
  </g>
          </svg>
          <div id="u7_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (Group) -->
      <div id="u8" class="ax_default" data-left="0" data-top="0" data-width="0" data-height="0" layer-opacity="1">
      </div>

      <!-- Unnamed (Group) -->
      <div id="u9" class="ax_default" data-left="14" data-top="145" data-width="220" data-height="40" layer-opacity="1" style="cursor: pointer;">

        <!-- Unnamed (Rectangle) -->
        <div id="u10" class="ax_default box_1 transition" selectiongroup="菜单">
          <div id="u10_div" class="" tabindex="0"></div>
          <div id="u10_text" class="text ">
            <p id="cache0" style=""><span id="cache1" style="">项目管理</span></p>
          </div>
        </div>

        <!-- Unnamed (Rectangle) -->
        <div id="u11" class="ax_default _形状 transition" selectiongroup="二级菜单">
          <div id="u11_div" class=""></div>
          <div id="u11_text" class="text " style="display:none; visibility: hidden">
            <p id="cache2" style=""></p>
          </div>
        </div>

        <!-- Unnamed (Group) -->
        <div id="u12" class="ax_default" data-left="31" data-top="157" data-width="15" data-height="16" layer-opacity="1">

          <!-- Unnamed (Shape) -->
          <div id="u13" class="ax_default _形状 selected transition">
            <svg data="http://***********/gsc/VBQ4F3/1c/74/19/1c74195a2a8d4aacad90f09e97e1cc57/images/index/u13.svg" id="u13_img" class="img generatedImage">

  <defs>
    <pattern id="u13_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imagerepeat="None"></pattern>
    <mask fill="white" id="u13_img_cl6">
      <path d="M 13.659375000000004 12.435227272727264  C 14.207812499999996 12.128409090909082  14.548828125 11.555681818181814  14.548828125 10.94204545454545  L 14.548828125 5.0409090909090954  C 14.548828125 4.427272727272731  14.207812499999996 3.854545454545463  13.659375000000004 3.547727272727281  L 8.389453124999996 0.5971590909090826  C 7.841015625000004 0.2903409090909008  7.158984375000001 0.2903409090909008  6.6123046875 0.5971590909090826  L 1.340625000000001 3.547727272727281  C 0.7921874999999989 3.854545454545463  0.451171875 4.427272727272731  0.451171875 5.0409090909090954  L 0.451171875 10.94204545454545  C 0.451171875 11.555681818181814  0.7921874999999989 12.128409090909082  1.340625000000001 12.435227272727264  L 6.610546874999999 15.385795454545462  C 6.884765625 15.539204545454554  7.1923828125 15.6159090909091  7.5 15.6159090909091  C 7.8076171875 15.6159090909091  8.115234375 15.539204545454554  8.389453124999996 15.385795454545462  L 13.659375000000004 12.435227272727264  Z M 7.774218749999996 1.6301136363636446  L 13.044140625000004 4.580681818181822  C 13.212890625 4.674431818181822  13.318359375 4.851704545454553  13.318359375 5.0409090909090954  L 13.318359375 10.94204545454545  C 13.318359375 11.131249999999993  13.212890625 11.306818181818182  13.044140625000004 11.402272727272724  L 7.774218749999996 14.3528409090909  C 7.605468750000001 14.4465909090909  7.396289062500001 14.4465909090909  7.2275390625 14.3528409090909  L 1.955859375000001 11.402272727272724  C 1.787109375 11.308522727272724  1.681640625 11.131249999999993  1.681640625 10.94204545454545  L 1.681640625 5.0409090909090954  C 1.681640625 4.851704545454553  1.787109375 4.676136363636363  1.955859375000001 4.580681818181822  L 7.225781249999999 1.6301136363636446  C 7.310156250000002 1.5823863636363635  7.405078125000001 1.5585227272727231  7.5 1.5585227272727231  C 7.594921875000003 1.5585227272727231  7.689843749999997 1.5823863636363635  7.774218749999996 1.6301136363636446  Z M 9.270117187500002 11.005113636363644  C 9.605859374999998 11.005113636363644  9.906445312500004 10.7409090909091  9.906445312500004 10.413636363636359  L 9.906445312500004 8.134659090909095  C 9.906445312500004 7.8090909090909175  9.633984375000004 7.544886363636372  9.296484375000002 7.544886363636372  C 8.960742187499996 7.544886363636372  8.66015625 7.8090909090909175  8.66015625 8.136363636363637  L 8.66015625 10.413636363636359  C 8.66015625 10.739204545454538  8.9326171875 11.005113636363644  9.270117187500002 11.005113636363644  Z M 7.471874999999999 11.003409090909082  C 7.8076171875 11.003409090909082  8.108203124999996 10.739204545454538  8.108203124999996 10.411931818181818  L 8.108203124999996 6.803409090909099  C 8.108203124999996 6.47613636363636  7.833984374999999 6.211931818181814  7.498242187499999 6.211931818181814  C 7.162499999999998 6.211931818181814  6.861914062500002 6.47613636363636  6.861914062500002 6.803409090909099  L 6.861914062500002 10.411931818181818  C 6.861914062500002 10.737499999999995  7.134375000000002 11.003409090909082  7.471874999999999 11.003409090909082  Z M 5.671875 11.003409090909082  C 6.007617187500001 11.003409090909082  6.308203125000002 10.739204545454538  6.308203125000002 10.411931818181818  L 6.308203125000002 9.511931818181809  C 6.308203125000002 9.184659090909092  6.035742187500002 8.920454545454545  5.6982421875 8.920454545454545  C 5.362499999999999 8.920454545454545  5.061914062499998 9.184659090909092  5.061914062499998 9.511931818181809  L 5.061914062499998 10.411931818181818  C 5.061914062499998 10.737499999999995  5.334374999999998 11.003409090909082  5.671875 11.003409090909082  Z " fill-rule="evenodd"></path>
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -31 -157 )">
    <path d="M 13.659375000000004 12.435227272727264  C 14.207812499999996 12.128409090909082  14.548828125 11.555681818181814  14.548828125 10.94204545454545  L 14.548828125 5.0409090909090954  C 14.548828125 4.427272727272731  14.207812499999996 3.854545454545463  13.659375000000004 3.547727272727281  L 8.389453124999996 0.5971590909090826  C 7.841015625000004 0.2903409090909008  7.158984375000001 0.2903409090909008  6.6123046875 0.5971590909090826  L 1.340625000000001 3.547727272727281  C 0.7921874999999989 3.854545454545463  0.451171875 4.427272727272731  0.451171875 5.0409090909090954  L 0.451171875 10.94204545454545  C 0.451171875 11.555681818181814  0.7921874999999989 12.128409090909082  1.340625000000001 12.435227272727264  L 6.610546874999999 15.385795454545462  C 6.884765625 15.539204545454554  7.1923828125 15.6159090909091  7.5 15.6159090909091  C 7.8076171875 15.6159090909091  8.115234375 15.539204545454554  8.389453124999996 15.385795454545462  L 13.659375000000004 12.435227272727264  Z M 7.774218749999996 1.6301136363636446  L 13.044140625000004 4.580681818181822  C 13.212890625 4.674431818181822  13.318359375 4.851704545454553  13.318359375 5.0409090909090954  L 13.318359375 10.94204545454545  C 13.318359375 11.131249999999993  13.212890625 11.306818181818182  13.044140625000004 11.402272727272724  L 7.774218749999996 14.3528409090909  C 7.605468750000001 14.4465909090909  7.396289062500001 14.4465909090909  7.2275390625 14.3528409090909  L 1.955859375000001 11.402272727272724  C 1.787109375 11.308522727272724  1.681640625 11.131249999999993  1.681640625 10.94204545454545  L 1.681640625 5.0409090909090954  C 1.681640625 4.851704545454553  1.787109375 4.676136363636363  1.955859375000001 4.580681818181822  L 7.225781249999999 1.6301136363636446  C 7.310156250000002 1.5823863636363635  7.405078125000001 1.5585227272727231  7.5 1.5585227272727231  C 7.594921875000003 1.5585227272727231  7.689843749999997 1.5823863636363635  7.774218749999996 1.6301136363636446  Z M 9.270117187500002 11.005113636363644  C 9.605859374999998 11.005113636363644  9.906445312500004 10.7409090909091  9.906445312500004 10.413636363636359  L 9.906445312500004 8.134659090909095  C 9.906445312500004 7.8090909090909175  9.633984375000004 7.544886363636372  9.296484375000002 7.544886363636372  C 8.960742187499996 7.544886363636372  8.66015625 7.8090909090909175  8.66015625 8.136363636363637  L 8.66015625 10.413636363636359  C 8.66015625 10.739204545454538  8.9326171875 11.005113636363644  9.270117187500002 11.005113636363644  Z M 7.471874999999999 11.003409090909082  C 7.8076171875 11.003409090909082  8.108203124999996 10.739204545454538  8.108203124999996 10.411931818181818  L 8.108203124999996 6.803409090909099  C 8.108203124999996 6.47613636363636  7.833984374999999 6.211931818181814  7.498242187499999 6.211931818181814  C 7.162499999999998 6.211931818181814  6.861914062500002 6.47613636363636  6.861914062500002 6.803409090909099  L 6.861914062500002 10.411931818181818  C 6.861914062500002 10.737499999999995  7.134375000000002 11.003409090909082  7.471874999999999 11.003409090909082  Z M 5.671875 11.003409090909082  C 6.007617187500001 11.003409090909082  6.308203125000002 10.739204545454538  6.308203125000002 10.411931818181818  L 6.308203125000002 9.511931818181809  C 6.308203125000002 9.184659090909092  6.035742187500002 8.920454545454545  5.6982421875 8.920454545454545  C 5.362499999999999 8.920454545454545  5.061914062499998 9.184659090909092  5.061914062499998 9.511931818181809  L 5.061914062499998 10.411931818181818  C 5.061914062499998 10.737499999999995  5.334374999999998 11.003409090909082  5.671875 11.003409090909082  Z " fill-rule="nonzero" fill="rgba(236, 236, 236, 1)" stroke="none" transform="matrix(1 0 0 1 31 157 )" class="fill"></path>
    <path d="M 13.659375000000004 12.435227272727264  C 14.207812499999996 12.128409090909082  14.548828125 11.555681818181814  14.548828125 10.94204545454545  L 14.548828125 5.0409090909090954  C 14.548828125 4.427272727272731  14.207812499999996 3.854545454545463  13.659375000000004 3.547727272727281  L 8.389453124999996 0.5971590909090826  C 7.841015625000004 0.2903409090909008  7.158984375000001 0.2903409090909008  6.6123046875 0.5971590909090826  L 1.340625000000001 3.547727272727281  C 0.7921874999999989 3.854545454545463  0.451171875 4.427272727272731  0.451171875 5.0409090909090954  L 0.451171875 10.94204545454545  C 0.451171875 11.555681818181814  0.7921874999999989 12.128409090909082  1.340625000000001 12.435227272727264  L 6.610546874999999 15.385795454545462  C 6.884765625 15.539204545454554  7.1923828125 15.6159090909091  7.5 15.6159090909091  C 7.8076171875 15.6159090909091  8.115234375 15.539204545454554  8.389453124999996 15.385795454545462  L 13.659375000000004 12.435227272727264  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 31 157 )" class="stroke" mask="url(#u13_img_cl6)"></path>
    <path d="M 7.774218749999996 1.6301136363636446  L 13.044140625000004 4.580681818181822  C 13.212890625 4.674431818181822  13.318359375 4.851704545454553  13.318359375 5.0409090909090954  L 13.318359375 10.94204545454545  C 13.318359375 11.131249999999993  13.212890625 11.306818181818182  13.044140625000004 11.402272727272724  L 7.774218749999996 14.3528409090909  C 7.605468750000001 14.4465909090909  7.396289062500001 14.4465909090909  7.2275390625 14.3528409090909  L 1.955859375000001 11.402272727272724  C 1.787109375 11.308522727272724  1.681640625 11.131249999999993  1.681640625 10.94204545454545  L 1.681640625 5.0409090909090954  C 1.681640625 4.851704545454553  1.787109375 4.676136363636363  1.955859375000001 4.580681818181822  L 7.225781249999999 1.6301136363636446  C 7.310156250000002 1.5823863636363635  7.405078125000001 1.5585227272727231  7.5 1.5585227272727231  C 7.594921875000003 1.5585227272727231  7.689843749999997 1.5823863636363635  7.774218749999996 1.6301136363636446  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 31 157 )" class="stroke" mask="url(#u13_img_cl6)"></path>
    <path d="M 9.270117187500002 11.005113636363644  C 9.605859374999998 11.005113636363644  9.906445312500004 10.7409090909091  9.906445312500004 10.413636363636359  L 9.906445312500004 8.134659090909095  C 9.906445312500004 7.8090909090909175  9.633984375000004 7.544886363636372  9.296484375000002 7.544886363636372  C 8.960742187499996 7.544886363636372  8.66015625 7.8090909090909175  8.66015625 8.136363636363637  L 8.66015625 10.413636363636359  C 8.66015625 10.739204545454538  8.9326171875 11.005113636363644  9.270117187500002 11.005113636363644  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 31 157 )" class="stroke" mask="url(#u13_img_cl6)"></path>
    <path d="M 7.471874999999999 11.003409090909082  C 7.8076171875 11.003409090909082  8.108203124999996 10.739204545454538  8.108203124999996 10.411931818181818  L 8.108203124999996 6.803409090909099  C 8.108203124999996 6.47613636363636  7.833984374999999 6.211931818181814  7.498242187499999 6.211931818181814  C 7.162499999999998 6.211931818181814  6.861914062500002 6.47613636363636  6.861914062500002 6.803409090909099  L 6.861914062500002 10.411931818181818  C 6.861914062500002 10.737499999999995  7.134375000000002 11.003409090909082  7.471874999999999 11.003409090909082  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 31 157 )" class="stroke" mask="url(#u13_img_cl6)"></path>
    <path d="M 5.671875 11.003409090909082  C 6.007617187500001 11.003409090909082  6.308203125000002 10.739204545454538  6.308203125000002 10.411931818181818  L 6.308203125000002 9.511931818181809  C 6.308203125000002 9.184659090909092  6.035742187500002 8.920454545454545  5.6982421875 8.920454545454545  C 5.362499999999999 8.920454545454545  5.061914062499998 9.184659090909092  5.061914062499998 9.511931818181809  L 5.061914062499998 10.411931818181818  C 5.061914062499998 10.737499999999995  5.334374999999998 11.003409090909082  5.671875 11.003409090909082  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 31 157 )" class="stroke" mask="url(#u13_img_cl6)"></path>
  </g>
            </svg>
            <div id="u13_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (Group) -->
      <div id="u14" class="ax_default" data-left="14" data-top="193" data-width="220" data-height="40" layer-opacity="1" style="cursor: pointer;">

        <!-- Unnamed (Rectangle) -->
        <div id="u15" class="ax_default box_1 transition" selectiongroup="菜单">
          <div id="u15_div" class="" tabindex="0"></div>
          <div id="u15_text" class="text ">
            <p id="cache9" style=""><span id="cache10" style="">缺陷管理</span></p>
          </div>
        </div>

        <!-- Unnamed (Rectangle) -->
        <div id="u16" class="ax_default _形状 transition" selectiongroup="二级菜单">
          <div id="u16_div" class=""></div>
          <div id="u16_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (Group) -->
        <div id="u17" class="ax_default" data-left="31" data-top="205" data-width="16" data-height="16" layer-opacity="1">

          <!-- Unnamed (Shape) -->
          <div id="u18" class="ax_default _形状 transition">
            <svg data="http://***********/gsc/VBQ4F3/1c/74/19/1c74195a2a8d4aacad90f09e97e1cc57/images/index/u18.svg" id="u18_img" class="img generatedImage">

  <defs>
    <pattern id="u18_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imagerepeat="None"></pattern>
    <mask fill="white" id="u18_img_cl7">
      <path d="M 14.109803927389708 14.4972548961397  C 13.950203622059549 14.49738512234139  13.797217246378791 14.433506604759607  13.685098045036765 14.319921574448525  L 12.66031683613326 13.282141027123464  C 11.76420691290778 14.8616430678597  10.067597509682855 15.927288574982386  8.122196072610295 15.927843131433827  L 7.892705882352944 15.927843131433827  C 5.946578842849075 15.927843131433827  4.249400489684109 14.861967096331682  3.353095856224627 13.28214008743097  L 2.328313719669121 14.319921574448525  C 2.214747460399682 14.434991190205464  2.0593111734780556 14.498994363836387  1.8976470588235288 14.4972548961397  C 1.741483317610436 14.496993551403007  1.591658766513092 14.435458469148323  1.4803921626838232 14.325882352941175  C 1.3684623142156558 14.214076960972173  1.30557030725548 14.062361590014943  1.30557030725548 13.904156859834556  C 1.30557030725548 13.74595212965417  1.3684623142156558 13.594236758696939  1.4803921626838232 13.482431366727937  L 2.8594270685974768 12.08442808250367  C 2.7404958081291464 11.64717743758052  2.677019603612206 11.187079983951849  2.6770196020220616 10.71215686856618  L 2.6770190342527673 9.99686275091913  L 1.2330196020220583 9.99686275091913  C 0.9038145730644942 9.99686275091913  0.6369411677389695 9.729989345593609  0.6369411677389695 9.400784316636017  C 0.6369411677389695 9.071579287678453  0.9038145730644942 8.804705882352932  1.2330196020220583 8.804705882352932  L 2.6770196020220616 8.804705882351914  L 2.6770196020220616 6.688627456801468  C 2.6772311386582515 6.639927536681091  2.6791330880768904 6.59162829640389  2.6826697483283946 6.543785273293906  L 1.4744313667279432 5.3176470588235345  C 1.3705507638301844 5.207109959447494  1.3127197675125264 5.061126870256885  1.3127197675125264 4.909437615538991  C 1.3127197675125264 4.580232591403782  1.5795931689287688 4.313359189987536  1.908798193063998 4.313359189987536  C 2.0630380296870565 4.313359189987536  2.2112732754139404 4.373146424890846  2.322352941176472 4.480156868566182  L 3.1786798443794972 5.350967098579638  C 3.293555827342897 5.2183203088656605  3.4251040636572925 5.100516957053755  3.5700615276305814 5.000810604240274  C 3.5501938739601844 4.918937682284676  3.5397201602626716 4.8334015841855145  3.539843131433827 4.745411764705884  L 3.539843131433827 4.259607837316173  C 3.5315669202673035 1.9621073380621965  5.382163233744537 0.0901938656312161  7.679607837316178 0.072156868566173  L 8.27568628033088 0.072156868566173  C 10.570797323981798 0.0934545418757914  12.417786179767932 1.9644129719213463  12.40949019025735 4.259607837316173  L 12.40949019025735 4.745411764705884  C 12.409594737097203 4.8202183838436  12.402040052008688 4.893251465184007  12.387565223570645 4.963767795858477  C 12.55232566979137 5.069384727427405  12.70104438410771 5.197845456203622  12.829249087401749 5.344684170675651  L 13.677647058823528 4.484627456801477  C 13.786231365439722 4.389859408742241  13.925474069364983 4.337641915930683  14.069597400495903 4.337641915930683  C 14.398802424631132 4.337641915930683  14.665675826047377 4.604515317346929  14.665675826047377 4.933720341482164  C 14.665675826047377 5.074207893861967  14.61605492249248 5.2101808602456  14.52556863327206 5.3176470588235345  L 13.329760917285451 6.531170449537084  C 13.334075377292894 6.585083107089464  13.336312501995895 6.639582373230443  13.336392162683824 6.694588235294117  L 13.336391770714405 8.804705882352932  L 14.761019602022062 8.804705882352932  C 15.090224630979625 8.804705882352932  15.357098036305146 9.071579287678453  15.357098036305146 9.400784316636017  C 15.357098036305146 9.729989345593609  15.090224630979625 9.99686275091913  14.761019602022062 9.99686275091913  L 13.336392162683824 9.996862749619863  L 13.336392162683824 10.718117647058829  C 13.335851810615141 11.19094128477301  13.27240721597614 11.649014359603022  13.124784307904413 12.054823529411753  L 14.533019602022064 13.482431366727937  C 14.64494945049023 13.594236758696939  14.707841457450407 13.74595212965417  14.707841457450407 13.904156859834556  C 14.707841457450407 14.062361590014943  14.64494945049023 14.214076960972173  14.533019602022064 14.325882352941175  C 14.420285149845153 14.436914456393852  14.26802991392282 14.498567104344469  14.109803927389708 14.4972548961397  Z M 11.217333339154406 4.259607837316173  C 11.225627929571882 2.62049802564185  9.9087573433448 1.2823231682798852  8.269725484374998 1.2643137196691094  L 7.673647058823529 1.2643137196691094  C 6.036950977182668 1.2855714098654436  4.723677789140826 2.622794868813852  4.7319999999999975 4.259607837316173  L 4.7319999999999975 4.6276862803308765  L 11.217333339154413 4.6276862803308765  L 11.217333339154406 4.259607837316173  Z M 11.281413040496613 5.831759562839553  L 8.603529411764706 5.831761665755948  L 8.6035162234878 14.713154899460251  C 10.59873505277898 14.475302813217963  12.145725484375001 12.777369717783854  12.145725484375001 10.718117647058829  L 12.145725484375001 6.694588235294117  C 12.144906311722726 6.217821496759986  11.758180482774625 5.831759562839553  11.281413040496613 5.831759562839553  Z M 7.411372543198528 14.713154970739572  L 7.411374950894075 5.831762601855617  L 4.7319999999999975 5.831764705882345  C 4.255815915564453 5.832585004398023  3.8699967691039103 6.218404150858559  3.8691764705882328 6.694588235294117  L 3.8691764705882328 10.718117647058829  C 3.8691764705882394 12.777369717783854  5.416166902184252 14.475302813217963  7.411372543198528 14.713154970739572  Z " fill-rule="evenodd"></path>
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -31 -205 )">
    <path d="M 14.109803927389708 14.4972548961397  C 13.950203622059549 14.49738512234139  13.797217246378791 14.433506604759607  13.685098045036765 14.319921574448525  L 12.66031683613326 13.282141027123464  C 11.76420691290778 14.8616430678597  10.067597509682855 15.927288574982386  8.122196072610295 15.927843131433827  L 7.892705882352944 15.927843131433827  C 5.946578842849075 15.927843131433827  4.249400489684109 14.861967096331682  3.353095856224627 13.28214008743097  L 2.328313719669121 14.319921574448525  C 2.214747460399682 14.434991190205464  2.0593111734780556 14.498994363836387  1.8976470588235288 14.4972548961397  C 1.741483317610436 14.496993551403007  1.591658766513092 14.435458469148323  1.4803921626838232 14.325882352941175  C 1.3684623142156558 14.214076960972173  1.30557030725548 14.062361590014943  1.30557030725548 13.904156859834556  C 1.30557030725548 13.74595212965417  1.3684623142156558 13.594236758696939  1.4803921626838232 13.482431366727937  L 2.8594270685974768 12.08442808250367  C 2.7404958081291464 11.64717743758052  2.677019603612206 11.187079983951849  2.6770196020220616 10.71215686856618  L 2.6770190342527673 9.99686275091913  L 1.2330196020220583 9.99686275091913  C 0.9038145730644942 9.99686275091913  0.6369411677389695 9.729989345593609  0.6369411677389695 9.400784316636017  C 0.6369411677389695 9.071579287678453  0.9038145730644942 8.804705882352932  1.2330196020220583 8.804705882352932  L 2.6770196020220616 8.804705882351914  L 2.6770196020220616 6.688627456801468  C 2.6772311386582515 6.639927536681091  2.6791330880768904 6.59162829640389  2.6826697483283946 6.543785273293906  L 1.4744313667279432 5.3176470588235345  C 1.3705507638301844 5.207109959447494  1.3127197675125264 5.061126870256885  1.3127197675125264 4.909437615538991  C 1.3127197675125264 4.580232591403782  1.5795931689287688 4.313359189987536  1.908798193063998 4.313359189987536  C 2.0630380296870565 4.313359189987536  2.2112732754139404 4.373146424890846  2.322352941176472 4.480156868566182  L 3.1786798443794972 5.350967098579638  C 3.293555827342897 5.2183203088656605  3.4251040636572925 5.100516957053755  3.5700615276305814 5.000810604240274  C 3.5501938739601844 4.918937682284676  3.5397201602626716 4.8334015841855145  3.539843131433827 4.745411764705884  L 3.539843131433827 4.259607837316173  C 3.5315669202673035 1.9621073380621965  5.382163233744537 0.0901938656312161  7.679607837316178 0.072156868566173  L 8.27568628033088 0.072156868566173  C 10.570797323981798 0.0934545418757914  12.417786179767932 1.9644129719213463  12.40949019025735 4.259607837316173  L 12.40949019025735 4.745411764705884  C 12.409594737097203 4.8202183838436  12.402040052008688 4.893251465184007  12.387565223570645 4.963767795858477  C 12.55232566979137 5.069384727427405  12.70104438410771 5.197845456203622  12.829249087401749 5.344684170675651  L 13.677647058823528 4.484627456801477  C 13.786231365439722 4.389859408742241  13.925474069364983 4.337641915930683  14.069597400495903 4.337641915930683  C 14.398802424631132 4.337641915930683  14.665675826047377 4.604515317346929  14.665675826047377 4.933720341482164  C 14.665675826047377 5.074207893861967  14.61605492249248 5.2101808602456  14.52556863327206 5.3176470588235345  L 13.329760917285451 6.531170449537084  C 13.334075377292894 6.585083107089464  13.336312501995895 6.639582373230443  13.336392162683824 6.694588235294117  L 13.336391770714405 8.804705882352932  L 14.761019602022062 8.804705882352932  C 15.090224630979625 8.804705882352932  15.357098036305146 9.071579287678453  15.357098036305146 9.400784316636017  C 15.357098036305146 9.729989345593609  15.090224630979625 9.99686275091913  14.761019602022062 9.99686275091913  L 13.336392162683824 9.996862749619863  L 13.336392162683824 10.718117647058829  C 13.335851810615141 11.19094128477301  13.27240721597614 11.649014359603022  13.124784307904413 12.054823529411753  L 14.533019602022064 13.482431366727937  C 14.64494945049023 13.594236758696939  14.707841457450407 13.74595212965417  14.707841457450407 13.904156859834556  C 14.707841457450407 14.062361590014943  14.64494945049023 14.214076960972173  14.533019602022064 14.325882352941175  C 14.420285149845153 14.436914456393852  14.26802991392282 14.498567104344469  14.109803927389708 14.4972548961397  Z M 11.217333339154406 4.259607837316173  C 11.225627929571882 2.62049802564185  9.9087573433448 1.2823231682798852  8.269725484374998 1.2643137196691094  L 7.673647058823529 1.2643137196691094  C 6.036950977182668 1.2855714098654436  4.723677789140826 2.622794868813852  4.7319999999999975 4.259607837316173  L 4.7319999999999975 4.6276862803308765  L 11.217333339154413 4.6276862803308765  L 11.217333339154406 4.259607837316173  Z M 11.281413040496613 5.831759562839553  L 8.603529411764706 5.831761665755948  L 8.6035162234878 14.713154899460251  C 10.59873505277898 14.475302813217963  12.145725484375001 12.777369717783854  12.145725484375001 10.718117647058829  L 12.145725484375001 6.694588235294117  C 12.144906311722726 6.217821496759986  11.758180482774625 5.831759562839553  11.281413040496613 5.831759562839553  Z M 7.411372543198528 14.713154970739572  L 7.411374950894075 5.831762601855617  L 4.7319999999999975 5.831764705882345  C 4.255815915564453 5.832585004398023  3.8699967691039103 6.218404150858559  3.8691764705882328 6.694588235294117  L 3.8691764705882328 10.718117647058829  C 3.8691764705882394 12.777369717783854  5.416166902184252 14.475302813217963  7.411372543198528 14.713154970739572  Z " fill-rule="nonzero" fill="rgba(236, 236, 236, 1)" stroke="none" transform="matrix(1 0 0 1 31 205 )" class="fill"></path>
    <path d="M 14.109803927389708 14.4972548961397  C 13.950203622059549 14.49738512234139  13.797217246378791 14.433506604759607  13.685098045036765 14.319921574448525  L 12.66031683613326 13.282141027123464  C 11.76420691290778 14.8616430678597  10.067597509682855 15.927288574982386  8.122196072610295 15.927843131433827  L 7.892705882352944 15.927843131433827  C 5.946578842849075 15.927843131433827  4.249400489684109 14.861967096331682  3.353095856224627 13.28214008743097  L 2.328313719669121 14.319921574448525  C 2.214747460399682 14.434991190205464  2.0593111734780556 14.498994363836387  1.8976470588235288 14.4972548961397  C 1.741483317610436 14.496993551403007  1.591658766513092 14.435458469148323  1.4803921626838232 14.325882352941175  C 1.3684623142156558 14.214076960972173  1.30557030725548 14.062361590014943  1.30557030725548 13.904156859834556  C 1.30557030725548 13.74595212965417  1.3684623142156558 13.594236758696939  1.4803921626838232 13.482431366727937  L 2.8594270685974768 12.08442808250367  C 2.7404958081291464 11.64717743758052  2.677019603612206 11.187079983951849  2.6770196020220616 10.71215686856618  L 2.6770190342527673 9.99686275091913  L 1.2330196020220583 9.99686275091913  C 0.9038145730644942 9.99686275091913  0.6369411677389695 9.729989345593609  0.6369411677389695 9.400784316636017  C 0.6369411677389695 9.071579287678453  0.9038145730644942 8.804705882352932  1.2330196020220583 8.804705882352932  L 2.6770196020220616 8.804705882351914  L 2.6770196020220616 6.688627456801468  C 2.6772311386582515 6.639927536681091  2.6791330880768904 6.59162829640389  2.6826697483283946 6.543785273293906  L 1.4744313667279432 5.3176470588235345  C 1.3705507638301844 5.207109959447494  1.3127197675125264 5.061126870256885  1.3127197675125264 4.909437615538991  C 1.3127197675125264 4.580232591403782  1.5795931689287688 4.313359189987536  1.908798193063998 4.313359189987536  C 2.0630380296870565 4.313359189987536  2.2112732754139404 4.373146424890846  2.322352941176472 4.480156868566182  L 3.1786798443794972 5.350967098579638  C 3.293555827342897 5.2183203088656605  3.4251040636572925 5.100516957053755  3.5700615276305814 5.000810604240274  C 3.5501938739601844 4.918937682284676  3.5397201602626716 4.8334015841855145  3.539843131433827 4.745411764705884  L 3.539843131433827 4.259607837316173  C 3.5315669202673035 1.9621073380621965  5.382163233744537 0.0901938656312161  7.679607837316178 0.072156868566173  L 8.27568628033088 0.072156868566173  C 10.570797323981798 0.0934545418757914  12.417786179767932 1.9644129719213463  12.40949019025735 4.259607837316173  L 12.40949019025735 4.745411764705884  C 12.409594737097203 4.8202183838436  12.402040052008688 4.893251465184007  12.387565223570645 4.963767795858477  C 12.55232566979137 5.069384727427405  12.70104438410771 5.197845456203622  12.829249087401749 5.344684170675651  L 13.677647058823528 4.484627456801477  C 13.786231365439722 4.389859408742241  13.925474069364983 4.337641915930683  14.069597400495903 4.337641915930683  C 14.398802424631132 4.337641915930683  14.665675826047377 4.604515317346929  14.665675826047377 4.933720341482164  C 14.665675826047377 5.074207893861967  14.61605492249248 5.2101808602456  14.52556863327206 5.3176470588235345  L 13.329760917285451 6.531170449537084  C 13.334075377292894 6.585083107089464  13.336312501995895 6.639582373230443  13.336392162683824 6.694588235294117  L 13.336391770714405 8.804705882352932  L 14.761019602022062 8.804705882352932  C 15.090224630979625 8.804705882352932  15.357098036305146 9.071579287678453  15.357098036305146 9.400784316636017  C 15.357098036305146 9.729989345593609  15.090224630979625 9.99686275091913  14.761019602022062 9.99686275091913  L 13.336392162683824 9.996862749619863  L 13.336392162683824 10.718117647058829  C 13.335851810615141 11.19094128477301  13.27240721597614 11.649014359603022  13.124784307904413 12.054823529411753  L 14.533019602022064 13.482431366727937  C 14.64494945049023 13.594236758696939  14.707841457450407 13.74595212965417  14.707841457450407 13.904156859834556  C 14.707841457450407 14.062361590014943  14.64494945049023 14.214076960972173  14.533019602022064 14.325882352941175  C 14.420285149845153 14.436914456393852  14.26802991392282 14.498567104344469  14.109803927389708 14.4972548961397  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 31 205 )" class="stroke" mask="url(#u18_img_cl7)"></path>
    <path d="M 11.217333339154406 4.259607837316173  C 11.225627929571882 2.62049802564185  9.9087573433448 1.2823231682798852  8.269725484374998 1.2643137196691094  L 7.673647058823529 1.2643137196691094  C 6.036950977182668 1.2855714098654436  4.723677789140826 2.622794868813852  4.7319999999999975 4.259607837316173  L 4.7319999999999975 4.6276862803308765  L 11.217333339154413 4.6276862803308765  L 11.217333339154406 4.259607837316173  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 31 205 )" class="stroke" mask="url(#u18_img_cl7)"></path>
    <path d="M 11.281413040496613 5.831759562839553  L 8.603529411764706 5.831761665755948  L 8.6035162234878 14.713154899460251  C 10.59873505277898 14.475302813217963  12.145725484375001 12.777369717783854  12.145725484375001 10.718117647058829  L 12.145725484375001 6.694588235294117  C 12.144906311722726 6.217821496759986  11.758180482774625 5.831759562839553  11.281413040496613 5.831759562839553  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 31 205 )" class="stroke" mask="url(#u18_img_cl7)"></path>
    <path d="M 7.411372543198528 14.713154970739572  L 7.411374950894075 5.831762601855617  L 4.7319999999999975 5.831764705882345  C 4.255815915564453 5.832585004398023  3.8699967691039103 6.218404150858559  3.8691764705882328 6.694588235294117  L 3.8691764705882328 10.718117647058829  C 3.8691764705882394 12.777369717783854  5.416166902184252 14.475302813217963  7.411372543198528 14.713154970739572  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 31 205 )" class="stroke" mask="url(#u18_img_cl7)"></path>
  </g>
            </svg>
            <div id="u18_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (Group) -->
      <div id="u19" class="ax_default" data-left="14" data-top="97" data-width="220" data-height="40" layer-opacity="1" style="cursor: pointer;">

        <!-- Unnamed (Rectangle) -->
        <div id="u20" class="ax_default box_1 transition" selectiongroup="菜单">
          <div id="u20_div" class="" tabindex="0"></div>
          <div id="u20_text" class="text ">
            <p id="cache15" style=""><span id="cache16" style="">首页</span></p>
          </div>
        </div>

        <!-- Unnamed (Rectangle) -->
        <div id="u21" class="ax_default _形状 transition" selectiongroup="二级菜单">
          <div id="u21_div" class=""></div>
          <div id="u21_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (Shape) -->
        <div id="u22" class="ax_default _形状 transition" selectiongroup="菜单">
          <svg data="http://***********/gsc/VBQ4F3/1c/74/19/1c74195a2a8d4aacad90f09e97e1cc57/images/index/u22.svg" id="u22_img" class="img generatedImage">

  <defs>
    <pattern id="u22_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imagerepeat="None"></pattern>
    <mask fill="white" id="u22_img_cl8">
      <path d="M 1.0779999999999998 5.946  L 7.326666671875 0.905333328125  C 7.719031286168874 0.5871071615789233  8.280635385706125 0.5871071615789233  8.673 0.905333328125  L 14.922000000000002 5.946333328125  C 15.391666671875 6.325  15.471 7.01966665625  15.099000000000002 7.498333328125  C 14.950913772413601 7.68974076739655  14.742951003527594 7.8259962823783  14.508333328125 7.885333328125  L 14.450333328125001 7.898000000000001  L 14.450666671875 13.546666671875002  C 14.450666671875 14.47433334375  13.755666671874998 15.241666671875  12.848666671875 15.326  L 12.774666671875 15.331666671874999  L 12.697333328125 15.333333328125  L 3.302666671875 15.333333328125  C 2.367666671875 15.333333328125  1.598666671875 14.586333328125  1.551 13.625333328125  L 1.549333328125 13.546666671875002  L 1.549 7.898000000000001  L 1.510666671875 7.890333328125002  C 1.046666671875 7.783333328125001  0.695666671875 7.3733333281250015  0.6683333281249999 6.875333328125002  L 0.666666671875 6.812666671875  C 0.666666671875 6.4750000000000005  0.818 6.156000000000001  1.0779999999999998 5.946  Z M 14.298666671874996 6.736999999999999  L 8.056666671875 1.6869999999999998  C 8.023632218193265 1.6602274475426018  7.976367781806735 1.6602274475426018  7.943333328125 1.6870000000000003  L 1.7009999999999998 6.736666671875  C 1.6790267785930375 6.7546715330168015  1.6663005866381961 6.781592317733931  1.6663333281250001 6.81  C 1.6663333281250001 6.861999999999999  1.707333328125 6.903999999999999  1.758 6.903999999999999  L 2.052 6.903999999999999  C 2.326 6.903666671874999  2.548 7.130666671875  2.548 7.410333328125  L 2.548 13.543666671875  L 2.549 13.596666671875  C 2.569333328125 14.008333328125  2.903 14.333333328125  3.308 14.333333328125  L 12.679333328125 14.333333328125  L 12.731333328125 14.331999999999999  C 13.134 14.311333328125  13.45166665625 13.970333328125001  13.451666671875 13.556666671875002  L 13.451666671875 7.410666671874999  C 13.451666671875 7.130666671875  13.673666671874999 6.903999999999999  13.947666671875 6.903999999999999  L 14.241666671875 6.903999999999999  C 14.269666671875 6.903999999999999  14.296000000000001 6.891  14.313666671875 6.868666671875001  C 14.344974711011751 6.827909400910558  14.33833993327267 6.769670794008243  14.298666671874996 6.736999999999999  Z M 9.833194043324344 10.333171075990682  C 9.980330383719137 10.333171075990682  10.119997670475493 10.397978265712622  10.215 10.510333328125  C 10.393387090790329 10.72112229314403  10.367121186012698 11.036611600989454  10.156333328125 11.215  C 9.544666671875 11.732666671875  8.876666671875 12  8.166666671875 12  C 7.456666671874999 12  6.788666671874999 11.732666671875  6.176666671874999 11.215  C 6.06447751847038 11.119957661316993  5.999786162193621 10.98036888362574  5.999786162193621 10.8333333359375  C 5.999786162193621 10.557072861662704  6.223739525725202 10.33311949813112  6.499999999999999 10.33311949813112  C 6.618411893184011 10.33311949813112  6.732984223719853 10.375126383264295  6.823333328124999 10.451666671875  C 7.261 10.822666671875  7.704000000000001 11  8.166666671875 11  C 8.629 11  9.072 10.822666671875  9.51 10.451666671875  C 9.600310328611846 10.375159664503304  9.714833249009029 10.333171075990682  9.833194043324344 10.333171075990682  Z " fill-rule="evenodd"></path>
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -31 -109 )">
    <path d="M 1.0779999999999998 5.946  L 7.326666671875 0.905333328125  C 7.719031286168874 0.5871071615789233  8.280635385706125 0.5871071615789233  8.673 0.905333328125  L 14.922000000000002 5.946333328125  C 15.391666671875 6.325  15.471 7.01966665625  15.099000000000002 7.498333328125  C 14.950913772413601 7.68974076739655  14.742951003527594 7.8259962823783  14.508333328125 7.885333328125  L 14.450333328125001 7.898000000000001  L 14.450666671875 13.546666671875002  C 14.450666671875 14.47433334375  13.755666671874998 15.241666671875  12.848666671875 15.326  L 12.774666671875 15.331666671874999  L 12.697333328125 15.333333328125  L 3.302666671875 15.333333328125  C 2.367666671875 15.333333328125  1.598666671875 14.586333328125  1.551 13.625333328125  L 1.549333328125 13.546666671875002  L 1.549 7.898000000000001  L 1.510666671875 7.890333328125002  C 1.046666671875 7.783333328125001  0.695666671875 7.3733333281250015  0.6683333281249999 6.875333328125002  L 0.666666671875 6.812666671875  C 0.666666671875 6.4750000000000005  0.818 6.156000000000001  1.0779999999999998 5.946  Z M 14.298666671874996 6.736999999999999  L 8.056666671875 1.6869999999999998  C 8.023632218193265 1.6602274475426018  7.976367781806735 1.6602274475426018  7.943333328125 1.6870000000000003  L 1.7009999999999998 6.736666671875  C 1.6790267785930375 6.7546715330168015  1.6663005866381961 6.781592317733931  1.6663333281250001 6.81  C 1.6663333281250001 6.861999999999999  1.707333328125 6.903999999999999  1.758 6.903999999999999  L 2.052 6.903999999999999  C 2.326 6.903666671874999  2.548 7.130666671875  2.548 7.410333328125  L 2.548 13.543666671875  L 2.549 13.596666671875  C 2.569333328125 14.008333328125  2.903 14.333333328125  3.308 14.333333328125  L 12.679333328125 14.333333328125  L 12.731333328125 14.331999999999999  C 13.134 14.311333328125  13.45166665625 13.970333328125001  13.451666671875 13.556666671875002  L 13.451666671875 7.410666671874999  C 13.451666671875 7.130666671875  13.673666671874999 6.903999999999999  13.947666671875 6.903999999999999  L 14.241666671875 6.903999999999999  C 14.269666671875 6.903999999999999  14.296000000000001 6.891  14.313666671875 6.868666671875001  C 14.344974711011751 6.827909400910558  14.33833993327267 6.769670794008243  14.298666671874996 6.736999999999999  Z M 9.833194043324344 10.333171075990682  C 9.980330383719137 10.333171075990682  10.119997670475493 10.397978265712622  10.215 10.510333328125  C 10.393387090790329 10.72112229314403  10.367121186012698 11.036611600989454  10.156333328125 11.215  C 9.544666671875 11.732666671875  8.876666671875 12  8.166666671875 12  C 7.456666671874999 12  6.788666671874999 11.732666671875  6.176666671874999 11.215  C 6.06447751847038 11.119957661316993  5.999786162193621 10.98036888362574  5.999786162193621 10.8333333359375  C 5.999786162193621 10.557072861662704  6.223739525725202 10.33311949813112  6.499999999999999 10.33311949813112  C 6.618411893184011 10.33311949813112  6.732984223719853 10.375126383264295  6.823333328124999 10.451666671875  C 7.261 10.822666671875  7.704000000000001 11  8.166666671875 11  C 8.629 11  9.072 10.822666671875  9.51 10.451666671875  C 9.600310328611846 10.375159664503304  9.714833249009029 10.333171075990682  9.833194043324344 10.333171075990682  Z " fill-rule="nonzero" fill="rgba(236, 236, 236, 1)" stroke="none" transform="matrix(1 0 0 1 31 109 )" class="fill"></path>
    <path d="M 1.0779999999999998 5.946  L 7.326666671875 0.905333328125  C 7.719031286168874 0.5871071615789233  8.280635385706125 0.5871071615789233  8.673 0.905333328125  L 14.922000000000002 5.946333328125  C 15.391666671875 6.325  15.471 7.01966665625  15.099000000000002 7.498333328125  C 14.950913772413601 7.68974076739655  14.742951003527594 7.8259962823783  14.508333328125 7.885333328125  L 14.450333328125001 7.898000000000001  L 14.450666671875 13.546666671875002  C 14.450666671875 14.47433334375  13.755666671874998 15.241666671875  12.848666671875 15.326  L 12.774666671875 15.331666671874999  L 12.697333328125 15.333333328125  L 3.302666671875 15.333333328125  C 2.367666671875 15.333333328125  1.598666671875 14.586333328125  1.551 13.625333328125  L 1.549333328125 13.546666671875002  L 1.549 7.898000000000001  L 1.510666671875 7.890333328125002  C 1.046666671875 7.783333328125001  0.695666671875 7.3733333281250015  0.6683333281249999 6.875333328125002  L 0.666666671875 6.812666671875  C 0.666666671875 6.4750000000000005  0.818 6.156000000000001  1.0779999999999998 5.946  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 31 109 )" class="stroke" mask="url(#u22_img_cl8)"></path>
    <path d="M 14.298666671874996 6.736999999999999  L 8.056666671875 1.6869999999999998  C 8.023632218193265 1.6602274475426018  7.976367781806735 1.6602274475426018  7.943333328125 1.6870000000000003  L 1.7009999999999998 6.736666671875  C 1.6790267785930375 6.7546715330168015  1.6663005866381961 6.781592317733931  1.6663333281250001 6.81  C 1.6663333281250001 6.861999999999999  1.707333328125 6.903999999999999  1.758 6.903999999999999  L 2.052 6.903999999999999  C 2.326 6.903666671874999  2.548 7.130666671875  2.548 7.410333328125  L 2.548 13.543666671875  L 2.549 13.596666671875  C 2.569333328125 14.008333328125  2.903 14.333333328125  3.308 14.333333328125  L 12.679333328125 14.333333328125  L 12.731333328125 14.331999999999999  C 13.134 14.311333328125  13.45166665625 13.970333328125001  13.451666671875 13.556666671875002  L 13.451666671875 7.410666671874999  C 13.451666671875 7.130666671875  13.673666671874999 6.903999999999999  13.947666671875 6.903999999999999  L 14.241666671875 6.903999999999999  C 14.269666671875 6.903999999999999  14.296000000000001 6.891  14.313666671875 6.868666671875001  C 14.344974711011751 6.827909400910558  14.33833993327267 6.769670794008243  14.298666671874996 6.736999999999999  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 31 109 )" class="stroke" mask="url(#u22_img_cl8)"></path>
    <path d="M 9.833194043324344 10.333171075990682  C 9.980330383719137 10.333171075990682  10.119997670475493 10.397978265712622  10.215 10.510333328125  C 10.393387090790329 10.72112229314403  10.367121186012698 11.036611600989454  10.156333328125 11.215  C 9.544666671875 11.732666671875  8.876666671875 12  8.166666671875 12  C 7.456666671874999 12  6.788666671874999 11.732666671875  6.176666671874999 11.215  C 6.06447751847038 11.119957661316993  5.999786162193621 10.98036888362574  5.999786162193621 10.8333333359375  C 5.999786162193621 10.557072861662704  6.223739525725202 10.33311949813112  6.499999999999999 10.33311949813112  C 6.618411893184011 10.33311949813112  6.732984223719853 10.375126383264295  6.823333328124999 10.451666671875  C 7.261 10.822666671875  7.704000000000001 11  8.166666671875 11  C 8.629 11  9.072 10.822666671875  9.51 10.451666671875  C 9.600310328611846 10.375159664503304  9.714833249009029 10.333171075990682  9.833194043324344 10.333171075990682  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 31 109 )" class="stroke" mask="url(#u22_img_cl8)"></path>
  </g>
          </svg>
          <div id="u22_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (Group) -->
      <div id="u23" class="ax_default" data-left="236" data-top="97" data-width="24" data-height="24" layer-opacity="1" style="cursor: pointer;">

        <!-- Unnamed (Ellipse) -->
        <div id="u24" class="ax_default _形状 transition" style="cursor: pointer;">
          <svg data="http://***********/gsc/VBQ4F3/1c/74/19/1c74195a2a8d4aacad90f09e97e1cc57/images/index/u24.svg" id="u24_img" class="img generatedImage" tabIndex="0">

  <defs>
    <pattern id="u24_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imagerepeat="None"></pattern>
    <mask fill="white" id="u24_img_cl9">
      <path d="M 0 12  C 0 5.279999999999999  5.279999999999999 0  12 0  C 18.72 0  24 5.279999999999999  24 12  C 24 18.72  18.72 24  12 24  C 5.279999999999999 24  0 18.72  0 12  Z " fill-rule="evenodd"></path>
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -236 -97 )">
    <path d="M 0 12  C 0 5.279999999999999  5.279999999999999 0  12 0  C 18.72 0  24 5.279999999999999  24 12  C 24 18.72  18.72 24  12 24  C 5.279999999999999 24  0 18.72  0 12  Z " fill-rule="nonzero" fill="rgba(23, 119, 255, 1)" stroke="none" transform="matrix(1 0 0 1 236 97 )" class="fill"></path>
    <path d="M 0 12  C 0 5.279999999999999  5.279999999999999 0  12 0  C 18.72 0  24 5.279999999999999  24 12  C 24 18.72  18.72 24  12 24  C 5.279999999999999 24  0 18.72  0 12  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 236 97 )" class="stroke" mask="url(#u24_img_cl9)"></path>
  </g>
          </svg>
          <div id="u24_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (Shape) -->
        <div id="u25" class="ax_default _形状 transition">
          <svg data="http://***********/gsc/VBQ4F3/1c/74/19/1c74195a2a8d4aacad90f09e97e1cc57/images/index/u25.svg" id="u25_img" class="img generatedImage">

  <defs>
    <pattern id="u25_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imagerepeat="None"></pattern>
    <mask fill="white" id="u25_img_cl10">
      <path d="M 10.334598214285714 1.55859375  C 10.668526785714285 1.2386718749999999  10.668526785714285 0.7230468749999999  10.334598214285714 0.404296875  C 10.000669642857142 0.08554687500000013  9.459263392857142 0.08554687500000013  9.125334821428572 0.404296875  L 5.5 3.8648437499999995  L 1.8734375 0.404296875  C 1.5395089285714285 0.08554687500000013  0.9981026785714286 0.08554687500000013  0.6641741071428571 0.404296875  C 0.3302455357142856 0.7230468749999999  0.3302455357142856 1.2398437500000001  0.6641741071428571 1.55859375  L 4.894754464285715 5.596875000000001  C 5.228683035714286 5.915625  5.770089285714286 5.915625  6.104017857142858 5.596875000000001  L 10.334598214285714 1.55859375  Z " fill-rule="evenodd"></path>
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -242 -106 )">
    <path d="M 10.334598214285714 1.55859375  C 10.668526785714285 1.2386718749999999  10.668526785714285 0.7230468749999999  10.334598214285714 0.404296875  C 10.000669642857142 0.08554687500000013  9.459263392857142 0.08554687500000013  9.125334821428572 0.404296875  L 5.5 3.8648437499999995  L 1.8734375 0.404296875  C 1.5395089285714285 0.08554687500000013  0.9981026785714286 0.08554687500000013  0.6641741071428571 0.404296875  C 0.3302455357142856 0.7230468749999999  0.3302455357142856 1.2398437500000001  0.6641741071428571 1.55859375  L 4.894754464285715 5.596875000000001  C 5.228683035714286 5.915625  5.770089285714286 5.915625  6.104017857142858 5.596875000000001  L 10.334598214285714 1.55859375  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 242 106 )" class="fill"></path>
    <path d="M 10.334598214285714 1.55859375  C 10.668526785714285 1.2386718749999999  10.668526785714285 0.7230468749999999  10.334598214285714 0.404296875  C 10.000669642857142 0.08554687500000013  9.459263392857142 0.08554687500000013  9.125334821428572 0.404296875  L 5.5 3.8648437499999995  L 1.8734375 0.404296875  C 1.5395089285714285 0.08554687500000013  0.9981026785714286 0.08554687500000013  0.6641741071428571 0.404296875  C 0.3302455357142856 0.7230468749999999  0.3302455357142856 1.2398437500000001  0.6641741071428571 1.55859375  L 4.894754464285715 5.596875000000001  C 5.228683035714286 5.915625  5.770089285714286 5.915625  6.104017857142858 5.596875000000001  L 10.334598214285714 1.55859375  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 242 106 )" class="stroke" mask="url(#u25_img_cl10)"></path>
  </g>
          </svg>
          <div id="u25_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (Group) -->
      <div id="u26" class="ax_default" data-left="14" data-top="241" data-width="220" data-height="40" layer-opacity="1" style="cursor: pointer;">

        <!-- Unnamed (Rectangle) -->
        <div id="u27" class="ax_default box_1 transition" selectiongroup="菜单">
          <div id="u27_div" class="" tabindex="0"></div>
          <div id="u27_text" class="text ">
            <p id="cache11" style=""><span id="cache12" style="">POC管理</span></p>
          </div>
        </div>

        <!-- Unnamed (Rectangle) -->
        <div id="u28" class="ax_default _形状 transition" selectiongroup="二级菜单">
          <div id="u28_div" class=""></div>
          <div id="u28_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (Group) -->
        <div id="u29" class="ax_default" data-left="31" data-top="254" data-width="16" data-height="14" layer-opacity="1">

          <!-- Unnamed (Shape) -->
          <div id="u30" class="ax_default _形状 transition">
            <svg data="http://***********/gsc/VBQ4F3/1c/74/19/1c74195a2a8d4aacad90f09e97e1cc57/images/index/u30.svg" id="u30_img" class="img generatedImage">

  <defs>
    <pattern id="u30_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imagerepeat="None"></pattern>
    <mask fill="white" id="u30_img_cl11">
      <path d="M 4.624 3.4960000000000013  C 4.898145924439782 3.15299882532684  4.844589267069259 2.653136689868621  4.504 2.376  C 4.362039936611465 2.2621778664540857  4.185519508272622 2.200149741967299  4.003563091287069 2.200149741967299  C 3.7589199457865856 2.200149741967299  3.5277245418926655 2.3120887391844054  3.3759999999999994 2.504  L 0.17599999999999927 6.504  C -0.05800026688021429 6.796266239658324  -0.05800026688021429 7.211733760341675  0.17599999999999927 7.503999999999999  L 3.3759999999999994 11.504  C 3.527993301772613 11.68199952622583  3.7503162322138017 11.784506100651514  3.984379888342559 11.784506100651514  C 4.426207688207194 11.784506100651514  4.784379888342559 11.426333900516148  4.784379888342559 10.984506100651515  C 4.784379888342559 10.81119343014452  4.72809724437352 10.642567837076665  4.624 10.504  L 1.824 7  L 4.624 3.4960000000000013  Z M 15.823999999999998 7.503999999999999  C 16.05800026688021 7.211733760341675  16.05800026688021 6.796266239658324  15.824 6.504  L 12.623999999999999 2.5039999999999996  C 12.472006698227386 2.326000473774177  12.249683767786202 2.2234938993484974  12.01562011165745 2.2234938993484974  C 11.573792311792815 2.2234938993484974  11.21562011165745 2.581666099483863  11.21562011165745 3.023493899348497  C 11.21562011165745 3.196806569855485  11.271902755626485 3.3654321629233364  11.376 3.504  L 14.175999999999998 7  L 11.375999999999998 10.504  C 11.271902755626485 10.642567837076662  11.215620111657449 10.811193430144513  11.215620111657449 10.984506100651501  C 11.215620111657449 11.426333900516136  11.573792311792815 11.784506100651502  12.015620111657448 11.784506100651502  C 12.2496837677862 11.784506100651502  12.472006698227382 11.681999526225823  12.623999999999997 11.504  L 15.823999999999998 7.503999999999999  Z M 10.020893854105053 1.3863585580636442  C 10.020893854105053 1.0012620457443657  9.746529922678409 0.6708125796560951  9.368 0.6000000000000001  C 9.312345475850334 0.587942993997322  9.255562670206713 0.5818627978851181  9.1986171045139 0.5818627978851181  C 8.820711698732328 0.5818627978851181  8.494350559923552 0.8463059569338445  8.416 1.2160000000000002  L 6.016 12.416  C 5.999151976522594 12.481292442284158  5.990626563636039 12.548453041717046  5.990626563636039 12.61588418374004  C 5.990626563636039 12.996564329431079  6.258878346034386 13.324517520805706  6.632 13.4  L 6.800000000000001 13.4  C 7.183754246816022 13.4085389161148  7.519486648656635 13.143310318660717  7.6 12.768  L 10 1.5680000000000014  C 10.013883462422392 1.508450313627348  10.020893854105053 1.4475052315292989  10.020893854105053 1.3863585580636442  Z " fill-rule="evenodd"></path>
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -31 -254 )">
    <path d="M 4.624 3.4960000000000013  C 4.898145924439782 3.15299882532684  4.844589267069259 2.653136689868621  4.504 2.376  C 4.362039936611465 2.2621778664540857  4.185519508272622 2.200149741967299  4.003563091287069 2.200149741967299  C 3.7589199457865856 2.200149741967299  3.5277245418926655 2.3120887391844054  3.3759999999999994 2.504  L 0.17599999999999927 6.504  C -0.05800026688021429 6.796266239658324  -0.05800026688021429 7.211733760341675  0.17599999999999927 7.503999999999999  L 3.3759999999999994 11.504  C 3.527993301772613 11.68199952622583  3.7503162322138017 11.784506100651514  3.984379888342559 11.784506100651514  C 4.426207688207194 11.784506100651514  4.784379888342559 11.426333900516148  4.784379888342559 10.984506100651515  C 4.784379888342559 10.81119343014452  4.72809724437352 10.642567837076665  4.624 10.504  L 1.824 7  L 4.624 3.4960000000000013  Z M 15.823999999999998 7.503999999999999  C 16.05800026688021 7.211733760341675  16.05800026688021 6.796266239658324  15.824 6.504  L 12.623999999999999 2.5039999999999996  C 12.472006698227386 2.326000473774177  12.249683767786202 2.2234938993484974  12.01562011165745 2.2234938993484974  C 11.573792311792815 2.2234938993484974  11.21562011165745 2.581666099483863  11.21562011165745 3.023493899348497  C 11.21562011165745 3.196806569855485  11.271902755626485 3.3654321629233364  11.376 3.504  L 14.175999999999998 7  L 11.375999999999998 10.504  C 11.271902755626485 10.642567837076662  11.215620111657449 10.811193430144513  11.215620111657449 10.984506100651501  C 11.215620111657449 11.426333900516136  11.573792311792815 11.784506100651502  12.015620111657448 11.784506100651502  C 12.2496837677862 11.784506100651502  12.472006698227382 11.681999526225823  12.623999999999997 11.504  L 15.823999999999998 7.503999999999999  Z M 10.020893854105053 1.3863585580636442  C 10.020893854105053 1.0012620457443657  9.746529922678409 0.6708125796560951  9.368 0.6000000000000001  C 9.312345475850334 0.587942993997322  9.255562670206713 0.5818627978851181  9.1986171045139 0.5818627978851181  C 8.820711698732328 0.5818627978851181  8.494350559923552 0.8463059569338445  8.416 1.2160000000000002  L 6.016 12.416  C 5.999151976522594 12.481292442284158  5.990626563636039 12.548453041717046  5.990626563636039 12.61588418374004  C 5.990626563636039 12.996564329431079  6.258878346034386 13.324517520805706  6.632 13.4  L 6.800000000000001 13.4  C 7.183754246816022 13.4085389161148  7.519486648656635 13.143310318660717  7.6 12.768  L 10 1.5680000000000014  C 10.013883462422392 1.508450313627348  10.020893854105053 1.4475052315292989  10.020893854105053 1.3863585580636442  Z " fill-rule="nonzero" fill="rgba(236, 236, 236, 1)" stroke="none" transform="matrix(1 0 0 1 31 254 )" class="fill"></path>
    <path d="M 4.624 3.4960000000000013  C 4.898145924439782 3.15299882532684  4.844589267069259 2.653136689868621  4.504 2.376  C 4.362039936611465 2.2621778664540857  4.185519508272622 2.200149741967299  4.003563091287069 2.200149741967299  C 3.7589199457865856 2.200149741967299  3.5277245418926655 2.3120887391844054  3.3759999999999994 2.504  L 0.17599999999999927 6.504  C -0.05800026688021429 6.796266239658324  -0.05800026688021429 7.211733760341675  0.17599999999999927 7.503999999999999  L 3.3759999999999994 11.504  C 3.527993301772613 11.68199952622583  3.7503162322138017 11.784506100651514  3.984379888342559 11.784506100651514  C 4.426207688207194 11.784506100651514  4.784379888342559 11.426333900516148  4.784379888342559 10.984506100651515  C 4.784379888342559 10.81119343014452  4.72809724437352 10.642567837076665  4.624 10.504  L 1.824 7  L 4.624 3.4960000000000013  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 31 254 )" class="stroke" mask="url(#u30_img_cl11)"></path>
    <path d="M 15.823999999999998 7.503999999999999  C 16.05800026688021 7.211733760341675  16.05800026688021 6.796266239658324  15.824 6.504  L 12.623999999999999 2.5039999999999996  C 12.472006698227386 2.326000473774177  12.249683767786202 2.2234938993484974  12.01562011165745 2.2234938993484974  C 11.573792311792815 2.2234938993484974  11.21562011165745 2.581666099483863  11.21562011165745 3.023493899348497  C 11.21562011165745 3.196806569855485  11.271902755626485 3.3654321629233364  11.376 3.504  L 14.175999999999998 7  L 11.375999999999998 10.504  C 11.271902755626485 10.642567837076662  11.215620111657449 10.811193430144513  11.215620111657449 10.984506100651501  C 11.215620111657449 11.426333900516136  11.573792311792815 11.784506100651502  12.015620111657448 11.784506100651502  C 12.2496837677862 11.784506100651502  12.472006698227382 11.681999526225823  12.623999999999997 11.504  L 15.823999999999998 7.503999999999999  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 31 254 )" class="stroke" mask="url(#u30_img_cl11)"></path>
    <path d="M 10.020893854105053 1.3863585580636442  C 10.020893854105053 1.0012620457443657  9.746529922678409 0.6708125796560951  9.368 0.6000000000000001  C 9.312345475850334 0.587942993997322  9.255562670206713 0.5818627978851181  9.1986171045139 0.5818627978851181  C 8.820711698732328 0.5818627978851181  8.494350559923552 0.8463059569338445  8.416 1.2160000000000002  L 6.016 12.416  C 5.999151976522594 12.481292442284158  5.990626563636039 12.548453041717046  5.990626563636039 12.61588418374004  C 5.990626563636039 12.996564329431079  6.258878346034386 13.324517520805706  6.632 13.4  L 6.800000000000001 13.4  C 7.183754246816022 13.4085389161148  7.519486648656635 13.143310318660717  7.6 12.768  L 10 1.5680000000000014  C 10.013883462422392 1.508450313627348  10.020893854105053 1.4475052315292989  10.020893854105053 1.3863585580636442  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 31 254 )" class="stroke" mask="url(#u30_img_cl11)"></path>
  </g>
            </svg>
            <div id="u30_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (Group) -->
      <div id="u31" class="ax_default" data-left="14" data-top="289" data-width="220" data-height="40" layer-opacity="1" style="cursor: pointer;">

        <!-- Unnamed (Rectangle) -->
        <div id="u32" class="ax_default box_1 transition selected" selectiongroup="菜单">
          <div id="u32_div" class="selected" tabindex="0"></div>
          <div id="u32_text" class="text ">
            <p id="cache13" style=""><span id="cache14" style="color: rgb(255, 255, 255);">模型配置</span></p>
          </div>
        </div>

        <!-- Unnamed (Rectangle) -->
        <div id="u33" class="ax_default _形状 transition selected" selectiongroup="二级菜单">
          <div id="u33_div" class="selected"></div>
          <div id="u33_text" class="text " style="display:none; visibility: hidden">
            <p id="cache7" style=""></p>
          </div>
        </div>

        <!-- Unnamed (Group) -->
        <div id="u34" class="ax_default" data-left="32" data-top="301" data-width="14" data-height="16" layer-opacity="1">

          <!-- Unnamed (Shape) -->
          <div id="u35" class="ax_default _形状 transition">
            <svg data="http://***********/gsc/VBQ4F3/1c/74/19/1c74195a2a8d4aacad90f09e97e1cc57/images/index/u35.svg" id="u35_img" class="img generatedImage">

  <defs>
    <pattern id="u35_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imagerepeat="None"></pattern>
    <mask fill="white" id="u35_img_cl12">
      <path d="M 13.533637152777782 4.669375000000001  C 13.534852430555556 4.3118750000000015  13.348914930555559 3.9790625000000013  13.047981770833331 3.8003124999999995  L 7.51603732638889 0.51515625  C 7.214952256944444 0.33609374999999997  6.84140625 0.33703125  6.541384548611111 0.5171875  L 0.9773871527777779 3.85171875  C 0.6811631944444445 4.02921875  0.49613715277777803 4.3584375  0.49492187499999996 4.7109375  L 0.4694010416666667 11.335  C 0.46818576388888883 11.692343750000001  0.6541232638888888 12.025  0.9550564236111109 12.203750000000001  L 6.487000868055556 15.488906250000003  C 6.6367838541666675 15.577968750000002  6.804796006944445 15.622499999999999  6.97265625 15.622499999999999  C 7.141731770833333 15.622499999999999  7.31095920138889 15.57734375  7.461653645833336 15.487031250000001  L 13.025651041666666 12.152500000000002  C 13.321875000000002 11.975000000000001  13.506597222222222 11.645781250000002  13.507964409722224 11.293281250000001  L 13.533637152777782 4.669375000000001  Z M 1.936241319444444 4.4446875  L 7.025520833333331 1.3890625  L 12.118749999999999 4.41375  L 7.061219618055554 7.416875  C 7.040256076388889 7.429375  7.021267361111111 7.444374999999999  7.001367187499999 7.458125  C 6.981315104166665 7.444374999999999  6.96247829861111 7.4292187499999995  6.941514756944443 7.416875  L 1.936241319444444 4.4446875  Z M 6.498697916666668 14.331093750000003  L 1.4505859375 11.33875  L 1.469422743055556 5.332656249999999  L 6.450694444444446 8.290781249999998  C 6.474848090277778 8.305156249999998  6.492317708333333 8.326874999999998  6.504470486111112 8.351562499999998  C 6.503103298611112 8.374843749999998  6.5000651041666675 8.397656249999999  6.500217013888891 8.421093749999999  L 6.510850694444446 11.1640625  L 6.498697916666668 14.331093750000003  Z M 12.531640625000001 11.280312499999997  L 7.504188368055557 14.298749999999998  L 7.49203559027778 11.164218749999998  L 7.502669270833336 8.421406249999999  C 7.502821180555558 8.397968749999999  7.499631076388891 8.374999999999998  7.498415798611114 8.351718749999998  C 7.5105685763888905 8.326874999999998  7.5280381944444485 8.305156249999998  7.5520399305555586 8.290781249999998  L 12.550325520833333 5.322656249999998  L 12.531640625000001 11.280312499999997  Z " fill-rule="evenodd"></path>
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -32 -301 )">
    <path d="M 13.533637152777782 4.669375000000001  C 13.534852430555556 4.3118750000000015  13.348914930555559 3.9790625000000013  13.047981770833331 3.8003124999999995  L 7.51603732638889 0.51515625  C 7.214952256944444 0.33609374999999997  6.84140625 0.33703125  6.541384548611111 0.5171875  L 0.9773871527777779 3.85171875  C 0.6811631944444445 4.02921875  0.49613715277777803 4.3584375  0.49492187499999996 4.7109375  L 0.4694010416666667 11.335  C 0.46818576388888883 11.692343750000001  0.6541232638888888 12.025  0.9550564236111109 12.203750000000001  L 6.487000868055556 15.488906250000003  C 6.6367838541666675 15.577968750000002  6.804796006944445 15.622499999999999  6.97265625 15.622499999999999  C 7.141731770833333 15.622499999999999  7.31095920138889 15.57734375  7.461653645833336 15.487031250000001  L 13.025651041666666 12.152500000000002  C 13.321875000000002 11.975000000000001  13.506597222222222 11.645781250000002  13.507964409722224 11.293281250000001  L 13.533637152777782 4.669375000000001  Z M 1.936241319444444 4.4446875  L 7.025520833333331 1.3890625  L 12.118749999999999 4.41375  L 7.061219618055554 7.416875  C 7.040256076388889 7.429375  7.021267361111111 7.444374999999999  7.001367187499999 7.458125  C 6.981315104166665 7.444374999999999  6.96247829861111 7.4292187499999995  6.941514756944443 7.416875  L 1.936241319444444 4.4446875  Z M 6.498697916666668 14.331093750000003  L 1.4505859375 11.33875  L 1.469422743055556 5.332656249999999  L 6.450694444444446 8.290781249999998  C 6.474848090277778 8.305156249999998  6.492317708333333 8.326874999999998  6.504470486111112 8.351562499999998  C 6.503103298611112 8.374843749999998  6.5000651041666675 8.397656249999999  6.500217013888891 8.421093749999999  L 6.510850694444446 11.1640625  L 6.498697916666668 14.331093750000003  Z M 12.531640625000001 11.280312499999997  L 7.504188368055557 14.298749999999998  L 7.49203559027778 11.164218749999998  L 7.502669270833336 8.421406249999999  C 7.502821180555558 8.397968749999999  7.499631076388891 8.374999999999998  7.498415798611114 8.351718749999998  C 7.5105685763888905 8.326874999999998  7.5280381944444485 8.305156249999998  7.5520399305555586 8.290781249999998  L 12.550325520833333 5.322656249999998  L 12.531640625000001 11.280312499999997  Z " fill-rule="nonzero" fill="rgba(236, 236, 236, 1)" stroke="none" transform="matrix(1 0 0 1 32 301 )" class="fill"></path>
    <path d="M 13.533637152777782 4.669375000000001  C 13.534852430555556 4.3118750000000015  13.348914930555559 3.9790625000000013  13.047981770833331 3.8003124999999995  L 7.51603732638889 0.51515625  C 7.214952256944444 0.33609374999999997  6.84140625 0.33703125  6.541384548611111 0.5171875  L 0.9773871527777779 3.85171875  C 0.6811631944444445 4.02921875  0.49613715277777803 4.3584375  0.49492187499999996 4.7109375  L 0.4694010416666667 11.335  C 0.46818576388888883 11.692343750000001  0.6541232638888888 12.025  0.9550564236111109 12.203750000000001  L 6.487000868055556 15.488906250000003  C 6.6367838541666675 15.577968750000002  6.804796006944445 15.622499999999999  6.97265625 15.622499999999999  C 7.141731770833333 15.622499999999999  7.31095920138889 15.57734375  7.461653645833336 15.487031250000001  L 13.025651041666666 12.152500000000002  C 13.321875000000002 11.975000000000001  13.506597222222222 11.645781250000002  13.507964409722224 11.293281250000001  L 13.533637152777782 4.669375000000001  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 32 301 )" class="stroke" mask="url(#u35_img_cl12)"></path>
    <path d="M 1.936241319444444 4.4446875  L 7.025520833333331 1.3890625  L 12.118749999999999 4.41375  L 7.061219618055554 7.416875  C 7.040256076388889 7.429375  7.021267361111111 7.444374999999999  7.001367187499999 7.458125  C 6.981315104166665 7.444374999999999  6.96247829861111 7.4292187499999995  6.941514756944443 7.416875  L 1.936241319444444 4.4446875  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 32 301 )" class="stroke" mask="url(#u35_img_cl12)"></path>
    <path d="M 6.498697916666668 14.331093750000003  L 1.4505859375 11.33875  L 1.469422743055556 5.332656249999999  L 6.450694444444446 8.290781249999998  C 6.474848090277778 8.305156249999998  6.492317708333333 8.326874999999998  6.504470486111112 8.351562499999998  C 6.503103298611112 8.374843749999998  6.5000651041666675 8.397656249999999  6.500217013888891 8.421093749999999  L 6.510850694444446 11.1640625  L 6.498697916666668 14.331093750000003  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 32 301 )" class="stroke" mask="url(#u35_img_cl12)"></path>
    <path d="M 12.531640625000001 11.280312499999997  L 7.504188368055557 14.298749999999998  L 7.49203559027778 11.164218749999998  L 7.502669270833336 8.421406249999999  C 7.502821180555558 8.397968749999999  7.499631076388891 8.374999999999998  7.498415798611114 8.351718749999998  C 7.5105685763888905 8.326874999999998  7.5280381944444485 8.305156249999998  7.5520399305555586 8.290781249999998  L 12.550325520833333 5.322656249999998  L 12.531640625000001 11.280312499999997  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 32 301 )" class="stroke" mask="url(#u35_img_cl12)"></path>
  </g>
            </svg>
            <div id="u35_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u36" class="ax_default _形状 transition">
        <div id="u36_div" class=""></div>
        <div id="u36_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (Inline frame) -->
      <div id="u37" class="ax_default" style="height: 691px; width: 1624px; left: 273px; top: 0px;">
        <iframe id="u37_input" scrolling="auto" frameborder="0" webkitallowfullscreen="" mozallowfullscreen="" allowfullscreen="" src="./配置列表.html" style="width: 1624px; height: 691px;"></iframe>
      </div>

      <!-- Unnamed (Rectangle) -->
      <div id="u38" class="ax_default _形状 transition">
        <div id="u38_div" class=""></div>
        <div id="u38_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- 风格切换 (Dynamic panel) -->
      <div id="u39" class="ax_default" data-label="风格切换">
        <div id="u39_state0" class="panel_state" data-label="状态2" style="">
          <div id="u39_state0_content" class="panel_state_content">

            <!-- Unnamed (Group) -->
            <div id="u40" class="ax_default" data-left="5" data-top="0" data-width="60" data-height="30" layer-opacity="1">

              <!-- Unnamed (Shape) -->
              <div id="u41" class="ax_default _形状 transition" style="cursor: pointer;">
                <svg data="http://***********/gsc/VBQ4F3/1c/74/19/1c74195a2a8d4aacad90f09e97e1cc57/images/index/u41.svg" id="u41_img" class="img generatedImage" tabIndex="0">

  <defs>
    <pattern id="u41_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imagerepeat="None"></pattern>
    <mask fill="white" id="u41_img_cl13">
      <path d="M 8.152874908600985 2.7167572277199077  C 7.350207552277431 2.9198876405599283  6.587769299694458 3.2575781680028726  5.8979447967664935 3.7154853689597793  C 5.20150120844184 4.174357082175924  4.602689917570891 4.719345463939525  4.1012605374348965 5.350450514250578  C 3.600206684208623 5.981555564561631  3.2070490004701973 6.698652143988716  2.920911327401621 7.501865373987267  C 2.635023988136574 8.304953446072048  2.491955161096644 9.139459084346063  2.491955161096644 10  C 2.491955161096644 11.01700290107784  2.6898482856264474 11.98706719885706  3.085759673611111 12.91269630150463  C 3.481921413122106 13.841204302264178  4.016771063295718 14.63916042086227  4.688180725405094 15.311946944082758  C 5.3599658967013895 15.984608309895833  6.1595492284432884 16.51657904322193  7.085804173574942 16.912991134512442  C 8.012309452003763 17.309403226056133  8.984751981807003 17.507546665400753  10.001504530852142 17.507546665653937  C 10.861669937319153 17.507546665653937  11.69404769560185 17.366730875759554  12.498512629050925 17.079842184823498  C 13.302727228949651 16.793078669524018  14.020199299587674 16.401923707609956  14.650928859194154 15.898616799008973  C 15.280782224283854 15.397938455222802  15.826521624421295 14.79812579445168  16.285768828342015 14.101932539171012  C 16.74308869259975 13.412496785012582  17.08066353166319 12.650763409189844  17.28424661780237 11.848879973524308  C 17.073961706054686 11.86715473310909  16.83576398820891 11.877543816731775  16.572907871057577 11.877543798755791  C 15.428106828522857 11.877543798755791  14.336002185510704 11.65323990487558  13.295217062174476 11.207385840096936  C 12.253806096100984 10.758778034613721  11.355213623770252 10.15646198491754  10.600315839445889 9.402815904296881  C 9.844041194263601 8.646666416268808  9.243227181315103 7.7469474343533  8.795370394458912 6.706537820203993  C 8.34813946831597 5.66587787250434  8.124962084020543 4.5732725444155085  8.124962084020543 3.4285966775173615  C 8.124962084020543 3.1652398573133675  8.134975658456307 2.9279183337311925  8.154251769965278 2.719260616898148  L 8.15300008449074 2.7167572277199077  Z M 10.000753511971933 0.6122809710286458  C 10.211539090567129 0.6122809710286458  10.418819928059895 0.6201666472439239  10.623722552770543 0.6358128420138888  C 10.208409858904801 1.530149536639178  10.000753512225113 2.461160926323784  10.000753512225113 3.4285966775173615  C 10.000753512225113 4.3178014184027775  10.173988219473376 5.167953269169559  10.521834513816549 5.9815555825376165  C 10.868929771050343 6.79252933134404  11.335937512261282 7.491351151186343  11.9232332094184 8.078146163519966  C 12.509777888454858 8.664816018193  13.208599708297161 9.13157340762442  14.020199299587672 9.478418331307871  C 14.830922714843748 9.825263255244504  15.682451408239293 9.999999999746818  16.571906501157404 10  C 17.53984291919849 10  18.471605327763307 9.791342301396122  19.365816864474823 9.376655450014468  C 19.38083721650752 9.582809759693285  19.38772154105179 9.788838893988714  19.38772154079861 10  C 19.38772154079861 10.850151832284434  19.27531926030816 11.682028905490451  19.05101534895833 12.493002672272857  C 18.825459751374417 13.303976421079284  18.512035115451383 14.054993954861112  18.108112856879334 14.740798107783563  C 17.704941616934317 15.429230844509547  17.21390131991464 16.06033589456742  16.637370196578413 16.63661668460648  C 16.060713916087963 17.212897455910014  15.428607495370368 17.70306155891927  14.741676795898433 18.107359326425055  C 14.054245429832173 18.51153191854745  13.304729932798027 18.824455887622975  12.49375618449797 19.05138834606481  C 11.681527697415637 19.275698253666377  10.842634575797918 19.38883913825788  10.000002511827258 19.38771902897136  C 9.157449368112667 19.388763415976328  8.318643526153771 19.27558084351871  7.506499154477721 19.05126318865741  C 6.695400230034723 18.824455887622978  5.946260242440685 18.51153193677662  5.2584533671875 18.10735932642506  C 4.5716478433521415 17.703061558666093  3.9397917749204274 17.212897456163194  3.362134123770254 16.636616684606484  C 2.7861036857638894 16.060335913302954  2.295313740270544 15.42923084450955  1.8923928338758678 14.740798108036747  C 1.488220241753472 14.054868778971354  1.1747955870949076 13.303851244936345  0.9492399902705438 12.493002672272864  C 0.7247148073167852 11.681060169841217  0.6113626215964096 10.842413993879472  0.6122834643735529 10  C 0.6122834643735529 9.149848167715568  0.7249360963903353 8.31797109450955  0.9492399902705438 7.506997327727141  C 1.174795587854456 6.696023578920717  1.4882202237774889 5.945006045138888  1.892392833622685 5.259201891963251  C 2.295438897931134 4.570769155237268  2.786103685510706 3.9396641051793986  3.362134123770254 3.3633833153935173  C 3.9397917746672455 2.78447397902199  4.5716478433521415 2.2943098765190975  5.2584533671875 1.8926406735749417  C 5.946385418330439 1.488468081705729  6.695525406177661 1.1730407234519677  7.506624330367478 0.94873681108941  C 8.318728305048658 0.7244303682415644  9.157491278218451 0.6112478984308737  10.000002511827258 0.6122809710286458  Z " fill-rule="evenodd"></path>
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -5 -5 )">
    <path d="M 8.152874908600985 2.7167572277199077  C 7.350207552277431 2.9198876405599283  6.587769299694458 3.2575781680028726  5.8979447967664935 3.7154853689597793  C 5.20150120844184 4.174357082175924  4.602689917570891 4.719345463939525  4.1012605374348965 5.350450514250578  C 3.600206684208623 5.981555564561631  3.2070490004701973 6.698652143988716  2.920911327401621 7.501865373987267  C 2.635023988136574 8.304953446072048  2.491955161096644 9.139459084346063  2.491955161096644 10  C 2.491955161096644 11.01700290107784  2.6898482856264474 11.98706719885706  3.085759673611111 12.91269630150463  C 3.481921413122106 13.841204302264178  4.016771063295718 14.63916042086227  4.688180725405094 15.311946944082758  C 5.3599658967013895 15.984608309895833  6.1595492284432884 16.51657904322193  7.085804173574942 16.912991134512442  C 8.012309452003763 17.309403226056133  8.984751981807003 17.507546665400753  10.001504530852142 17.507546665653937  C 10.861669937319153 17.507546665653937  11.69404769560185 17.366730875759554  12.498512629050925 17.079842184823498  C 13.302727228949651 16.793078669524018  14.020199299587674 16.401923707609956  14.650928859194154 15.898616799008973  C 15.280782224283854 15.397938455222802  15.826521624421295 14.79812579445168  16.285768828342015 14.101932539171012  C 16.74308869259975 13.412496785012582  17.08066353166319 12.650763409189844  17.28424661780237 11.848879973524308  C 17.073961706054686 11.86715473310909  16.83576398820891 11.877543816731775  16.572907871057577 11.877543798755791  C 15.428106828522857 11.877543798755791  14.336002185510704 11.65323990487558  13.295217062174476 11.207385840096936  C 12.253806096100984 10.758778034613721  11.355213623770252 10.15646198491754  10.600315839445889 9.402815904296881  C 9.844041194263601 8.646666416268808  9.243227181315103 7.7469474343533  8.795370394458912 6.706537820203993  C 8.34813946831597 5.66587787250434  8.124962084020543 4.5732725444155085  8.124962084020543 3.4285966775173615  C 8.124962084020543 3.1652398573133675  8.134975658456307 2.9279183337311925  8.154251769965278 2.719260616898148  L 8.15300008449074 2.7167572277199077  Z M 10.000753511971933 0.6122809710286458  C 10.211539090567129 0.6122809710286458  10.418819928059895 0.6201666472439239  10.623722552770543 0.6358128420138888  C 10.208409858904801 1.530149536639178  10.000753512225113 2.461160926323784  10.000753512225113 3.4285966775173615  C 10.000753512225113 4.3178014184027775  10.173988219473376 5.167953269169559  10.521834513816549 5.9815555825376165  C 10.868929771050343 6.79252933134404  11.335937512261282 7.491351151186343  11.9232332094184 8.078146163519966  C 12.509777888454858 8.664816018193  13.208599708297161 9.13157340762442  14.020199299587672 9.478418331307871  C 14.830922714843748 9.825263255244504  15.682451408239293 9.999999999746818  16.571906501157404 10  C 17.53984291919849 10  18.471605327763307 9.791342301396122  19.365816864474823 9.376655450014468  C 19.38083721650752 9.582809759693285  19.38772154105179 9.788838893988714  19.38772154079861 10  C 19.38772154079861 10.850151832284434  19.27531926030816 11.682028905490451  19.05101534895833 12.493002672272857  C 18.825459751374417 13.303976421079284  18.512035115451383 14.054993954861112  18.108112856879334 14.740798107783563  C 17.704941616934317 15.429230844509547  17.21390131991464 16.06033589456742  16.637370196578413 16.63661668460648  C 16.060713916087963 17.212897455910014  15.428607495370368 17.70306155891927  14.741676795898433 18.107359326425055  C 14.054245429832173 18.51153191854745  13.304729932798027 18.824455887622975  12.49375618449797 19.05138834606481  C 11.681527697415637 19.275698253666377  10.842634575797918 19.38883913825788  10.000002511827258 19.38771902897136  C 9.157449368112667 19.388763415976328  8.318643526153771 19.27558084351871  7.506499154477721 19.05126318865741  C 6.695400230034723 18.824455887622978  5.946260242440685 18.51153193677662  5.2584533671875 18.10735932642506  C 4.5716478433521415 17.703061558666093  3.9397917749204274 17.212897456163194  3.362134123770254 16.636616684606484  C 2.7861036857638894 16.060335913302954  2.295313740270544 15.42923084450955  1.8923928338758678 14.740798108036747  C 1.488220241753472 14.054868778971354  1.1747955870949076 13.303851244936345  0.9492399902705438 12.493002672272864  C 0.7247148073167852 11.681060169841217  0.6113626215964096 10.842413993879472  0.6122834643735529 10  C 0.6122834643735529 9.149848167715568  0.7249360963903353 8.31797109450955  0.9492399902705438 7.506997327727141  C 1.174795587854456 6.696023578920717  1.4882202237774889 5.945006045138888  1.892392833622685 5.259201891963251  C 2.295438897931134 4.570769155237268  2.786103685510706 3.9396641051793986  3.362134123770254 3.3633833153935173  C 3.9397917746672455 2.78447397902199  4.5716478433521415 2.2943098765190975  5.2584533671875 1.8926406735749417  C 5.946385418330439 1.488468081705729  6.695525406177661 1.1730407234519677  7.506624330367478 0.94873681108941  C 8.318728305048658 0.7244303682415644  9.157491278218451 0.6112478984308737  10.000002511827258 0.6122809710286458  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 5 5 )" class="fill"></path>
    <path d="M 8.152874908600985 2.7167572277199077  C 7.350207552277431 2.9198876405599283  6.587769299694458 3.2575781680028726  5.8979447967664935 3.7154853689597793  C 5.20150120844184 4.174357082175924  4.602689917570891 4.719345463939525  4.1012605374348965 5.350450514250578  C 3.600206684208623 5.981555564561631  3.2070490004701973 6.698652143988716  2.920911327401621 7.501865373987267  C 2.635023988136574 8.304953446072048  2.491955161096644 9.139459084346063  2.491955161096644 10  C 2.491955161096644 11.01700290107784  2.6898482856264474 11.98706719885706  3.085759673611111 12.91269630150463  C 3.481921413122106 13.841204302264178  4.016771063295718 14.63916042086227  4.688180725405094 15.311946944082758  C 5.3599658967013895 15.984608309895833  6.1595492284432884 16.51657904322193  7.085804173574942 16.912991134512442  C 8.012309452003763 17.309403226056133  8.984751981807003 17.507546665400753  10.001504530852142 17.507546665653937  C 10.861669937319153 17.507546665653937  11.69404769560185 17.366730875759554  12.498512629050925 17.079842184823498  C 13.302727228949651 16.793078669524018  14.020199299587674 16.401923707609956  14.650928859194154 15.898616799008973  C 15.280782224283854 15.397938455222802  15.826521624421295 14.79812579445168  16.285768828342015 14.101932539171012  C 16.74308869259975 13.412496785012582  17.08066353166319 12.650763409189844  17.28424661780237 11.848879973524308  C 17.073961706054686 11.86715473310909  16.83576398820891 11.877543816731775  16.572907871057577 11.877543798755791  C 15.428106828522857 11.877543798755791  14.336002185510704 11.65323990487558  13.295217062174476 11.207385840096936  C 12.253806096100984 10.758778034613721  11.355213623770252 10.15646198491754  10.600315839445889 9.402815904296881  C 9.844041194263601 8.646666416268808  9.243227181315103 7.7469474343533  8.795370394458912 6.706537820203993  C 8.34813946831597 5.66587787250434  8.124962084020543 4.5732725444155085  8.124962084020543 3.4285966775173615  C 8.124962084020543 3.1652398573133675  8.134975658456307 2.9279183337311925  8.154251769965278 2.719260616898148  L 8.15300008449074 2.7167572277199077  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 5 5 )" class="stroke" mask="url(#u41_img_cl13)"></path>
    <path d="M 10.000753511971933 0.6122809710286458  C 10.211539090567129 0.6122809710286458  10.418819928059895 0.6201666472439239  10.623722552770543 0.6358128420138888  C 10.208409858904801 1.530149536639178  10.000753512225113 2.461160926323784  10.000753512225113 3.4285966775173615  C 10.000753512225113 4.3178014184027775  10.173988219473376 5.167953269169559  10.521834513816549 5.9815555825376165  C 10.868929771050343 6.79252933134404  11.335937512261282 7.491351151186343  11.9232332094184 8.078146163519966  C 12.509777888454858 8.664816018193  13.208599708297161 9.13157340762442  14.020199299587672 9.478418331307871  C 14.830922714843748 9.825263255244504  15.682451408239293 9.999999999746818  16.571906501157404 10  C 17.53984291919849 10  18.471605327763307 9.791342301396122  19.365816864474823 9.376655450014468  C 19.38083721650752 9.582809759693285  19.38772154105179 9.788838893988714  19.38772154079861 10  C 19.38772154079861 10.850151832284434  19.27531926030816 11.682028905490451  19.05101534895833 12.493002672272857  C 18.825459751374417 13.303976421079284  18.512035115451383 14.054993954861112  18.108112856879334 14.740798107783563  C 17.704941616934317 15.429230844509547  17.21390131991464 16.06033589456742  16.637370196578413 16.63661668460648  C 16.060713916087963 17.212897455910014  15.428607495370368 17.70306155891927  14.741676795898433 18.107359326425055  C 14.054245429832173 18.51153191854745  13.304729932798027 18.824455887622975  12.49375618449797 19.05138834606481  C 11.681527697415637 19.275698253666377  10.842634575797918 19.38883913825788  10.000002511827258 19.38771902897136  C 9.157449368112667 19.388763415976328  8.318643526153771 19.27558084351871  7.506499154477721 19.05126318865741  C 6.695400230034723 18.824455887622978  5.946260242440685 18.51153193677662  5.2584533671875 18.10735932642506  C 4.5716478433521415 17.703061558666093  3.9397917749204274 17.212897456163194  3.362134123770254 16.636616684606484  C 2.7861036857638894 16.060335913302954  2.295313740270544 15.42923084450955  1.8923928338758678 14.740798108036747  C 1.488220241753472 14.054868778971354  1.1747955870949076 13.303851244936345  0.9492399902705438 12.493002672272864  C 0.7247148073167852 11.681060169841217  0.6113626215964096 10.842413993879472  0.6122834643735529 10  C 0.6122834643735529 9.149848167715568  0.7249360963903353 8.31797109450955  0.9492399902705438 7.506997327727141  C 1.174795587854456 6.696023578920717  1.4882202237774889 5.945006045138888  1.892392833622685 5.259201891963251  C 2.295438897931134 4.570769155237268  2.786103685510706 3.9396641051793986  3.362134123770254 3.3633833153935173  C 3.9397917746672455 2.78447397902199  4.5716478433521415 2.2943098765190975  5.2584533671875 1.8926406735749417  C 5.946385418330439 1.488468081705729  6.695525406177661 1.1730407234519677  7.506624330367478 0.94873681108941  C 8.318728305048658 0.7244303682415644  9.157491278218451 0.6112478984308737  10.000002511827258 0.6122809710286458  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 5 5 )" class="stroke" mask="url(#u41_img_cl13)"></path>
  </g>
                </svg>
                <div id="u41_text" class="text " style="display:none; visibility: hidden">
                  <p></p>
                </div>
              </div>

              <!-- Unnamed (Rectangle) -->
              <div id="u42" class="ax_default _形状 transition">
                <div id="u42_div" class=""></div>
                <div id="u42_text" class="text " style="display:none; visibility: hidden">
                  <p></p>
                </div>
              </div>
            </div>

            <!-- Unnamed (Shape) -->
            <div id="u43" class="ax_default _形状 transition">
              <svg data="http://***********/gsc/VBQ4F3/1c/74/19/1c74195a2a8d4aacad90f09e97e1cc57/images/index/u43.svg" id="u43_img" class="img generatedImage">

  <defs>
    <pattern id="u43_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imagerepeat="None"></pattern>
    <mask fill="white" id="u43_img_cl14">
      <path d="M 16.037315300697546 3.1873806944754475  C 16.2474978031529 3.1873806944754475  16.429622860602677 3.2649731754464284  16.582945585435265 3.4175510126674107  C 16.737013197879463 3.570253003683036  16.813612501227677 3.7538678366071427  16.813612501227677 3.963305433370535  C 16.813612501227677 4.178081398465403  16.73701321595982 4.361572095424107  16.582945585435265 4.514274086439732  L 15.486222511662946 5.610873023995536  C 15.337244959458706 5.760843753822545  15.155492345814732 5.835829127650669  14.94071636263951 5.835829127650669  C 14.71787076827567 5.835829127650669  14.533883491294642 5.760843753822543  14.386023288839286 5.61608723828125  C 14.238287222349332 5.468599461551339  14.165039914006694 5.282377521484375  14.165039914006694 5.059904370926338  C 14.165039914006694 4.845252542047991  14.239280400223214 4.664244816015625  14.388878685993303 4.514274086439732  L 15.485477623800218 3.417551012667409  C 15.639669390290173 3.264973175697544  15.82316006941964 3.1873806944754444  16.037439454492187 3.1873806944754444  Z M 16.43396803323103 9.451638454324776  C 16.58629758018973 9.299060617354911  16.76891923574219 9.22407524327567  16.983074466517856 9.22407524327567  L 18.534923962137274 9.22407524327567  C 18.749079193415174 9.22407524327567  18.93219742873884 9.299060617103796  19.083285508063614 9.451638454324776  C 19.235615055022322 9.604340445089287  19.311097008872768 9.785348171121651  19.311097008872768 10  C 19.311097008872768 10.214775965094864  19.235615055022322 10.39826666205357  19.083285508063614 10.548361545675224  C 18.93219744656808 10.700939382645089  18.749079193415174 10.77592475672433  18.534923962137274 10.77592475672433  L 16.983074466517856 10.77592475672433  C 16.768919235239956 10.77592475672433  16.58629758018973 10.700939382896204  16.43396803323103 10.548361545675224  C 16.28287997173549 10.398266661802452  16.206528976227677 10.214651828878349  16.206528976227677 10  C 16.206528976227677 9.785224034905132  16.282879971735493 9.604216309123885  16.43396803323103 9.451638454324776  Z M 10 0.6889029911272322  C 10.2141552312779 0.6889029911272322  10.396528578487722 0.7664954720982144  10.548733989732144 0.9164662021763397  C 10.699946205273442 1.069168193191965  10.77617304673549 1.2527830261160715  10.77617304673549 1.464827747851563  L 10.77617304673549 3.016677243219866  C 10.77617304673549 3.2314532083147323  10.699946205273442 3.4149439052734385  10.548858125697544 3.565038789146206  C 10.396528578738842 3.717616626116072  10.214155231026787 3.7952091073381706  10 3.7952091070870546  C 9.785596478710938 3.7952091070870546  9.603223113671875 3.717616626116072  9.450893566462053 3.565038789146206  C 9.299805504966516 3.4149439052734385  9.223578645424107 3.231329072349331  9.223578663504462 3.016677243219866  L 9.223578663504462 1.4648277478515626  C 9.223578663504462 1.252783026116071  9.299805504966516 1.0690440569754462  9.450893566462053 0.9164662021763391  C 9.603347267466518 0.766495472098214  9.785720614927456 0.6889029911272322  10.000124153794642 0.6889029911272322  Z M 3.9685196474051336 3.1873806944754475  C 4.178577995814731 3.1873806944754475  4.360578899469865 3.2649731754464284  4.514522376199777 3.4175510126674107  L 5.611121314006695 4.514274086439733  C 5.765313080496651 4.6668519234096  5.841415785993302 4.850466756584823  5.841415785993302 5.059904370926339  C 5.841415785993302 5.274680336021207  5.765809678097097 5.4582951689453125  5.613976711160714 5.6082659168526785  C 5.4625162056082575 5.760967907868304  5.279894550558035 5.835829127901785  5.065242739257813 5.83582912765067  C 4.846866471316964 5.83582912765067  4.662755058621651 5.760843753822545  4.514025796428571 5.613480131138394  L 3.417054414564732 4.516757039285715  C 3.267083684737724 4.366786309458706  3.192843198772322 4.183171476283482  3.192843198772322 3.963305433370537  C 3.192843198772322 3.748529468275671  3.2683251526227686 3.567521742494421  3.420654699581474 3.414943905524555  C 3.571742761077009 3.2649731756975457  3.7548610142299124 3.1873806944754475  3.9695128431082596 3.1873806944754475  Z M 14.94071636263951 14.158956658063618  C 15.150774711049106 14.158956658063618  15.332651478738837 14.23394203189174  15.486222511662946 14.389126976004466  L 16.58294558518415 15.485850067606023  C 16.737013197628347 15.641035011718749  16.81361250097656 15.822042737751117  16.813612501227677 16.03669456662946  C 16.813612501227677 16.24625631743861  16.73701321595982 16.42987115036272  16.58294558518415 16.582448987332587  C 16.429498706556917 16.73763393144531  16.247497802901783 16.812619305524553  16.037439454492187 16.812619305524553  C 15.823284223214284 16.812619305524553  15.639669390290177 16.737633931696426  15.485477623800218 16.582448987332587  L 14.388878685993303 15.485850067606023  C 14.239280400223214 15.338362290876116  14.165039914006694 15.154747457952007  14.165039914006694 14.94009562907366  C 14.165039914006694 14.725319663978794  14.2406460219029 14.541704831054687  14.392478970758928 14.389126976004466  C 14.543939476311383 14.236549139034597  14.727181865429683 14.1589566578125  14.941212960742188 14.158956658063618  Z M 10.000124153794642 6.896300991183036  C 9.143254939174106 6.896300991183036  8.41252004578683 7.19897371202567  7.805560657003348 7.806677988671876  C 7.199470309626115 8.411899294140625  6.896176855217634 9.143875673242187  6.896176855217634 10  C 6.896176855217634 10.85612432700893  7.199470327957588 11.588100705859375  7.805560657003348 12.195929118470982  C 8.412644182003348 12.800902134179688  9.143254921344866 13.103699008816964  10 13.103699008816964  C 10.856869214620538 13.103699008816964  11.587852415848214 12.80102628797433  12.194935940848215 12.19580496467634  C 12.800405536328125 11.588224859654018  13.103699008816964 10.856248481054688  13.103699008816964 10  C 13.103699008816964 9.14387567299107  12.800405536077012 8.411899294140625  12.194935940848215 7.806677988671876  C 11.587852415848214 7.19897372985491  10.856869214620538 6.896300991183036  10 6.896300991183036  Z M 0.9167144919363837 9.451638454324776  C 1.0681749974888393 9.299060617354911  1.2509208065848214 9.22407524327567  1.465076037862723 9.22407524327567  L 3.0169255334821434 9.22407524327567  C 3.2313290547712055 9.22407524327567  3.413702419810268 9.299060617103796  3.5660319667689726 9.451638454324776  C 3.7171200282645094 9.6043404453404  3.7934710237723213 9.785348171121651  3.7934710237723213 10  C 3.7934710237723213 10.214775965094864  3.7171200282645094 10.39826666205357  3.5660319667689726 10.548361545675224  C 3.413702419810268 10.700939382645089  3.231329072098215 10.77592475672433  3.0169255334821434 10.77592475672433  L 1.465076037862723 10.77592475672433  C 1.2509208065848214 10.77592475672433  1.0680508612723218 10.700939382896204  0.9167144919363837 10.548361545675224  C 0.7643849449776787 10.39826666205357  0.6889029911272322 10.214651828878349  0.6889029911272322 10  C 0.6889029911272322 9.785224034905132  0.7643849449776795 9.604216309123885  0.9167144919363837 9.451638454324776  Z M 10.000124153794642 16.207398000055804  C 10.214279385072544 16.207398000055804  10.396652732282366 16.282383373883928  10.548858125697544 16.434961210602676  C 10.700070341238842 16.5876632016183  10.77629718270089 16.76867092739955  10.776297200530134 16.98332275678013  L 10.776297200530134 18.53517225214844  C 10.776297200530134 18.7499482172433  10.700070359068082 18.933438914202007  10.548982279492186 19.08353379782366  C 10.396652732533482 19.236111635044644  10.214279385072548 19.311097008872768  10 19.311097008872768  C 9.785596478710938 19.311097008872768  9.603223113671875 19.236111635044644  9.450893566462053 19.08353379782366  C 9.299805504966516 18.933438913950894  9.223578645424107 18.749824081026784  9.223578663504462 18.53517225214844  L 9.223578663504462 16.983322756780133  C 9.223578663504462 16.768546791685267  9.299805504966516 16.58753906590402  9.450893566462053 16.434961210853796  C 9.603223113420759 16.282383373883928  9.78559646113281 16.207397999804687  10 16.207398000055807  Z M 5.066235916880579 14.158956658063616  C 5.2797704140904 14.158956658063616  5.463012803208703 14.233942031891738  5.614845752566962 14.389126976004462  C 5.766306258119417 14.541828967020084  5.842657253376115 14.725443799944193  5.842657253376115 14.940095629073657  C 5.842657253376115 15.152140350809146  5.765313080245535 15.333148076841514  5.611742047572542 15.485725913560263  L 4.515018974051337 16.582448987332587  C 4.361696249218747 16.73763393144531  4.179819481780131 16.81261930552455  3.9695128431082565 16.81261930552455  C 3.7548610142299093 16.81261930552455  3.5717427789062484 16.737633931696422  3.4207788536272306 16.587539065652898  C 3.268325152622766 16.434961228683033  3.1928431987723194 16.25134639550781  3.1928431987723194 16.03669456662946  C 3.1928431987723194 15.819435630357138  3.2670836849888376 15.63569666146763  3.417054414815847 15.485725913560263  L 4.51402579642857 14.389126976004462  C 4.66734852126116 14.233942031891738  4.851584106082588 14.158956657812498  5.065242739257812 14.158956658063616  Z M 7.663162940513394 5.967798397237727  C 8.376641279352677 5.553889110407367  9.155545569196429 5.344451495563617  10 5.344451495563617  C 10.844454430803571 5.344451495563617  11.623482874441963 5.554013246372769  12.336588751395091 5.967674261272321  C 13.050439515959821 6.386797780468749  13.615312746400669 6.950553661662946  14.030711827287947 7.66440442622768  C 14.446855795535715 8.37577223794643  14.655176060630579 9.154179947767858  14.655176060630579 10  C 14.655176060630579 10.845820052232146  14.447352375306918 11.624227762053572  14.030711827287947 12.338078526869419  C 13.614319569029018 13.049446338588169  13.049198048577008 13.613202219280131  12.336588751395091 14.032201602762274  C 11.624351898270088 14.448593861021203  10.845571744391743 14.655548504436384  10 14.655548504436384  C 9.154552391573661 14.655548504436384  8.376020527706473 14.448593860770092  7.663162940513394 14.032325738727682  C 6.950553643582591 13.613202219531251  6.385928720982143 13.049322184542412  5.969039882952009 12.338078526869422  C 5.55264762469308 11.624227762304692  5.344575649609374 10.845820052232147  5.344575649358259 10.000000000000002  C 5.344575649358259 9.154179947767862  5.553392512304686 8.375772237946432  5.969039882952009 7.664404426227681  C 6.384687253348213 6.9505536616629495  6.949312175948661 6.386797762639512  7.663162940513394 5.967798397237727  Z " fill-rule="evenodd"></path>
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -40 -5 )">
    <path d="M 16.037315300697546 3.1873806944754475  C 16.2474978031529 3.1873806944754475  16.429622860602677 3.2649731754464284  16.582945585435265 3.4175510126674107  C 16.737013197879463 3.570253003683036  16.813612501227677 3.7538678366071427  16.813612501227677 3.963305433370535  C 16.813612501227677 4.178081398465403  16.73701321595982 4.361572095424107  16.582945585435265 4.514274086439732  L 15.486222511662946 5.610873023995536  C 15.337244959458706 5.760843753822545  15.155492345814732 5.835829127650669  14.94071636263951 5.835829127650669  C 14.71787076827567 5.835829127650669  14.533883491294642 5.760843753822543  14.386023288839286 5.61608723828125  C 14.238287222349332 5.468599461551339  14.165039914006694 5.282377521484375  14.165039914006694 5.059904370926338  C 14.165039914006694 4.845252542047991  14.239280400223214 4.664244816015625  14.388878685993303 4.514274086439732  L 15.485477623800218 3.417551012667409  C 15.639669390290173 3.264973175697544  15.82316006941964 3.1873806944754444  16.037439454492187 3.1873806944754444  Z M 16.43396803323103 9.451638454324776  C 16.58629758018973 9.299060617354911  16.76891923574219 9.22407524327567  16.983074466517856 9.22407524327567  L 18.534923962137274 9.22407524327567  C 18.749079193415174 9.22407524327567  18.93219742873884 9.299060617103796  19.083285508063614 9.451638454324776  C 19.235615055022322 9.604340445089287  19.311097008872768 9.785348171121651  19.311097008872768 10  C 19.311097008872768 10.214775965094864  19.235615055022322 10.39826666205357  19.083285508063614 10.548361545675224  C 18.93219744656808 10.700939382645089  18.749079193415174 10.77592475672433  18.534923962137274 10.77592475672433  L 16.983074466517856 10.77592475672433  C 16.768919235239956 10.77592475672433  16.58629758018973 10.700939382896204  16.43396803323103 10.548361545675224  C 16.28287997173549 10.398266661802452  16.206528976227677 10.214651828878349  16.206528976227677 10  C 16.206528976227677 9.785224034905132  16.282879971735493 9.604216309123885  16.43396803323103 9.451638454324776  Z M 10 0.6889029911272322  C 10.2141552312779 0.6889029911272322  10.396528578487722 0.7664954720982144  10.548733989732144 0.9164662021763397  C 10.699946205273442 1.069168193191965  10.77617304673549 1.2527830261160715  10.77617304673549 1.464827747851563  L 10.77617304673549 3.016677243219866  C 10.77617304673549 3.2314532083147323  10.699946205273442 3.4149439052734385  10.548858125697544 3.565038789146206  C 10.396528578738842 3.717616626116072  10.214155231026787 3.7952091073381706  10 3.7952091070870546  C 9.785596478710938 3.7952091070870546  9.603223113671875 3.717616626116072  9.450893566462053 3.565038789146206  C 9.299805504966516 3.4149439052734385  9.223578645424107 3.231329072349331  9.223578663504462 3.016677243219866  L 9.223578663504462 1.4648277478515626  C 9.223578663504462 1.252783026116071  9.299805504966516 1.0690440569754462  9.450893566462053 0.9164662021763391  C 9.603347267466518 0.766495472098214  9.785720614927456 0.6889029911272322  10.000124153794642 0.6889029911272322  Z M 3.9685196474051336 3.1873806944754475  C 4.178577995814731 3.1873806944754475  4.360578899469865 3.2649731754464284  4.514522376199777 3.4175510126674107  L 5.611121314006695 4.514274086439733  C 5.765313080496651 4.6668519234096  5.841415785993302 4.850466756584823  5.841415785993302 5.059904370926339  C 5.841415785993302 5.274680336021207  5.765809678097097 5.4582951689453125  5.613976711160714 5.6082659168526785  C 5.4625162056082575 5.760967907868304  5.279894550558035 5.835829127901785  5.065242739257813 5.83582912765067  C 4.846866471316964 5.83582912765067  4.662755058621651 5.760843753822545  4.514025796428571 5.613480131138394  L 3.417054414564732 4.516757039285715  C 3.267083684737724 4.366786309458706  3.192843198772322 4.183171476283482  3.192843198772322 3.963305433370537  C 3.192843198772322 3.748529468275671  3.2683251526227686 3.567521742494421  3.420654699581474 3.414943905524555  C 3.571742761077009 3.2649731756975457  3.7548610142299124 3.1873806944754475  3.9695128431082596 3.1873806944754475  Z M 14.94071636263951 14.158956658063618  C 15.150774711049106 14.158956658063618  15.332651478738837 14.23394203189174  15.486222511662946 14.389126976004466  L 16.58294558518415 15.485850067606023  C 16.737013197628347 15.641035011718749  16.81361250097656 15.822042737751117  16.813612501227677 16.03669456662946  C 16.813612501227677 16.24625631743861  16.73701321595982 16.42987115036272  16.58294558518415 16.582448987332587  C 16.429498706556917 16.73763393144531  16.247497802901783 16.812619305524553  16.037439454492187 16.812619305524553  C 15.823284223214284 16.812619305524553  15.639669390290177 16.737633931696426  15.485477623800218 16.582448987332587  L 14.388878685993303 15.485850067606023  C 14.239280400223214 15.338362290876116  14.165039914006694 15.154747457952007  14.165039914006694 14.94009562907366  C 14.165039914006694 14.725319663978794  14.2406460219029 14.541704831054687  14.392478970758928 14.389126976004466  C 14.543939476311383 14.236549139034597  14.727181865429683 14.1589566578125  14.941212960742188 14.158956658063618  Z M 10.000124153794642 6.896300991183036  C 9.143254939174106 6.896300991183036  8.41252004578683 7.19897371202567  7.805560657003348 7.806677988671876  C 7.199470309626115 8.411899294140625  6.896176855217634 9.143875673242187  6.896176855217634 10  C 6.896176855217634 10.85612432700893  7.199470327957588 11.588100705859375  7.805560657003348 12.195929118470982  C 8.412644182003348 12.800902134179688  9.143254921344866 13.103699008816964  10 13.103699008816964  C 10.856869214620538 13.103699008816964  11.587852415848214 12.80102628797433  12.194935940848215 12.19580496467634  C 12.800405536328125 11.588224859654018  13.103699008816964 10.856248481054688  13.103699008816964 10  C 13.103699008816964 9.14387567299107  12.800405536077012 8.411899294140625  12.194935940848215 7.806677988671876  C 11.587852415848214 7.19897372985491  10.856869214620538 6.896300991183036  10 6.896300991183036  Z M 0.9167144919363837 9.451638454324776  C 1.0681749974888393 9.299060617354911  1.2509208065848214 9.22407524327567  1.465076037862723 9.22407524327567  L 3.0169255334821434 9.22407524327567  C 3.2313290547712055 9.22407524327567  3.413702419810268 9.299060617103796  3.5660319667689726 9.451638454324776  C 3.7171200282645094 9.6043404453404  3.7934710237723213 9.785348171121651  3.7934710237723213 10  C 3.7934710237723213 10.214775965094864  3.7171200282645094 10.39826666205357  3.5660319667689726 10.548361545675224  C 3.413702419810268 10.700939382645089  3.231329072098215 10.77592475672433  3.0169255334821434 10.77592475672433  L 1.465076037862723 10.77592475672433  C 1.2509208065848214 10.77592475672433  1.0680508612723218 10.700939382896204  0.9167144919363837 10.548361545675224  C 0.7643849449776787 10.39826666205357  0.6889029911272322 10.214651828878349  0.6889029911272322 10  C 0.6889029911272322 9.785224034905132  0.7643849449776795 9.604216309123885  0.9167144919363837 9.451638454324776  Z M 10.000124153794642 16.207398000055804  C 10.214279385072544 16.207398000055804  10.396652732282366 16.282383373883928  10.548858125697544 16.434961210602676  C 10.700070341238842 16.5876632016183  10.77629718270089 16.76867092739955  10.776297200530134 16.98332275678013  L 10.776297200530134 18.53517225214844  C 10.776297200530134 18.7499482172433  10.700070359068082 18.933438914202007  10.548982279492186 19.08353379782366  C 10.396652732533482 19.236111635044644  10.214279385072548 19.311097008872768  10 19.311097008872768  C 9.785596478710938 19.311097008872768  9.603223113671875 19.236111635044644  9.450893566462053 19.08353379782366  C 9.299805504966516 18.933438913950894  9.223578645424107 18.749824081026784  9.223578663504462 18.53517225214844  L 9.223578663504462 16.983322756780133  C 9.223578663504462 16.768546791685267  9.299805504966516 16.58753906590402  9.450893566462053 16.434961210853796  C 9.603223113420759 16.282383373883928  9.78559646113281 16.207397999804687  10 16.207398000055807  Z M 5.066235916880579 14.158956658063616  C 5.2797704140904 14.158956658063616  5.463012803208703 14.233942031891738  5.614845752566962 14.389126976004462  C 5.766306258119417 14.541828967020084  5.842657253376115 14.725443799944193  5.842657253376115 14.940095629073657  C 5.842657253376115 15.152140350809146  5.765313080245535 15.333148076841514  5.611742047572542 15.485725913560263  L 4.515018974051337 16.582448987332587  C 4.361696249218747 16.73763393144531  4.179819481780131 16.81261930552455  3.9695128431082565 16.81261930552455  C 3.7548610142299093 16.81261930552455  3.5717427789062484 16.737633931696422  3.4207788536272306 16.587539065652898  C 3.268325152622766 16.434961228683033  3.1928431987723194 16.25134639550781  3.1928431987723194 16.03669456662946  C 3.1928431987723194 15.819435630357138  3.2670836849888376 15.63569666146763  3.417054414815847 15.485725913560263  L 4.51402579642857 14.389126976004462  C 4.66734852126116 14.233942031891738  4.851584106082588 14.158956657812498  5.065242739257812 14.158956658063616  Z M 7.663162940513394 5.967798397237727  C 8.376641279352677 5.553889110407367  9.155545569196429 5.344451495563617  10 5.344451495563617  C 10.844454430803571 5.344451495563617  11.623482874441963 5.554013246372769  12.336588751395091 5.967674261272321  C 13.050439515959821 6.386797780468749  13.615312746400669 6.950553661662946  14.030711827287947 7.66440442622768  C 14.446855795535715 8.37577223794643  14.655176060630579 9.154179947767858  14.655176060630579 10  C 14.655176060630579 10.845820052232146  14.447352375306918 11.624227762053572  14.030711827287947 12.338078526869419  C 13.614319569029018 13.049446338588169  13.049198048577008 13.613202219280131  12.336588751395091 14.032201602762274  C 11.624351898270088 14.448593861021203  10.845571744391743 14.655548504436384  10 14.655548504436384  C 9.154552391573661 14.655548504436384  8.376020527706473 14.448593860770092  7.663162940513394 14.032325738727682  C 6.950553643582591 13.613202219531251  6.385928720982143 13.049322184542412  5.969039882952009 12.338078526869422  C 5.55264762469308 11.624227762304692  5.344575649609374 10.845820052232147  5.344575649358259 10.000000000000002  C 5.344575649358259 9.154179947767862  5.553392512304686 8.375772237946432  5.969039882952009 7.664404426227681  C 6.384687253348213 6.9505536616629495  6.949312175948661 6.386797762639512  7.663162940513394 5.967798397237727  Z " fill-rule="nonzero" fill="rgba(30, 30, 30, 1)" stroke="none" transform="matrix(1 0 0 1 40 5 )" class="fill"></path>
    <path d="M 16.037315300697546 3.1873806944754475  C 16.2474978031529 3.1873806944754475  16.429622860602677 3.2649731754464284  16.582945585435265 3.4175510126674107  C 16.737013197879463 3.570253003683036  16.813612501227677 3.7538678366071427  16.813612501227677 3.963305433370535  C 16.813612501227677 4.178081398465403  16.73701321595982 4.361572095424107  16.582945585435265 4.514274086439732  L 15.486222511662946 5.610873023995536  C 15.337244959458706 5.760843753822545  15.155492345814732 5.835829127650669  14.94071636263951 5.835829127650669  C 14.71787076827567 5.835829127650669  14.533883491294642 5.760843753822543  14.386023288839286 5.61608723828125  C 14.238287222349332 5.468599461551339  14.165039914006694 5.282377521484375  14.165039914006694 5.059904370926338  C 14.165039914006694 4.845252542047991  14.239280400223214 4.664244816015625  14.388878685993303 4.514274086439732  L 15.485477623800218 3.417551012667409  C 15.639669390290173 3.264973175697544  15.82316006941964 3.1873806944754444  16.037439454492187 3.1873806944754444  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 40 5 )" class="stroke" mask="url(#u43_img_cl14)"></path>
    <path d="M 16.43396803323103 9.451638454324776  C 16.58629758018973 9.299060617354911  16.76891923574219 9.22407524327567  16.983074466517856 9.22407524327567  L 18.534923962137274 9.22407524327567  C 18.749079193415174 9.22407524327567  18.93219742873884 9.299060617103796  19.083285508063614 9.451638454324776  C 19.235615055022322 9.604340445089287  19.311097008872768 9.785348171121651  19.311097008872768 10  C 19.311097008872768 10.214775965094864  19.235615055022322 10.39826666205357  19.083285508063614 10.548361545675224  C 18.93219744656808 10.700939382645089  18.749079193415174 10.77592475672433  18.534923962137274 10.77592475672433  L 16.983074466517856 10.77592475672433  C 16.768919235239956 10.77592475672433  16.58629758018973 10.700939382896204  16.43396803323103 10.548361545675224  C 16.28287997173549 10.398266661802452  16.206528976227677 10.214651828878349  16.206528976227677 10  C 16.206528976227677 9.785224034905132  16.282879971735493 9.604216309123885  16.43396803323103 9.451638454324776  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 40 5 )" class="stroke" mask="url(#u43_img_cl14)"></path>
    <path d="M 10 0.6889029911272322  C 10.2141552312779 0.6889029911272322  10.396528578487722 0.7664954720982144  10.548733989732144 0.9164662021763397  C 10.699946205273442 1.069168193191965  10.77617304673549 1.2527830261160715  10.77617304673549 1.464827747851563  L 10.77617304673549 3.016677243219866  C 10.77617304673549 3.2314532083147323  10.699946205273442 3.4149439052734385  10.548858125697544 3.565038789146206  C 10.396528578738842 3.717616626116072  10.214155231026787 3.7952091073381706  10 3.7952091070870546  C 9.785596478710938 3.7952091070870546  9.603223113671875 3.717616626116072  9.450893566462053 3.565038789146206  C 9.299805504966516 3.4149439052734385  9.223578645424107 3.231329072349331  9.223578663504462 3.016677243219866  L 9.223578663504462 1.4648277478515626  C 9.223578663504462 1.252783026116071  9.299805504966516 1.0690440569754462  9.450893566462053 0.9164662021763391  C 9.603347267466518 0.766495472098214  9.785720614927456 0.6889029911272322  10.000124153794642 0.6889029911272322  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 40 5 )" class="stroke" mask="url(#u43_img_cl14)"></path>
    <path d="M 3.9685196474051336 3.1873806944754475  C 4.178577995814731 3.1873806944754475  4.360578899469865 3.2649731754464284  4.514522376199777 3.4175510126674107  L 5.611121314006695 4.514274086439733  C 5.765313080496651 4.6668519234096  5.841415785993302 4.850466756584823  5.841415785993302 5.059904370926339  C 5.841415785993302 5.274680336021207  5.765809678097097 5.4582951689453125  5.613976711160714 5.6082659168526785  C 5.4625162056082575 5.760967907868304  5.279894550558035 5.835829127901785  5.065242739257813 5.83582912765067  C 4.846866471316964 5.83582912765067  4.662755058621651 5.760843753822545  4.514025796428571 5.613480131138394  L 3.417054414564732 4.516757039285715  C 3.267083684737724 4.366786309458706  3.192843198772322 4.183171476283482  3.192843198772322 3.963305433370537  C 3.192843198772322 3.748529468275671  3.2683251526227686 3.567521742494421  3.420654699581474 3.414943905524555  C 3.571742761077009 3.2649731756975457  3.7548610142299124 3.1873806944754475  3.9695128431082596 3.1873806944754475  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 40 5 )" class="stroke" mask="url(#u43_img_cl14)"></path>
    <path d="M 14.94071636263951 14.158956658063618  C 15.150774711049106 14.158956658063618  15.332651478738837 14.23394203189174  15.486222511662946 14.389126976004466  L 16.58294558518415 15.485850067606023  C 16.737013197628347 15.641035011718749  16.81361250097656 15.822042737751117  16.813612501227677 16.03669456662946  C 16.813612501227677 16.24625631743861  16.73701321595982 16.42987115036272  16.58294558518415 16.582448987332587  C 16.429498706556917 16.73763393144531  16.247497802901783 16.812619305524553  16.037439454492187 16.812619305524553  C 15.823284223214284 16.812619305524553  15.639669390290177 16.737633931696426  15.485477623800218 16.582448987332587  L 14.388878685993303 15.485850067606023  C 14.239280400223214 15.338362290876116  14.165039914006694 15.154747457952007  14.165039914006694 14.94009562907366  C 14.165039914006694 14.725319663978794  14.2406460219029 14.541704831054687  14.392478970758928 14.389126976004466  C 14.543939476311383 14.236549139034597  14.727181865429683 14.1589566578125  14.941212960742188 14.158956658063618  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 40 5 )" class="stroke" mask="url(#u43_img_cl14)"></path>
    <path d="M 10.000124153794642 6.896300991183036  C 9.143254939174106 6.896300991183036  8.41252004578683 7.19897371202567  7.805560657003348 7.806677988671876  C 7.199470309626115 8.411899294140625  6.896176855217634 9.143875673242187  6.896176855217634 10  C 6.896176855217634 10.85612432700893  7.199470327957588 11.588100705859375  7.805560657003348 12.195929118470982  C 8.412644182003348 12.800902134179688  9.143254921344866 13.103699008816964  10 13.103699008816964  C 10.856869214620538 13.103699008816964  11.587852415848214 12.80102628797433  12.194935940848215 12.19580496467634  C 12.800405536328125 11.588224859654018  13.103699008816964 10.856248481054688  13.103699008816964 10  C 13.103699008816964 9.14387567299107  12.800405536077012 8.411899294140625  12.194935940848215 7.806677988671876  C 11.587852415848214 7.19897372985491  10.856869214620538 6.896300991183036  10 6.896300991183036  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 40 5 )" class="stroke" mask="url(#u43_img_cl14)"></path>
    <path d="M 0.9167144919363837 9.451638454324776  C 1.0681749974888393 9.299060617354911  1.2509208065848214 9.22407524327567  1.465076037862723 9.22407524327567  L 3.0169255334821434 9.22407524327567  C 3.2313290547712055 9.22407524327567  3.413702419810268 9.299060617103796  3.5660319667689726 9.451638454324776  C 3.7171200282645094 9.6043404453404  3.7934710237723213 9.785348171121651  3.7934710237723213 10  C 3.7934710237723213 10.214775965094864  3.7171200282645094 10.39826666205357  3.5660319667689726 10.548361545675224  C 3.413702419810268 10.700939382645089  3.231329072098215 10.77592475672433  3.0169255334821434 10.77592475672433  L 1.465076037862723 10.77592475672433  C 1.2509208065848214 10.77592475672433  1.0680508612723218 10.700939382896204  0.9167144919363837 10.548361545675224  C 0.7643849449776787 10.39826666205357  0.6889029911272322 10.214651828878349  0.6889029911272322 10  C 0.6889029911272322 9.785224034905132  0.7643849449776795 9.604216309123885  0.9167144919363837 9.451638454324776  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 40 5 )" class="stroke" mask="url(#u43_img_cl14)"></path>
    <path d="M 10.000124153794642 16.207398000055804  C 10.214279385072544 16.207398000055804  10.396652732282366 16.282383373883928  10.548858125697544 16.434961210602676  C 10.700070341238842 16.5876632016183  10.77629718270089 16.76867092739955  10.776297200530134 16.98332275678013  L 10.776297200530134 18.53517225214844  C 10.776297200530134 18.7499482172433  10.700070359068082 18.933438914202007  10.548982279492186 19.08353379782366  C 10.396652732533482 19.236111635044644  10.214279385072548 19.311097008872768  10 19.311097008872768  C 9.785596478710938 19.311097008872768  9.603223113671875 19.236111635044644  9.450893566462053 19.08353379782366  C 9.299805504966516 18.933438913950894  9.223578645424107 18.749824081026784  9.223578663504462 18.53517225214844  L 9.223578663504462 16.983322756780133  C 9.223578663504462 16.768546791685267  9.299805504966516 16.58753906590402  9.450893566462053 16.434961210853796  C 9.603223113420759 16.282383373883928  9.78559646113281 16.207397999804687  10 16.207398000055807  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 40 5 )" class="stroke" mask="url(#u43_img_cl14)"></path>
    <path d="M 5.066235916880579 14.158956658063616  C 5.2797704140904 14.158956658063616  5.463012803208703 14.233942031891738  5.614845752566962 14.389126976004462  C 5.766306258119417 14.541828967020084  5.842657253376115 14.725443799944193  5.842657253376115 14.940095629073657  C 5.842657253376115 15.152140350809146  5.765313080245535 15.333148076841514  5.611742047572542 15.485725913560263  L 4.515018974051337 16.582448987332587  C 4.361696249218747 16.73763393144531  4.179819481780131 16.81261930552455  3.9695128431082565 16.81261930552455  C 3.7548610142299093 16.81261930552455  3.5717427789062484 16.737633931696422  3.4207788536272306 16.587539065652898  C 3.268325152622766 16.434961228683033  3.1928431987723194 16.25134639550781  3.1928431987723194 16.03669456662946  C 3.1928431987723194 15.819435630357138  3.2670836849888376 15.63569666146763  3.417054414815847 15.485725913560263  L 4.51402579642857 14.389126976004462  C 4.66734852126116 14.233942031891738  4.851584106082588 14.158956657812498  5.065242739257812 14.158956658063616  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 40 5 )" class="stroke" mask="url(#u43_img_cl14)"></path>
    <path d="M 7.663162940513394 5.967798397237727  C 8.376641279352677 5.553889110407367  9.155545569196429 5.344451495563617  10 5.344451495563617  C 10.844454430803571 5.344451495563617  11.623482874441963 5.554013246372769  12.336588751395091 5.967674261272321  C 13.050439515959821 6.386797780468749  13.615312746400669 6.950553661662946  14.030711827287947 7.66440442622768  C 14.446855795535715 8.37577223794643  14.655176060630579 9.154179947767858  14.655176060630579 10  C 14.655176060630579 10.845820052232146  14.447352375306918 11.624227762053572  14.030711827287947 12.338078526869419  C 13.614319569029018 13.049446338588169  13.049198048577008 13.613202219280131  12.336588751395091 14.032201602762274  C 11.624351898270088 14.448593861021203  10.845571744391743 14.655548504436384  10 14.655548504436384  C 9.154552391573661 14.655548504436384  8.376020527706473 14.448593860770092  7.663162940513394 14.032325738727682  C 6.950553643582591 13.613202219531251  6.385928720982143 13.049322184542412  5.969039882952009 12.338078526869422  C 5.55264762469308 11.624227762304692  5.344575649609374 10.845820052232147  5.344575649358259 10.000000000000002  C 5.344575649358259 9.154179947767862  5.553392512304686 8.375772237946432  5.969039882952009 7.664404426227681  C 6.384687253348213 6.9505536616629495  6.949312175948661 6.386797762639512  7.663162940513394 5.967798397237727  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 40 5 )" class="stroke" mask="url(#u43_img_cl14)"></path>
  </g>
              </svg>
              <div id="u43_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (Group) -->
      <div id="u44" class="ax_default" data-left="14" data-top="337" data-width="220" data-height="40" layer-opacity="1" style="cursor: pointer;">

        <!-- Unnamed (Rectangle) -->
        <div id="u45" class="ax_default box_1 transition" selectiongroup="菜单">
          <div id="u45_div" class="" tabindex="0"></div>
          <div id="u45_text" class="text ">
            <p id="cache5" style=""><span id="cache6" style="">报告管理</span></p>
          </div>
        </div>

        <!-- Unnamed (Rectangle) -->
        <div id="u46" class="ax_default _形状 transition" selectiongroup="二级菜单">
          <div id="u46_div" class=""></div>
          <div id="u46_text" class="text " style="display:none; visibility: hidden">
            <p id="cache8" style=""></p>
          </div>
        </div>

        <!-- Unnamed (Group) -->
        <div id="u47" class="ax_default" data-left="32" data-top="350" data-width="15" data-height="15" layer-opacity="1">

          <!-- Unnamed (Group) -->
          <div id="u48" class="ax_default" data-left="32" data-top="350" data-width="15" data-height="15" layer-opacity="1">

            <!-- Unnamed (Shape) -->
            <div id="u49" class="ax_default _形状 transition">
              <svg data="http://***********/gsc/VBQ4F3/1c/74/19/1c74195a2a8d4aacad90f09e97e1cc57/images/index/u49.svg" id="u49_img" class="img generatedImage">

  <defs>
    <pattern id="u49_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imagerepeat="None"></pattern>
    <mask fill="white" id="u49_img_cl15">
      <path d="M 11.079433593749997 0.34092773437498636  C 12.72416015625 0.34092773437498636  14.062294921875 1.6792675781250068  14 3.3237890624999693  L 14 13.465927734374986  C 14.0625 14.1240234375  13.527451171874997 14.659072265625014  12.869355468750005 14.659072265625014  L 3.920361328124999 14.659072265625014  C 2.275634765625 14.659072265625014  0.9375 13.320732421874993  1 11.67621093750003  L 1 1.5340722656250136  C 0.9375 0.8759765625  1.472548828125003 0.34092773437498636  2.1306445312500006 0.34092773437498636  L 11.079433593749997 0.34092773437498636  Z M 12.870175781250001 13.465927734374986  L 12.869355468750005 3.3237890624999693  C 12.869355468750005 2.336748046874959  12.066474609375007 1.5340722656250136  11.079638671875006 1.5340722656250136  L 2.1306445312500006 1.5340722656250136  L 2.1306445312500006 11.67621093750003  C 2.1306445312500006 12.663251953125041  2.933525390625004 13.465927734374986  3.9203613281250043 13.465927734374986  L 12.870175781250001 13.465927734374986  Z M 4.788251953124998 9.090791015624973  L 8.042431640625004 9.090791015624973  C 8.371992187500004 9.090791015624973  8.639003906250002 9.358007812500034  8.639003906250002 9.687363281249986  C 8.639003906250002 10.016923828124959  8.371787109375 10.283935546875  8.042431640625004 10.283935546875  L 4.788251953124998 10.283935546875  C 4.458691406249999 10.283935546875  4.191679687500001 10.016718750000024  4.191679687500001 9.687363281249986  C 4.191679687500001 9.357802734375014  4.458896484374998 9.090791015624973  4.788251953124998 9.090791015624973  Z M 4.788251953124998 4.715654296874959  L 10.211748046875002 4.715654296874959  C 10.541308593750001 4.715654296874959  10.808320312500005 4.9828710937500205  10.808320312500005 5.312226562499973  C 10.808320312500005 5.64158203125001  10.541308593750001 5.908798828124986  10.211748046875002 5.908798828124986  L 4.788251953124998 5.908798828124986  C 4.458691406249999 5.908798828124986  4.191679687500001 5.64158203125001  4.191679687500001 5.312226562499973  C 4.191679687500001 4.9828710937500205  4.458896484374998 4.715654296874959  4.788251953124998 4.715654296874959  Z " fill-rule="evenodd"></path>
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -32 -350 )">
    <path d="M 11.079433593749997 0.34092773437498636  C 12.72416015625 0.34092773437498636  14.062294921875 1.6792675781250068  14 3.3237890624999693  L 14 13.465927734374986  C 14.0625 14.1240234375  13.527451171874997 14.659072265625014  12.869355468750005 14.659072265625014  L 3.920361328124999 14.659072265625014  C 2.275634765625 14.659072265625014  0.9375 13.320732421874993  1 11.67621093750003  L 1 1.5340722656250136  C 0.9375 0.8759765625  1.472548828125003 0.34092773437498636  2.1306445312500006 0.34092773437498636  L 11.079433593749997 0.34092773437498636  Z M 12.870175781250001 13.465927734374986  L 12.869355468750005 3.3237890624999693  C 12.869355468750005 2.336748046874959  12.066474609375007 1.5340722656250136  11.079638671875006 1.5340722656250136  L 2.1306445312500006 1.5340722656250136  L 2.1306445312500006 11.67621093750003  C 2.1306445312500006 12.663251953125041  2.933525390625004 13.465927734374986  3.9203613281250043 13.465927734374986  L 12.870175781250001 13.465927734374986  Z M 4.788251953124998 9.090791015624973  L 8.042431640625004 9.090791015624973  C 8.371992187500004 9.090791015624973  8.639003906250002 9.358007812500034  8.639003906250002 9.687363281249986  C 8.639003906250002 10.016923828124959  8.371787109375 10.283935546875  8.042431640625004 10.283935546875  L 4.788251953124998 10.283935546875  C 4.458691406249999 10.283935546875  4.191679687500001 10.016718750000024  4.191679687500001 9.687363281249986  C 4.191679687500001 9.357802734375014  4.458896484374998 9.090791015624973  4.788251953124998 9.090791015624973  Z M 4.788251953124998 4.715654296874959  L 10.211748046875002 4.715654296874959  C 10.541308593750001 4.715654296874959  10.808320312500005 4.9828710937500205  10.808320312500005 5.312226562499973  C 10.808320312500005 5.64158203125001  10.541308593750001 5.908798828124986  10.211748046875002 5.908798828124986  L 4.788251953124998 5.908798828124986  C 4.458691406249999 5.908798828124986  4.191679687500001 5.64158203125001  4.191679687500001 5.312226562499973  C 4.191679687500001 4.9828710937500205  4.458896484374998 4.715654296874959  4.788251953124998 4.715654296874959  Z " fill-rule="nonzero" fill="rgba(255, 255, 255, 1)" stroke="none" transform="matrix(1 0 0 1 32 350 )" class="fill"></path>
    <path d="M 11.079433593749997 0.34092773437498636  C 12.72416015625 0.34092773437498636  14.062294921875 1.6792675781250068  14 3.3237890624999693  L 14 13.465927734374986  C 14.0625 14.1240234375  13.527451171874997 14.659072265625014  12.869355468750005 14.659072265625014  L 3.920361328124999 14.659072265625014  C 2.275634765625 14.659072265625014  0.9375 13.320732421874993  1 11.67621093750003  L 1 1.5340722656250136  C 0.9375 0.8759765625  1.472548828125003 0.34092773437498636  2.1306445312500006 0.34092773437498636  L 11.079433593749997 0.34092773437498636  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 32 350 )" class="stroke" mask="url(#u49_img_cl15)"></path>
    <path d="M 12.870175781250001 13.465927734374986  L 12.869355468750005 3.3237890624999693  C 12.869355468750005 2.336748046874959  12.066474609375007 1.5340722656250136  11.079638671875006 1.5340722656250136  L 2.1306445312500006 1.5340722656250136  L 2.1306445312500006 11.67621093750003  C 2.1306445312500006 12.663251953125041  2.933525390625004 13.465927734374986  3.9203613281250043 13.465927734374986  L 12.870175781250001 13.465927734374986  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 32 350 )" class="stroke" mask="url(#u49_img_cl15)"></path>
    <path d="M 4.788251953124998 9.090791015624973  L 8.042431640625004 9.090791015624973  C 8.371992187500004 9.090791015624973  8.639003906250002 9.358007812500034  8.639003906250002 9.687363281249986  C 8.639003906250002 10.016923828124959  8.371787109375 10.283935546875  8.042431640625004 10.283935546875  L 4.788251953124998 10.283935546875  C 4.458691406249999 10.283935546875  4.191679687500001 10.016718750000024  4.191679687500001 9.687363281249986  C 4.191679687500001 9.357802734375014  4.458896484374998 9.090791015624973  4.788251953124998 9.090791015624973  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 32 350 )" class="stroke" mask="url(#u49_img_cl15)"></path>
    <path d="M 4.788251953124998 4.715654296874959  L 10.211748046875002 4.715654296874959  C 10.541308593750001 4.715654296874959  10.808320312500005 4.9828710937500205  10.808320312500005 5.312226562499973  C 10.808320312500005 5.64158203125001  10.541308593750001 5.908798828124986  10.211748046875002 5.908798828124986  L 4.788251953124998 5.908798828124986  C 4.458691406249999 5.908798828124986  4.191679687500001 5.64158203125001  4.191679687500001 5.312226562499973  C 4.191679687500001 4.9828710937500205  4.458896484374998 4.715654296874959  4.788251953124998 4.715654296874959  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 32 350 )" class="stroke" mask="url(#u49_img_cl15)"></path>
  </g>
              </svg>
              <div id="u49_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (Group) -->
      <div id="u50" class="ax_default" data-left="14" data-top="385" data-width="220" data-height="40" layer-opacity="1" style="cursor: pointer;">

        <!-- Unnamed (Rectangle) -->
        <div id="u51" class="ax_default box_1 transition" selectiongroup="菜单">
          <div id="u51_div" class="" tabindex="0"></div>
          <div id="u51_text" class="text ">
            <p id="cache3" style=""><span id="cache4" style="">系统管理</span></p>
          </div>
        </div>

        <!-- Unnamed (Rectangle) -->
        <div id="u52" class="ax_default _形状 transition" selectiongroup="二级菜单">
          <div id="u52_div" class=""></div>
          <div id="u52_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (Group) -->
        <div id="u53" class="ax_default" data-left="31" data-top="397" data-width="16" data-height="16" layer-opacity="1">

          <!-- Unnamed (Shape) -->
          <div id="u54" class="ax_default _形状 transition">
            <svg data="http://***********/gsc/VBQ4F3/1c/74/19/1c74195a2a8d4aacad90f09e97e1cc57/images/index/u54.svg" id="u54_img" class="img generatedImage">

  <defs>
    <pattern id="u54_img_bgp" patternUnits="userSpaceOnUse" alignment="0 0" imagerepeat="None"></pattern>
    <mask fill="white" id="u54_img_cl16">
      <path d="M 15.264705882352942 4.674161764705882  L 15.264705882352942 11.325855698529413  C 15.264705882352942 11.747977022058826  15.030576286764706 12.135468750000001  14.651519301470588 12.343613051470589  L 8.603005514705883 15.669451286764707  C 8.228454044117646 15.874888786764707  7.7715459558823525 15.874888786764707  7.396994485294118 15.669451286764707  L 1.3484806985294118 12.343595588235292  C 0.9694237132352939 12.135451286764706  0.7352941176470589 11.747977022058823  0.7352941176470589 11.325838235294116  L 0.7352941176470589 4.674161764705882  C 0.7352941176470589 4.2520404411764705  0.9694237132352939 3.864548713235295  1.3484806985294118 3.6564044117647065  L 7.396994485294118 0.33054871323529406  C 7.771545955882353 0.125111213235294  8.228454044117646 0.125111213235294  8.603005514705883 0.33054871323529406  L 14.651519301470588 3.6563869485294123  C 15.030576286764706 3.864548713235294  15.264705882352942 4.252022977941176  15.264705882352942 4.674161764705882  Z M 14.147058823529411 4.839154411764706  C 14.147058823529411 4.647058823529412  14.042279411764707 4.594669117647059  14.042279411764707 4.594669117647059  L 8.069852941176471 1.3115808823529411  C 8.069852941176471 1.3115808823529411  2.014039522058824 4.566518382352942  1.9577205882352942 4.594669117647059  C 1.8529411764705883 4.647058823529412  1.8529411764705883 4.7693014705882355  1.8529411764705883 4.7693014705882355  L 1.8529411764705883 11.230698529411764  C 1.8529411764705883 11.352941176470589  1.9577205882352942 11.405330882352942  1.9577205882352942 11.405330882352942  L 7.895220588235294 14.670955882352942  C 7.895220588235294 14.670955882352942  7.947610294117647 14.705882352941176  8 14.705882352941176  C 8.052389705882353 14.705882352941176  8.104779411764707 14.670955882352942  8.104779411764707 14.670955882352942  C 8.104779411764707 14.670955882352942  13.943018382352943 11.459554227941178  14.007352941176471 11.422794117647058  C 14.129595588235293 11.352941176470589  14.147058823529411 11.213235294117647  14.147058823529411 11.213235294117647  L 14.147058823529411 4.839154411764706  Z M 10.794117647058824 8  C 10.794117647058824 9.54315625  9.54315625 10.794117647058824  8 10.794117647058824  C 6.45684375 10.794117647058824  5.205882352941177 9.54315625  5.205882352941177 8  C 5.205882352941177 6.45684375  6.45684375 5.205882352941177  8 5.205882352941177  C 9.54315625 5.205882352941177  10.794117647058824 6.45684375  10.794117647058824 8  Z M 6.323529411764706 8  C 6.323529411764706 8.924416360294117  7.075583639705883 9.676470588235293  8 9.676470588235293  C 8.924416360294117 9.676470588235293  9.676470588235293 8.924416360294117  9.676470588235293 8  C 9.676470588235293 7.075583639705883  8.924416360294117 6.323529411764706  8 6.323529411764706  C 7.075583639705883 6.323529411764706  6.323529411764706 7.075583639705883  6.323529411764706 8  Z " fill-rule="evenodd"></path>
    </mask>
  </defs>
  <g transform="matrix(1 0 0 1 -31 -397 )">
    <path d="M 15.264705882352942 4.674161764705882  L 15.264705882352942 11.325855698529413  C 15.264705882352942 11.747977022058826  15.030576286764706 12.135468750000001  14.651519301470588 12.343613051470589  L 8.603005514705883 15.669451286764707  C 8.228454044117646 15.874888786764707  7.7715459558823525 15.874888786764707  7.396994485294118 15.669451286764707  L 1.3484806985294118 12.343595588235292  C 0.9694237132352939 12.135451286764706  0.7352941176470589 11.747977022058823  0.7352941176470589 11.325838235294116  L 0.7352941176470589 4.674161764705882  C 0.7352941176470589 4.2520404411764705  0.9694237132352939 3.864548713235295  1.3484806985294118 3.6564044117647065  L 7.396994485294118 0.33054871323529406  C 7.771545955882353 0.125111213235294  8.228454044117646 0.125111213235294  8.603005514705883 0.33054871323529406  L 14.651519301470588 3.6563869485294123  C 15.030576286764706 3.864548713235294  15.264705882352942 4.252022977941176  15.264705882352942 4.674161764705882  Z M 14.147058823529411 4.839154411764706  C 14.147058823529411 4.647058823529412  14.042279411764707 4.594669117647059  14.042279411764707 4.594669117647059  L 8.069852941176471 1.3115808823529411  C 8.069852941176471 1.3115808823529411  2.014039522058824 4.566518382352942  1.9577205882352942 4.594669117647059  C 1.8529411764705883 4.647058823529412  1.8529411764705883 4.7693014705882355  1.8529411764705883 4.7693014705882355  L 1.8529411764705883 11.230698529411764  C 1.8529411764705883 11.352941176470589  1.9577205882352942 11.405330882352942  1.9577205882352942 11.405330882352942  L 7.895220588235294 14.670955882352942  C 7.895220588235294 14.670955882352942  7.947610294117647 14.705882352941176  8 14.705882352941176  C 8.052389705882353 14.705882352941176  8.104779411764707 14.670955882352942  8.104779411764707 14.670955882352942  C 8.104779411764707 14.670955882352942  13.943018382352943 11.459554227941178  14.007352941176471 11.422794117647058  C 14.129595588235293 11.352941176470589  14.147058823529411 11.213235294117647  14.147058823529411 11.213235294117647  L 14.147058823529411 4.839154411764706  Z M 10.794117647058824 8  C 10.794117647058824 9.54315625  9.54315625 10.794117647058824  8 10.794117647058824  C 6.45684375 10.794117647058824  5.205882352941177 9.54315625  5.205882352941177 8  C 5.205882352941177 6.45684375  6.45684375 5.205882352941177  8 5.205882352941177  C 9.54315625 5.205882352941177  10.794117647058824 6.45684375  10.794117647058824 8  Z M 6.323529411764706 8  C 6.323529411764706 8.924416360294117  7.075583639705883 9.676470588235293  8 9.676470588235293  C 8.924416360294117 9.676470588235293  9.676470588235293 8.924416360294117  9.676470588235293 8  C 9.676470588235293 7.075583639705883  8.924416360294117 6.323529411764706  8 6.323529411764706  C 7.075583639705883 6.323529411764706  6.323529411764706 7.075583639705883  6.323529411764706 8  Z " fill-rule="nonzero" fill="rgba(236, 236, 236, 1)" stroke="none" transform="matrix(1 0 0 1 31 397 )" class="fill"></path>
    <path d="M 15.264705882352942 4.674161764705882  L 15.264705882352942 11.325855698529413  C 15.264705882352942 11.747977022058826  15.030576286764706 12.135468750000001  14.651519301470588 12.343613051470589  L 8.603005514705883 15.669451286764707  C 8.228454044117646 15.874888786764707  7.7715459558823525 15.874888786764707  7.396994485294118 15.669451286764707  L 1.3484806985294118 12.343595588235292  C 0.9694237132352939 12.135451286764706  0.7352941176470589 11.747977022058823  0.7352941176470589 11.325838235294116  L 0.7352941176470589 4.674161764705882  C 0.7352941176470589 4.2520404411764705  0.9694237132352939 3.864548713235295  1.3484806985294118 3.6564044117647065  L 7.396994485294118 0.33054871323529406  C 7.771545955882353 0.125111213235294  8.228454044117646 0.125111213235294  8.603005514705883 0.33054871323529406  L 14.651519301470588 3.6563869485294123  C 15.030576286764706 3.864548713235294  15.264705882352942 4.252022977941176  15.264705882352942 4.674161764705882  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 31 397 )" class="stroke" mask="url(#u54_img_cl16)"></path>
    <path d="M 14.147058823529411 4.839154411764706  C 14.147058823529411 4.647058823529412  14.042279411764707 4.594669117647059  14.042279411764707 4.594669117647059  L 8.069852941176471 1.3115808823529411  C 8.069852941176471 1.3115808823529411  2.014039522058824 4.566518382352942  1.9577205882352942 4.594669117647059  C 1.8529411764705883 4.647058823529412  1.8529411764705883 4.7693014705882355  1.8529411764705883 4.7693014705882355  L 1.8529411764705883 11.230698529411764  C 1.8529411764705883 11.352941176470589  1.9577205882352942 11.405330882352942  1.9577205882352942 11.405330882352942  L 7.895220588235294 14.670955882352942  C 7.895220588235294 14.670955882352942  7.947610294117647 14.705882352941176  8 14.705882352941176  C 8.052389705882353 14.705882352941176  8.104779411764707 14.670955882352942  8.104779411764707 14.670955882352942  C 8.104779411764707 14.670955882352942  13.943018382352943 11.459554227941178  14.007352941176471 11.422794117647058  C 14.129595588235293 11.352941176470589  14.147058823529411 11.213235294117647  14.147058823529411 11.213235294117647  L 14.147058823529411 4.839154411764706  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 31 397 )" class="stroke" mask="url(#u54_img_cl16)"></path>
    <path d="M 10.794117647058824 8  C 10.794117647058824 9.54315625  9.54315625 10.794117647058824  8 10.794117647058824  C 6.45684375 10.794117647058824  5.205882352941177 9.54315625  5.205882352941177 8  C 5.205882352941177 6.45684375  6.45684375 5.205882352941177  8 5.205882352941177  C 9.54315625 5.205882352941177  10.794117647058824 6.45684375  10.794117647058824 8  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 31 397 )" class="stroke" mask="url(#u54_img_cl16)"></path>
    <path d="M 6.323529411764706 8  C 6.323529411764706 8.924416360294117  7.075583639705883 9.676470588235293  8 9.676470588235293  C 8.924416360294117 9.676470588235293  9.676470588235293 8.924416360294117  9.676470588235293 8  C 9.676470588235293 7.075583639705883  8.924416360294117 6.323529411764706  8 6.323529411764706  C 7.075583639705883 6.323529411764706  6.323529411764706 7.075583639705883  6.323529411764706 8  Z " stroke-width="0" stroke-dasharray="0" stroke="rgba(255, 255, 255, 0)" fill="none" transform="matrix(1 0 0 1 31 397 )" class="stroke" mask="url(#u54_img_cl16)"></path>
  </g>
            </svg>
            <div id="u54_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <script src="./ios.js.下载"></script>
  <script type="text/javascript">
                    (function() {
                        if ($axure && $axure.loadDocument) {
                            window.parent.postMessage("isAxurePrototype", "*");
                            var origLoadDocument = $axure.loadDocument;
                            $axure.loadDocument = function(doc) {
                                doc.configuration.prototypeId = 'VBQ4F3';
                                doc.configuration.projectName = '\u5927\u6A21\u578B\u6F0F\u6D1E\u6316\u6398\u5E73\u53F0_RP11_B1.0 (1)';
                                origLoadDocument(doc);
                            };
                        }
                    })();
                </script>

</body><div class="xl-chrome-ext-bar_4DB361DE-01F7-4376-B494-639E489D19ED" id="xl_chrome_ext_4DB361DE-01F7-4376-B494-639E489D19ED" data-v-app="" style="display: block;"><div class=""><div id="xl_chrome_ext_video_tag_wrapper_4DB361DE-01F7-4376-B494-639E489D19ED" config="[object Object]" uiversion="v2" exception="false" latestvideosrc="" isshowvideotag="false" isshowdownloadbar="true" isshowcloudaddbar="false"><div class="_video_op_wrapper_96mx8_1" config="[object Object]" uiversion="v2" style="display: none;"><ul class="_video_op_list_96mx8_17"><li class="_op_item_96mx8_39 _download_96mx8_45"><span class="_op_icon_96mx8_25"></span><span class="_op_text_96mx8_31">下载</span></li><li class="_op_item_96mx8_39 _screen_96mx8_51"><span class="_op_icon_96mx8_25"></span><span class="_op_text_96mx8_31">投屏</span></li></ul></div></div><div class="badge_wrapper_4DB361DE-01F7-4376-B494-639E489D19ED _badge_wrapper_eor63_1" id="badge_wrapper_4DB361DE-01F7-4376-B494-639E489D19ED"><div class="_logo_eor63_20"></div><span class="_line_eor63_27"></span><div class="_text_wrapper_eor63_33"><span class="_download_icon_eor63_39"></span><span class="_text_eor63_33">高速下载</span></div></div><!----><!----><!----><!----><div class="_container_wq4mj_4"><!----></div></div></div></html>