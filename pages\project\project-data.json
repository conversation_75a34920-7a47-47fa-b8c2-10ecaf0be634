{"status": 0, "msg": "success", "data": {"items": [{"id": "01", "projectName": "香港挖掘测试", "targetUrl": "https://hk-test.com", "status": "running", "priority": "high", "progress": 75, "description": "对香港地区网站进行安全漏洞挖掘测试", "createTime": "2024-03-10 09:00:00", "updateTime": "2024-03-12 12:00:00", "completedTasks": 5, "totalTasks": 8, "vulnerabilityCount": 1243}, {"id": "02", "projectName": "漏洞挖掘测试", "targetUrl": "https://vuln-test.com", "status": "running", "priority": "medium", "progress": 62, "description": "通用漏洞挖掘测试项目", "createTime": "2024-03-08 10:15:00", "updateTime": "2024-03-12 12:00:00", "completedTasks": 5, "totalTasks": 8, "vulnerabilityCount": 1243}, {"id": "03", "projectName": "漏洞挖掘测试", "targetUrl": "https://api.example.com", "status": "running", "priority": "high", "progress": 62, "description": "对REST API接口进行安全漏洞扫描", "createTime": "2024-03-12 08:30:00", "updateTime": "2024-03-12 12:00:00", "completedTasks": 5, "totalTasks": 8, "vulnerabilityCount": 1243}, {"id": "04", "projectName": "香港挖掘测试", "targetUrl": "https://mobile-backend.com", "status": "running", "priority": "medium", "progress": 62, "description": "移动应用后端服务的安全检测", "createTime": "2024-03-09 14:20:00", "updateTime": "2024-03-12 12:00:00", "completedTasks": 5, "totalTasks": 8, "vulnerabilityCount": 1243}, {"id": "05", "projectName": "漏洞挖掘测试", "targetUrl": "https://finance-system.com", "status": "running", "priority": "high", "progress": 62, "description": "金融系统的深度渗透测试项目", "createTime": "2024-03-07 16:00:00", "updateTime": "2024-03-12 12:00:00", "completedTasks": 5, "totalTasks": 8, "vulnerabilityCount": 1243}, {"id": "06", "projectName": "香港挖掘测试", "targetUrl": "https://hk-test2.com", "status": "running", "priority": "medium", "progress": 62, "description": "香港地区第二个测试项目", "createTime": "2024-03-05 16:00:00", "updateTime": "2024-03-12 12:00:00", "completedTasks": 5, "totalTasks": 8, "vulnerabilityCount": 1243}], "total": 6, "page": 1, "perPage": 10}}