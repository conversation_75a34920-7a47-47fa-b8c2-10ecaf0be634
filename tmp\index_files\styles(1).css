body {
  margin:0px;
  background-color:rgba(247, 247, 247, 1);
  background-image:none;
  position:static;
  left:auto;
  width:1922px;
  margin-left:0;
  margin-right:0;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:249px;
  height:918px;
  display:flex;
  transition:none;
}
#u0 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u0_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:249px;
  height:918px;
}
#u0_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1 {
  border-width:0px;
  position:absolute;
  left:248px;
  top:0px;
  width:10px;
  height:10px;
}
#u2 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:233px;
  height:63px;
}
#u3 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u4_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:187px;
  height:28px;
  background:inherit;
  background-color:rgba(51, 51, 51, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Medium", "PingFang SC Medium", "PingFang SC", sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:20px;
  color:#FFFFFF;
  text-align:center;
  line-height:28px;
}
#u4 {
  border-width:0px;
  position:absolute;
  left:46px;
  top:25px;
  width:187px;
  height:28px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Medium", "PingFang SC Medium", "PingFang SC", sans-serif;
  font-weight:500;
  font-style:normal;
  font-size:20px;
  color:#FFFFFF;
  text-align:center;
  line-height:28px;
}
#u4 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:187px;
  height:10px;
  background:inherit;
  background-color:rgba(51, 51, 51, 0);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:9px;
  letter-spacing:0.1px;
  color:#FFFFFF;
  text-align:center;
  line-height:10px;
}
#u5 {
  border-width:0px;
  position:absolute;
  left:46px;
  top:53px;
  width:187px;
  height:10px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"Arial Normal", "Arial", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:9px;
  letter-spacing:0.1px;
  color:#FFFFFF;
  text-align:center;
  line-height:10px;
}
#u5 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u7 {
  border-width:0px;
  position:absolute;
  left:10px;
  top:31px;
  width:31px;
  height:29px;
  display:flex;
  transition:none;
}
#u7 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:31px;
  height:29px;
}
#u7_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u8 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u9 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u10_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:220px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
  text-align:left;
}
#u10 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:145px;
  width:220px;
  height:40px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
  text-align:left;
}
#u10 .text {
  position:absolute;
  align-self:center;
  padding:2px 10px 2px 54px;
  box-sizing:border-box;
  width:100%;
}
#u10_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:220px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
  text-align:left;
}
#u10.mouseOver {
}
#u10_div.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:220px;
  height:40px;
  background:inherit;
  background-color:rgba(44, 44, 44, 1);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
  text-align:left;
}
#u10.selected {
}
#u10_div.mouseOver.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:220px;
  height:40px;
  background:inherit;
  background-color:rgba(44, 44, 44, 1);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
  text-align:left;
}
#u10.mouseOver.selected {
}
#u10_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u11_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:26px;
  background:inherit;
  background-color:rgba(45, 45, 45, 1);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
}
#u11 {
  border-width:0px;
  position:absolute;
  left:26px;
  top:152px;
  width:26px;
  height:26px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u11 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u11_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:26px;
  background:inherit;
  background-color:rgba(23, 119, 255, 1);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
}
#u11.mouseOver {
}
#u11_div.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:26px;
  background:inherit;
  background-color:rgba(23, 119, 255, 1);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
}
#u11.selected {
}
#u11_div.mouseOver.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:26px;
  background:inherit;
  background-color:rgba(23, 119, 255, 1);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
}
#u11.mouseOver.selected {
}
#u11_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u13 {
  border-width:0px;
  position:absolute;
  left:31px;
  top:157px;
  width:15px;
  height:16px;
  display:flex;
  transition:none;
}
#u13 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u13_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:16px;
}
#u13_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u14 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u15_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:220px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
  text-align:left;
}
#u15 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:193px;
  width:220px;
  height:40px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
  text-align:left;
}
#u15 .text {
  position:absolute;
  align-self:center;
  padding:2px 10px 2px 54px;
  box-sizing:border-box;
  width:100%;
}
#u15_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:220px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
  text-align:left;
}
#u15.mouseOver {
}
#u15_div.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:220px;
  height:40px;
  background:inherit;
  background-color:rgba(44, 44, 44, 1);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
  text-align:left;
}
#u15.selected {
}
#u15_div.mouseOver.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:220px;
  height:40px;
  background:inherit;
  background-color:rgba(44, 44, 44, 1);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
  text-align:left;
}
#u15.mouseOver.selected {
}
#u15_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u16_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:26px;
  background:inherit;
  background-color:rgba(45, 45, 45, 1);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
}
#u16 {
  border-width:0px;
  position:absolute;
  left:26px;
  top:200px;
  width:26px;
  height:26px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u16 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u16_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:26px;
  background:inherit;
  background-color:rgba(23, 119, 255, 1);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
}
#u16.mouseOver {
}
#u16_div.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:26px;
  background:inherit;
  background-color:rgba(23, 119, 255, 1);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
}
#u16.selected {
}
#u16_div.mouseOver.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:26px;
  background:inherit;
  background-color:rgba(23, 119, 255, 1);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
}
#u16.mouseOver.selected {
}
#u16_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u17 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u18 {
  border-width:0px;
  position:absolute;
  left:31px;
  top:205px;
  width:16px;
  height:16px;
  display:flex;
  transition:none;
}
#u18 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u18_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
}
#u18_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u19 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u20_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:220px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
  text-align:left;
}
#u20 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:97px;
  width:220px;
  height:40px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
  text-align:left;
}
#u20 .text {
  position:absolute;
  align-self:center;
  padding:2px 10px 2px 54px;
  box-sizing:border-box;
  width:100%;
}
#u20_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:220px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
  text-align:left;
}
#u20.mouseOver {
}
#u20_div.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:220px;
  height:40px;
  background:inherit;
  background-color:rgba(44, 44, 44, 1);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
  text-align:left;
}
#u20.selected {
}
#u20_div.mouseOver.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:220px;
  height:40px;
  background:inherit;
  background-color:rgba(44, 44, 44, 1);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
  text-align:left;
}
#u20.mouseOver.selected {
}
#u20_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u21_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:26px;
  background:inherit;
  background-color:rgba(45, 45, 45, 1);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
}
#u21 {
  border-width:0px;
  position:absolute;
  left:26px;
  top:104px;
  width:26px;
  height:26px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u21 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u21_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:26px;
  background:inherit;
  background-color:rgba(23, 119, 255, 1);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
}
#u21.mouseOver {
}
#u21_div.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:26px;
  background:inherit;
  background-color:rgba(23, 119, 255, 1);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
}
#u21.selected {
}
#u21_div.mouseOver.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:26px;
  background:inherit;
  background-color:rgba(23, 119, 255, 1);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
}
#u21.mouseOver.selected {
}
#u21_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u22 {
  border-width:0px;
  position:absolute;
  left:31px;
  top:109px;
  width:16px;
  height:16px;
  display:flex;
  transition:none;
}
#u22 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u22_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
}
#u22_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u23 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u24 {
  border-width:0px;
  position:absolute;
  left:236px;
  top:97px;
  width:24px;
  height:24px;
  display:flex;
  transition:none;
}
#u24 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u24_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:24px;
}
#u24_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u25 {
  border-width:0px;
  position:absolute;
  left:242px;
  top:106px;
  width:11px;
  height:6px;
  display:flex;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
  transition:none;
}
#u25 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u25_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:6px;
}
#u25_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u26 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u27_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:220px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
  text-align:left;
}
#u27 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:241px;
  width:220px;
  height:40px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
  text-align:left;
}
#u27 .text {
  position:absolute;
  align-self:center;
  padding:2px 10px 2px 54px;
  box-sizing:border-box;
  width:100%;
}
#u27_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:220px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
  text-align:left;
}
#u27.mouseOver {
}
#u27_div.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:220px;
  height:40px;
  background:inherit;
  background-color:rgba(44, 44, 44, 1);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
  text-align:left;
}
#u27.selected {
}
#u27_div.mouseOver.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:220px;
  height:40px;
  background:inherit;
  background-color:rgba(44, 44, 44, 1);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
  text-align:left;
}
#u27.mouseOver.selected {
}
#u27_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u28_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:26px;
  background:inherit;
  background-color:rgba(45, 45, 45, 1);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
}
#u28 {
  border-width:0px;
  position:absolute;
  left:26px;
  top:248px;
  width:26px;
  height:26px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u28 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u28_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:26px;
  background:inherit;
  background-color:rgba(23, 119, 255, 1);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
}
#u28.mouseOver {
}
#u28_div.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:26px;
  background:inherit;
  background-color:rgba(23, 119, 255, 1);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
}
#u28.selected {
}
#u28_div.mouseOver.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:26px;
  background:inherit;
  background-color:rgba(23, 119, 255, 1);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
}
#u28.mouseOver.selected {
}
#u28_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u29 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u30 {
  border-width:0px;
  position:absolute;
  left:31px;
  top:254px;
  width:16px;
  height:14px;
  display:flex;
  transition:none;
}
#u30 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u30_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:14px;
}
#u30_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u31 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u32_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:220px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
  text-align:left;
}
#u32 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:289px;
  width:220px;
  height:40px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
  text-align:left;
}
#u32 .text {
  position:absolute;
  align-self:center;
  padding:2px 10px 2px 54px;
  box-sizing:border-box;
  width:100%;
}
#u32_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:220px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
  text-align:left;
}
#u32.mouseOver {
}
#u32_div.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:220px;
  height:40px;
  background:inherit;
  background-color:rgba(44, 44, 44, 1);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
  text-align:left;
}
#u32.selected {
}
#u32_div.mouseOver.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:220px;
  height:40px;
  background:inherit;
  background-color:rgba(44, 44, 44, 1);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
  text-align:left;
}
#u32.mouseOver.selected {
}
#u32_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u33_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:26px;
  background:inherit;
  background-color:rgba(45, 45, 45, 1);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
}
#u33 {
  border-width:0px;
  position:absolute;
  left:26px;
  top:296px;
  width:26px;
  height:26px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u33 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u33_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:26px;
  background:inherit;
  background-color:rgba(23, 119, 255, 1);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
}
#u33.mouseOver {
}
#u33_div.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:26px;
  background:inherit;
  background-color:rgba(23, 119, 255, 1);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
}
#u33.selected {
}
#u33_div.mouseOver.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:26px;
  background:inherit;
  background-color:rgba(23, 119, 255, 1);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
}
#u33.mouseOver.selected {
}
#u33_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u34 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u35 {
  border-width:0px;
  position:absolute;
  left:32px;
  top:301px;
  width:14px;
  height:16px;
  display:flex;
  transition:none;
}
#u35 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u35_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:16px;
}
#u35_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u36_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1665px;
  height:64px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  filter:drop-shadow(none);
  transition:none;
}
#u36 {
  border-width:0px;
  position:absolute;
  left:248px;
  top:0px;
  width:1665px;
  height:64px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u36 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u36_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u37 {
  border-width:0px;
  position:absolute;
  left:273px;
  top:0px;
  width:1649px;
  height:914px;
}
#u37_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1649px;
  height:914px;
  overflow:auto;
}
#u38_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:41px;
  background:inherit;
  background-color:rgba(50, 50, 50, 1);
  border-radius:8px;
  filter:drop-shadow(none);
  transition:none;
}
#u38 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:851px;
  width:78px;
  height:41px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u38 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u38_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u39 {
  position:absolute;
  left:20px;
  top:857px;
}
#u39_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:65px;
  height:30px;
  background-image:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u39_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u40 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u41 {
  border-width:0px;
  position:absolute;
  left:5px;
  top:5px;
  width:20px;
  height:20px;
  display:flex;
  transition:none;
}
#u41 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u41_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u41_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u42_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border-radius:8px;
  filter:drop-shadow(none);
  transition:none;
}
#u42 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:0px;
  width:30px;
  height:30px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u42 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u42_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u43 {
  border-width:0px;
  position:absolute;
  left:40px;
  top:5px;
  width:20px;
  height:20px;
  display:flex;
  transition:none;
}
#u43 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u43_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u43_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u44 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u45_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:220px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
  text-align:left;
}
#u45 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:337px;
  width:220px;
  height:40px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
  text-align:left;
}
#u45 .text {
  position:absolute;
  align-self:center;
  padding:2px 10px 2px 54px;
  box-sizing:border-box;
  width:100%;
}
#u45_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:220px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
  text-align:left;
}
#u45.mouseOver {
}
#u45_div.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:220px;
  height:40px;
  background:inherit;
  background-color:rgba(44, 44, 44, 1);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
  text-align:left;
}
#u45.selected {
}
#u45_div.mouseOver.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:220px;
  height:40px;
  background:inherit;
  background-color:rgba(44, 44, 44, 1);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
  text-align:left;
}
#u45.mouseOver.selected {
}
#u45_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u46_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:26px;
  background:inherit;
  background-color:rgba(45, 45, 45, 1);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
}
#u46 {
  border-width:0px;
  position:absolute;
  left:26px;
  top:344px;
  width:26px;
  height:26px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u46 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u46_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:26px;
  background:inherit;
  background-color:rgba(23, 119, 255, 1);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
}
#u46.mouseOver {
}
#u46_div.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:26px;
  background:inherit;
  background-color:rgba(23, 119, 255, 1);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
}
#u46.selected {
}
#u46_div.mouseOver.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:26px;
  background:inherit;
  background-color:rgba(23, 119, 255, 1);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
}
#u46.mouseOver.selected {
}
#u46_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u47 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u48 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u49 {
  border-width:0px;
  position:absolute;
  left:32px;
  top:350px;
  width:15px;
  height:15px;
  display:flex;
  transition:none;
}
#u49 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u49_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:15px;
}
#u49_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u50 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u51_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:220px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
  text-align:left;
}
#u51 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:385px;
  width:220px;
  height:40px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
  text-align:left;
}
#u51 .text {
  position:absolute;
  align-self:center;
  padding:2px 10px 2px 54px;
  box-sizing:border-box;
  width:100%;
}
#u51_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:220px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
  text-align:left;
}
#u51.mouseOver {
}
#u51_div.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:220px;
  height:40px;
  background:inherit;
  background-color:rgba(44, 44, 44, 1);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
  text-align:left;
}
#u51.selected {
}
#u51_div.mouseOver.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:220px;
  height:40px;
  background:inherit;
  background-color:rgba(44, 44, 44, 1);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
  font-family:"PingFangSC-Regular", "PingFang SC", sans-serif;
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
  text-align:left;
}
#u51.mouseOver.selected {
}
#u51_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u52_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:26px;
  background:inherit;
  background-color:rgba(45, 45, 45, 1);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
}
#u52 {
  border-width:0px;
  position:absolute;
  left:26px;
  top:392px;
  width:26px;
  height:26px;
  display:flex;
  transition:none;
  transform-origin:50% 50%;
}
#u52 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u52_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:26px;
  background:inherit;
  background-color:rgba(23, 119, 255, 1);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
}
#u52.mouseOver {
}
#u52_div.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:26px;
  background:inherit;
  background-color:rgba(23, 119, 255, 1);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
}
#u52.selected {
}
#u52_div.mouseOver.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:26px;
  background:inherit;
  background-color:rgba(23, 119, 255, 1);
  border-radius:4px;
  filter:drop-shadow(none);
  transition:none;
}
#u52.mouseOver.selected {
}
#u52_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u53 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u54 {
  border-width:0px;
  position:absolute;
  left:31px;
  top:397px;
  width:16px;
  height:16px;
  display:flex;
  transition:none;
}
#u54 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u54_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
}
#u54_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
