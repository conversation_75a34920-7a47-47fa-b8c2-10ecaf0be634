/* 项目卡片样式 */
.project-card {
  transition: all 0.3s ease;
  cursor: pointer;
}

.project-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0,0,0,0.15) !important;
}

.project-card .project-title {
  font-size: 16px !important;
  font-weight: 600 !important;
  color: #333 !important;
  margin-bottom: 0 !important;
  line-height: 1.4;
}

.project-card .project-actions {
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.project-card:hover .project-actions {
  opacity: 1;
}

.project-card .stat-item {
  text-align: center;
}

.project-card .stat-label {
  font-size: 12px !important;
  color: #666 !important;
  margin-bottom: 4px !important;
  display: block;
}

.project-card .stat-value {
  font-size: 16px !important;
  font-weight: 600 !important;
  color: #333 !important;
  display: flex;
  align-items: center;
  justify-content: center;
}

.project-card .stat-icon {
  margin-right: 4px !important;
}

.project-card .update-time {
  font-size: 12px !important;
  color: #999 !important;
  text-align: right;
}

.project-card .project-card-header {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 12px;
  margin-bottom: 16px !important;
}

.project-card .project-card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.project-card .project-stats {
  margin-bottom: 16px !important;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .amis-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}

@media (max-width: 768px) {
  .amis-grid {
    grid-template-columns: 1fr !important;
  }
  
  .project-card {
    height: auto !important;
    min-height: 180px;
  }
}

/* 卡片网格布局优化 */
.amis-grid {
  gap: 20px !important;
}

.amis-grid .amis-grid-item {
  margin-bottom: 0 !important;
}

/* 下拉菜单样式优化 */
.project-card .cxd-DropDown {
  position: relative;
}

.project-card .cxd-DropDown-menu {
  min-width: 120px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  border: 1px solid #e8e8e8;
  border-radius: 6px;
}

.project-card .cxd-DropDown-menu .cxd-DropDown-menuItem {
  padding: 8px 12px;
  font-size: 14px;
}

.project-card .cxd-DropDown-menu .cxd-DropDown-menuItem:hover {
  background-color: #f5f5f5;
}

/* 图标颜色 */
.project-card .fa-file-text-o {
  color: #1890ff !important;
}

.project-card .fa-bug {
  color: #ff4d4f !important;
}

.project-card .fa-ellipsis-v {
  color: #666 !important;
}

.project-card .fa-ellipsis-v:hover {
  color: #333 !important;
}

/* 卡片描述样式 */
.project-card .card-description {
  color: #666 !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
  margin-bottom: 16px !important;
  min-height: 42px !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  display: -webkit-box !important;
  -webkit-line-clamp: 2 !important;
  -webkit-box-orient: vertical !important;
}

/* 卡片底部样式 */
.project-card .card-footer {
  border-top: 1px solid #f0f0f0 !important;
  padding-top: 12px !important;
  margin-top: auto !important;
}

/* 优先级标签样式 */
.label {
  display: inline-block !important;
  padding: 2px 6px !important;
  font-size: 11px !important;
  font-weight: bold !important;
  line-height: 1 !important;
  color: #fff !important;
  text-align: center !important;
  white-space: nowrap !important;
  vertical-align: baseline !important;
  border-radius: 3px !important;
}

.label-danger {
  background-color: #d9534f !important;
}

.label-warning {
  background-color: #f0ad4e !important;
}

.label-info {
  background-color: #5bc0de !important;
}

/* 统计项目样式优化 */
.project-card .stat-item {
  text-align: left !important;
}

.project-card .stat-item .fa {
  margin-right: 8px !important;
  font-size: 16px !important;
}
