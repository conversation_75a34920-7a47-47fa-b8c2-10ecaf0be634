<!DOCTYPE html>
<!-- saved from url=(0048)http://***********/VBQ4F3/?id=iu1cwq&p=index&c=1 -->
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><style>.ͼ1.cm-focused {outline: 1px dotted #212121;}
.ͼ1 {position: relative !important; box-sizing: border-box; display: flex !important; flex-direction: column;}
.ͼ1 .cm-scroller {display: flex !important; align-items: flex-start !important; font-family: monospace; line-height: 1.4; height: 100%; overflow-x: auto; position: relative; z-index: 0;}
.ͼ1 .cm-content[contenteditable=true] {-webkit-user-modify: read-write-plaintext-only;}
.ͼ1 .cm-content {margin: 0; flex-grow: 2; flex-shrink: 0; display: block; white-space: pre; word-wrap: normal; box-sizing: border-box; min-height: 100%; padding: 4px 0; outline: none;}
.ͼ1 .cm-lineWrapping {white-space: pre-wrap; white-space: break-spaces; word-break: break-word; overflow-wrap: anywhere; flex-shrink: 1;}
.ͼ2 .cm-content {caret-color: black;}
.ͼ3 .cm-content {caret-color: white;}
.ͼ1 .cm-line {display: block; padding: 0 2px 0 6px;}
.ͼ1 .cm-layer > * {position: absolute;}
.ͼ1 .cm-layer {position: absolute; left: 0; top: 0; contain: size style;}
.ͼ2 .cm-selectionBackground {background: #d9d9d9;}
.ͼ3 .cm-selectionBackground {background: #222;}
.ͼ2.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground {background: #d7d4f0;}
.ͼ3.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground {background: #233;}
.ͼ1 .cm-cursorLayer {pointer-events: none;}
.ͼ1.cm-focused > .cm-scroller > .cm-cursorLayer {animation: steps(1) cm-blink 1.2s infinite;}
@keyframes cm-blink {50% {opacity: 0;}}
@keyframes cm-blink2 {50% {opacity: 0;}}
.ͼ1 .cm-cursor, .ͼ1 .cm-dropCursor {border-left: 1.2px solid black; margin-left: -0.6px; pointer-events: none;}
.ͼ1 .cm-cursor {display: none;}
.ͼ3 .cm-cursor {border-left-color: #444;}
.ͼ1 .cm-dropCursor {position: absolute;}
.ͼ1.cm-focused > .cm-scroller > .cm-cursorLayer .cm-cursor {display: block;}
.ͼ1 .cm-iso {unicode-bidi: isolate;}
.ͼ1 .cm-announced {position: fixed; top: -10000px;}
@media print {.ͼ1 .cm-announced {display: none;}}
.ͼ2 .cm-activeLine {background-color: #cceeff44;}
.ͼ3 .cm-activeLine {background-color: #99eeff33;}
.ͼ2 .cm-specialChar {color: red;}
.ͼ3 .cm-specialChar {color: #f78;}
.ͼ1 .cm-gutters {flex-shrink: 0; display: flex; height: 100%; box-sizing: border-box; inset-inline-start: 0; z-index: 200;}
.ͼ2 .cm-gutters {background-color: #f5f5f5; color: #6c6c6c; border-right: 1px solid #ddd;}
.ͼ3 .cm-gutters {background-color: #333338; color: #ccc;}
.ͼ1 .cm-gutter {display: flex !important; flex-direction: column; flex-shrink: 0; box-sizing: border-box; min-height: 100%; overflow: hidden;}
.ͼ1 .cm-gutterElement {box-sizing: border-box;}
.ͼ1 .cm-lineNumbers .cm-gutterElement {padding: 0 3px 0 5px; min-width: 20px; text-align: right; white-space: nowrap;}
.ͼ2 .cm-activeLineGutter {background-color: #e2f2ff;}
.ͼ3 .cm-activeLineGutter {background-color: #222227;}
.ͼ1 .cm-panels {box-sizing: border-box; position: sticky; left: 0; right: 0;}
.ͼ2 .cm-panels {background-color: #f5f5f5; color: black;}
.ͼ2 .cm-panels-top {border-bottom: 1px solid #ddd;}
.ͼ2 .cm-panels-bottom {border-top: 1px solid #ddd;}
.ͼ3 .cm-panels {background-color: #333338; color: white;}
.ͼ1 .cm-tab {display: inline-block; overflow: hidden; vertical-align: bottom;}
.ͼ1 .cm-widgetBuffer {vertical-align: text-top; height: 1em; width: 0; display: inline;}
.ͼ1 .cm-placeholder {color: #888; display: inline-block; vertical-align: top;}
.ͼ1 .cm-highlightSpace:before {content: attr(data-display); position: absolute; pointer-events: none; color: #888;}
.ͼ1 .cm-highlightTab {background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="200" height="20"><path stroke="%23888" stroke-width="1" fill="none" d="M1 10H196L190 5M190 15L196 10M197 4L197 16"/></svg>'); background-size: auto 100%; background-position: right 90%; background-repeat: no-repeat;}
.ͼ1 .cm-trailingSpace {background-color: #ff332255;}
.ͼ1 .cm-button {vertical-align: middle; color: inherit; font-size: 70%; padding: .2em 1em; border-radius: 1px;}
.ͼ2 .cm-button:active {background-image: linear-gradient(#b4b4b4, #d0d3d6);}
.ͼ2 .cm-button {background-image: linear-gradient(#eff1f5, #d9d9df); border: 1px solid #888;}
.ͼ3 .cm-button:active {background-image: linear-gradient(#111, #333);}
.ͼ3 .cm-button {background-image: linear-gradient(#393939, #111); border: 1px solid #888;}
.ͼ1 .cm-textfield {vertical-align: middle; color: inherit; font-size: 70%; border: 1px solid silver; padding: .2em .5em;}
.ͼ2 .cm-textfield {background-color: white;}
.ͼ3 .cm-textfield {border: 1px solid #555; background-color: inherit;}
.ͼ15 .cm-focused {outline: none;}
.ͼp {color: #c678dd;}
.ͼq {color: #e06c75;}
.ͼr {color: #61afef;}
.ͼs {color: #d19a66;}
.ͼt {color: #abb2bf;}
.ͼu {color: #e5c07b;}
.ͼv {color: #56b6c2;}
.ͼw {color: #7d8799;}
.ͼx {font-weight: bold;}
.ͼy {font-style: italic;}
.ͼz {text-decoration: line-through;}
.ͼ10 {color: #7d8799; text-decoration: underline;}
.ͼ11 {font-weight: bold; color: #e06c75;}
.ͼ12 {color: #d19a66;}
.ͼ13 {color: #98c379;}
.ͼ14 {color: #ffffff;}
.ͼo {color: #abb2bf; background-color: #282c34;}
.ͼo .cm-content {caret-color: #528bff;}
.ͼo .cm-cursor, .ͼo .cm-dropCursor {border-left-color: #528bff;}
.ͼo.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground, .ͼo .cm-selectionBackground, .ͼo .cm-content ::selection {background-color: #3E4451;}
.ͼo .cm-panels {background-color: #21252b; color: #abb2bf;}
.ͼo .cm-panels.cm-panels-top {border-bottom: 2px solid black;}
.ͼo .cm-panels.cm-panels-bottom {border-top: 2px solid black;}
.ͼo .cm-searchMatch {background-color: #72a1ff59; outline: 1px solid #457dff;}
.ͼo .cm-searchMatch.cm-searchMatch-selected {background-color: #6199ff2f;}
.ͼo .cm-activeLine {background-color: #6699ff0b;}
.ͼo .cm-selectionMatch {background-color: #aafe661a;}
.ͼo.cm-focused .cm-matchingBracket, .ͼo.cm-focused .cm-nonmatchingBracket {background-color: #bad0f847;}
.ͼo .cm-gutters {background-color: #282c34; color: #7d8799; border: none;}
.ͼo .cm-activeLineGutter {background-color: #2c313a;}
.ͼo .cm-foldPlaceholder {background-color: transparent; border: none; color: #ddd;}
.ͼo .cm-tooltip {border: none; background-color: #353a42;}
.ͼo .cm-tooltip .cm-tooltip-arrow:before {border-top-color: transparent; border-bottom-color: transparent;}
.ͼo .cm-tooltip .cm-tooltip-arrow:after {border-top-color: #353a42; border-bottom-color: #353a42;}
.ͼo .cm-tooltip-autocomplete > ul > li[aria-selected] {background-color: #2c313a; color: #abb2bf;}
.ͼ5 {color: #404740;}
.ͼ6 {text-decoration: underline;}
.ͼ7 {text-decoration: underline; font-weight: bold;}
.ͼ8 {font-style: italic;}
.ͼ9 {font-weight: bold;}
.ͼa {text-decoration: line-through;}
.ͼb {color: #708;}
.ͼc {color: #219;}
.ͼd {color: #164;}
.ͼe {color: #a11;}
.ͼf {color: #e40;}
.ͼg {color: #00f;}
.ͼh {color: #30a;}
.ͼi {color: #085;}
.ͼj {color: #167;}
.ͼk {color: #256;}
.ͼl {color: #00c;}
.ͼm {color: #940;}
.ͼn {color: #f00;}
.ͼ4 .cm-line ::selection {background-color: transparent !important;}
.ͼ4 .cm-line::selection {background-color: transparent !important;}
.ͼ4 .cm-line {caret-color: transparent !important;}
.ͼ4 .cm-content {caret-color: transparent !important;}
</style>
    <meta name="robots" content="noindex, nofollow">
    <title>index</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <link type="text/css" href="./index_files/reset.css" rel="Stylesheet">
    <link type="text/css" href="./index_files/default.css" rel="Stylesheet">
    
    
    <link rel="preconnect" href="https://fonts.googleapis.com/">
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="">
    <link href="./index_files/fonts.css" rel="stylesheet">
    <link href="./index_files/css2" rel="stylesheet">

    <link rel="icon" href="http://***********/app/favicon.ico"><meta property="og:site_name" content="Axure Cloud">
<meta property="og:type" content="website">
<meta property="og:title" content="大模型漏洞挖掘平台_RP11_B1.0 (1)">
<meta property="og:description" content="Created with Axure RP">
<meta property="og:url" content="http://***********/VBQ4F3/?g=4">
<meta property="og:image" content="http://***********/gsc/VBQ4F3/thumbnails/iu1cwq.png">
<link rel="alternate" type="application/json+oembed" href="http://localhost/oembed?url=http%3A%2F%2F***********%2FVBQ4F3%2F%3Fg%3D4" title="oEmbed"><script type="text/javascript">
        AXSHARE_HOST_URL = 'http://***********';
        AXSHARE_HOST_SECURE_URL = 'http://***********';
        ACCOUNT_SERVICE_URL = 'http://***********';
        ACCOUNT_SERVICE_SECURE_URL = 'http://***********';
        ON_PREM_LDAP_ENABLED = 'false';
        AXSHARE_CLIENT_URL = 'http://***********/app/'
</script><script type="text/javascript">
        if (location.href.toString().indexOf('file://localhost/') == 0) {
            location.href = location.href.toString().replace('file://localhost/', 'file:///');
        }
    </script>
    <!--<link type="text/css" rel="Stylesheet" href="plugins/sitemap/styles/sitemap.css" />
    <link type="text/css" rel="Stylesheet" href="plugins/page_notes/styles/page_notes.css" />
    <link type="text/css" rel="Stylesheet" href="plugins/debug/styles/debug.css" />
    <link type="text/css" rel="Stylesheet" href="plugins/handoff/styles/handoff.css" />
    <link type="text/css" rel="Stylesheet" href="plugins/handoff/styles/codemirror.css" />-->
<script type="text/javascript" async="" src="./index_files/document.js.下载"></script><script type="text/javascript" src="./index_files/debug.js.下载"></script><script type="text/javascript" src="./index_files/sitemap.js.下载"></script><script type="text/javascript" src="./index_files/page_notes.js.下载"></script><script type="text/javascript" src="./index_files/hintmanager.js.下载"></script><link type="text/css" rel="stylesheet" href="./index_files/debug.css"><link type="text/css" rel="stylesheet" href="./index_files/sitemap.css"><link type="text/css" rel="stylesheet" href="./index_files/page_notes.css"><link rel="stylesheet" type="text/css" href="./index_files/fonts.css"><script text="text/javascript" language="JavaScript" src="./index_files/feedback9.js.下载"></script><link rel="stylesheet" type="text/css" href="./index_files/feedback9.css"><link text="text/css" href="./index_files/axImgAreaSelect.css" rel="Stylesheet"><style data-id="immersive-translate-input-injected-css">.immersive-translate-input {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  z-index: 2147483647;
  display: flex;
  justify-content: center;
  align-items: center;
}
.immersive-translate-attach-loading::after {
  content: " ";

  --loading-color: #f78fb6;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  display: block;
  margin: 12px auto;
  position: relative;
  color: white;
  left: -100px;
  box-sizing: border-box;
  animation: immersiveTranslateShadowRolling 1.5s linear infinite;

  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-2000%, -50%);
  z-index: 100;
}

.immersive-translate-loading-spinner {
  vertical-align: middle !important;
  width: 10px !important;
  height: 10px !important;
  display: inline-block !important;
  margin: 0 4px !important;
  border: 2px rgba(221, 244, 255, 0.6) solid !important;
  border-top: 2px rgba(0, 0, 0, 0.375) solid !important;
  border-left: 2px rgba(0, 0, 0, 0.375) solid !important;
  border-radius: 50% !important;
  padding: 0 !important;
  -webkit-animation: immersive-translate-loading-animation 0.6s infinite linear !important;
  animation: immersive-translate-loading-animation 0.6s infinite linear !important;
}

@-webkit-keyframes immersive-translate-loading-animation {
  from {
    -webkit-transform: rotate(0deg);
  }

  to {
    -webkit-transform: rotate(359deg);
  }
}

@keyframes immersive-translate-loading-animation {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(359deg);
  }
}

.immersive-translate-input-loading {
  --loading-color: #f78fb6;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  display: block;
  margin: 12px auto;
  position: relative;
  color: white;
  left: -100px;
  box-sizing: border-box;
  animation: immersiveTranslateShadowRolling 1.5s linear infinite;
}

@keyframes immersiveTranslateShadowRolling {
  0% {
    box-shadow: 0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0),
      0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
  }

  12% {
    box-shadow: 100px 0 var(--loading-color), 0px 0 rgba(255, 255, 255, 0),
      0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
  }

  25% {
    box-shadow: 110px 0 var(--loading-color), 100px 0 var(--loading-color),
      0px 0 rgba(255, 255, 255, 0), 0px 0 rgba(255, 255, 255, 0);
  }

  36% {
    box-shadow: 120px 0 var(--loading-color), 110px 0 var(--loading-color),
      100px 0 var(--loading-color), 0px 0 rgba(255, 255, 255, 0);
  }

  50% {
    box-shadow: 130px 0 var(--loading-color), 120px 0 var(--loading-color),
      110px 0 var(--loading-color), 100px 0 var(--loading-color);
  }

  62% {
    box-shadow: 200px 0 rgba(255, 255, 255, 0), 130px 0 var(--loading-color),
      120px 0 var(--loading-color), 110px 0 var(--loading-color);
  }

  75% {
    box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0),
      130px 0 var(--loading-color), 120px 0 var(--loading-color);
  }

  87% {
    box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0),
      200px 0 rgba(255, 255, 255, 0), 130px 0 var(--loading-color);
  }

  100% {
    box-shadow: 200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0),
      200px 0 rgba(255, 255, 255, 0), 200px 0 rgba(255, 255, 255, 0);
  }
}

.immersive-translate-toast {
  display: flex;
  position: fixed;
  z-index: 2147483647;
  left: 0;
  right: 0;
  top: 1%;
  width: fit-content;
  padding: 12px 20px;
  margin: auto;
  overflow: auto;
  background: #fef6f9;
  box-shadow: 0px 4px 10px 0px rgba(0, 10, 30, 0.06);
  font-size: 15px;
  border-radius: 8px;
  color: #333;
}

.immersive-translate-toast-content {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.immersive-translate-toast-hidden {
  margin: 0 20px 0 72px;
  text-decoration: underline;
  cursor: pointer;
}

.immersive-translate-toast-close {
  color: #666666;
  font-size: 20px;
  font-weight: bold;
  padding: 0 10px;
  cursor: pointer;
}

@media screen and (max-width: 768px) {
  .immersive-translate-toast {
    top: 0;
    padding: 12px 0px 0 10px;
  }
  .immersive-translate-toast-content {
    flex-direction: column;
    text-align: center;
  }
  .immersive-translate-toast-hidden {
    margin: 10px auto;
  }
}

.immersive-translate-modal {
  display: none;
  position: fixed;
  z-index: 2147483647;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgb(0, 0, 0);
  background-color: rgba(0, 0, 0, 0.4);
  font-size: 15px;
}

.immersive-translate-modal-content {
  background-color: #fefefe;
  margin: 10% auto;
  padding: 40px 24px 24px;
  border-radius: 12px;
  width: 350px;
  font-family: system-ui, -apple-system, "Segoe UI", "Roboto", "Ubuntu",
    "Cantarell", "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
    "Segoe UI Symbol", "Noto Color Emoji";
  position: relative;
}

@media screen and (max-width: 768px) {
  .immersive-translate-modal-content {
    margin: 50% auto !important;
  }
}

.immersive-translate-modal .immersive-translate-modal-content-in-input {
  max-width: 500px;
}
.immersive-translate-modal-content-in-input .immersive-translate-modal-body {
  text-align: left;
  max-height: unset;
}

.immersive-translate-modal-title {
  text-align: center;
  font-size: 16px;
  font-weight: 700;
  color: #333333;
}

.immersive-translate-modal-body {
  text-align: center;
  font-size: 14px;
  font-weight: 400;
  color: #333333;
  margin-top: 24px;
}

@media screen and (max-width: 768px) {
  .immersive-translate-modal-body {
    max-height: 250px;
    overflow-y: auto;
  }
}

.immersive-translate-close {
  color: #666666;
  position: absolute;
  right: 16px;
  top: 16px;
  font-size: 20px;
  font-weight: bold;
}

.immersive-translate-close:hover,
.immersive-translate-close:focus {
  text-decoration: none;
  cursor: pointer;
}

.immersive-translate-modal-footer {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 24px;
}

.immersive-translate-btn {
  width: fit-content;
  color: #fff;
  background-color: #ea4c89;
  border: none;
  font-size: 14px;
  margin: 0 8px;
  padding: 9px 30px;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.immersive-translate-btn-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.immersive-translate-btn:hover {
  background-color: #f082ac;
}
.immersive-translate-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
.immersive-translate-btn:disabled:hover {
  background-color: #ea4c89;
}

.immersive-translate-link-btn {
  background-color: transparent;
  color: #ea4c89;
  border: none;
  cursor: pointer;
  height: 30px;
  line-height: 30px;
}

.immersive-translate-cancel-btn {
  /* gray color */
  background-color: rgb(89, 107, 120);
}

.immersive-translate-cancel-btn:hover {
  background-color: hsl(205, 20%, 32%);
}

.immersive-translate-action-btn {
  background-color: transparent;
  color: #ea4c89;
  border: 1px solid #ea4c89;
}

.immersive-translate-btn svg {
  margin-right: 5px;
}

.immersive-translate-link {
  cursor: pointer;
  user-select: none;
  -webkit-user-drag: none;
  text-decoration: none;
  color: #ea4c89;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
}

.immersive-translate-primary-link {
  cursor: pointer;
  user-select: none;
  -webkit-user-drag: none;
  text-decoration: none;
  color: #ea4c89;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
}

.immersive-translate-modal input[type="radio"] {
  margin: 0 6px;
  cursor: pointer;
}

.immersive-translate-modal label {
  cursor: pointer;
}

.immersive-translate-close-action {
  position: absolute;
  top: 2px;
  right: 0px;
  cursor: pointer;
}

.imt-image-status {
  background-color: rgba(0, 0, 0, 0.5) !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 16px !important;
}
.imt-image-status img,
.imt-image-status svg,
.imt-img-loading {
  width: 28px !important;
  height: 28px !important;
  margin: 0 0 8px 0 !important;
  min-height: 28px !important;
  min-width: 28px !important;
  position: relative !important;
}
.imt-img-loading {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADgAAAA4CAMAAACfWMssAAAAtFBMVEUAAAD////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////oK74hAAAAPHRSTlMABBMIDyQXHwyBfFdDMSw+OjXCb+5RG51IvV/k0rOqlGRM6KKMhdvNyZBz9MaupmxpWyj437iYd/yJVNZeuUC7AAACt0lEQVRIx53T2XKiUBCA4QYOiyCbiAsuuGBcYtxiYtT3f6/pbqoYHVFO5r+iivpo6DpAWYpqeoFfr9f90DsYAuRSWkFnPO50OgR9PwiCUFcl2GEcx+N/YBh6pvKaefHlUgZd1zVe0NbYcQjGBfzrPE8Xz8aF+71D8gG6DHFPpc4a7xFiCDuhaWgKgGIJQ3d5IMGDrpS4S5KgpIm+en9f6PlAhKby4JwEIxlYJV9h5k5nee9GoxHJ2IDSNB0dwdad1NAxDJ/uXDHYmebdk4PdbkS58CIVHdYSUHTYYRWOJblWSyu2lmy3KNFVJNBhxcuGW4YBVCbYGRZwIooipHsNqjM4FbgOQqQqSKQQU9V8xmi1QlgHqQQ6DDBvRUVCDirs+EzGDGOQTCATgtYTnbCVLgsVgRE0T1QE0qHCFAht2z6dLvJQs3Lo2FQoDxWNUiBhaP4eRgwNkI+dAjVOA/kUrIDwf3CG8NfNOE0eiFotSuo+rBiq8tD9oY4Qzc6YJw99hl1wzpQvD7ef2M8QgnOGJfJw+EltQc+oX2yn907QB22WZcvlUpd143dqQu+8pCJZuGE4xCuPXJqqcs5sNpsI93Rmzym1k4Npk+oD1SH3/a3LOK/JpUBpWfqNySxWzCfNCUITuDG5dtuphrUJ1myeIE9bIsPiKrfqTai5WZxbhtNphYx6GEIHihyGFTI69lje/rxajdh0s0msZ0zYxyPLhYCb1CyHm9Qsd2H37Y3lugVwL9kNh8Ot8cha6fUNQ8nuXi5z9/ExsAO4zQrb/ev1yrCB7lGyQzgYDGuxq1toDN/JGvN+HyWNHKB7zEoK+PX11e12G431erGYzwmytAWU56fkMHY5JJnDRR2eZji3AwtIcrEV8Cojat/BdQ7XOwGV1e1hDjGGjXbdArm8uJZtCH5MbcctVX8A1WpqumJHwckAAAAASUVORK5CYII=");
  background-size: 28px 28px;
  animation: image-loading-rotate 1s linear infinite !important;
}

.imt-image-status span {
  color: var(--bg-2, #fff) !important;
  font-size: 14px !important;
  line-height: 14px !important;
  font-weight: 500 !important;
  font-family: "PingFang SC", Arial, sans-serif !important;
}

@keyframes image-loading-rotate {
  from {
    transform: rotate(360deg);
  }
  to {
    transform: rotate(0deg);
  }
}
</style></head>
<body scroll="no" class="hashover" style="overflow: hidden;"><div class="feedbackRoot"><!----></div>
    <div id="topPanel" style="display: none;">
        <div id="interfaceControlFrame" style="opacity: 1;">
            <div id="interfaceControlFrameLeft">
                <div id="interfaceControlFrameMinimizeContainer" class="">
                    <a title="Collapse" id="interfaceControlFrameMinimizeButton">
                        <div id="minimizeArrow" class="minimizeButtonHover minimizeIcon"></div>
                        <div id="minimizeX" class="minimizeButton minimizeIcon"></div>
                    </a>
                </div>
                <div id="interfaceControlFrameCloseContainer">
                    <a title="Close" id="interfaceControlFrameCloseButton">CLOSE</a>
                </div>

                <div id="sitemapControlFrameContainer" title="Project Pages" class="selected">
                    <div id="projectControlFrameHeaderContainer">
                        <ul id="projectControlFrameHeader"><li id="sitemapHostBtn"><a pluginid="sitemapHost" title="Project Pages" class="selected">&nbsp;</a></li></ul>
                    </div>

                    <div id="interfacePageNameContainer">
                        <div id="interfacePageNameButton" class="pageNameHeader">index</div>
                        <div id="interfacePageCount" class="pageCountHeader">(1 of 18)</div>
                    </div>
                </div>


                <div id="interfaceAdaptiveViewsContainer" title="Adaptive Views" style="display: none;">
                    <div id="interfaceAdaptiveViewsIconButton">&nbsp;</div>
                    <div id="interfaceAdaptiveViewsButton" class="adaptiveViewHeader"> (1922 x any)</div>
                    <div id="adaptiveViewsDropdown" class="caret"></div>
                </div>
            </div>

            <div id="interfaceControlFrameContainer">
                <div id="handoffControlFrameHeaderContainer">
                    <ul id="handoffControlFrameHeader"><li id="handoffPreviewHeaderItem" class="selected"><button id="handoffPreviewBtn" class="handoffHeaderBtn selected">
      <div id="handoffPreviewIcon" class="handoffHeaderIcon"><svg viewBox="0 0 18 12"><path d="M18 5.89l-.27-.359C17.57 5.322 13.8.401 9 .401 4.2.4.429 5.321.27 5.53L0 5.89l.269.359C.427 6.46 4.198 11.43 9 11.43s8.573-4.97 8.731-5.182L18 5.889zm-1.516.003c-.93 1.1-3.97 4.344-7.484 4.344-3.506 0-6.553-3.246-7.484-4.345.93-1.09 3.97-4.299 7.484-4.299 3.506 0 6.55 3.21 7.484 4.3zm-4.253.022c0-1.782-1.449-3.23-3.231-3.23a3.234 3.234 0 0 0-3.231 3.23A3.234 3.234 0 0 0 9 9.146a3.234 3.234 0 0 0 3.231-3.231zm-1.193 0A2.04 2.04 0 0 1 9 7.953a2.04 2.04 0 0 1-2.038-2.038A2.04 2.04 0 0 1 9 3.877a2.04 2.04 0 0 1 2.038 2.038z"></path></svg></div>
      <span>Preview</span>
    </button></li><li id="handoffInspectHeaderItem" class="selected"><button id="handoffInspectBtn" class="handoffHeaderBtn">
      <div id="handoffInspectIcon" class="handoffHeaderIcon"><svg viewBox="0 0 18 15"><path d="M17.209 14.504l.62-.417-3.237-5.498a11.076 11.076 0 0 0 2.196-1.672l.277-.271-2.169-1.99-.258.254a8.028 8.028 0 0 1-1.521 1.172L9.95.706 9 1.248 8.05.706l-3.18 5.4A8.031 8.031 0 0 1 3.31 4.91l-.258-.254-2.168 1.99.277.27A11.08 11.08 0 0 0 3.395 8.61L.17 14.087l.621.418 4.23-5.13c1.245.467 2.58.714 3.954.714 1.387 0 2.736-.252 3.992-.73l4.242 5.145zM5.538 6.454L8.326 1.72 9 2.105l.674-.385 2.777 4.714a8.057 8.057 0 0 1-.688.287L9 3.37 6.225 6.735a8 8 0 0 1-.687-.281zM9 4.55l1.987 2.411a8.22 8.22 0 0 1-3.984.012L9 4.549zM4.296 9.077l-1.057 1.28.821-1.394c.079.037.156.077.236.114zM1.977 6.659l1.064-.976a8.798 8.798 0 0 0 5.934 2.27 8.801 8.801 0 0 0 5.935-2.27l1.064.976a10.335 10.335 0 0 1-6.999 2.69 10.34 10.34 0 0 1-6.998-2.69zm12.782 3.697L13.69 9.06c.08-.036.158-.077.237-.117l.832 1.414z"></path></svg></div>
      <span>Inspect</span>
    </button></li></ul>
                </div>
                <div id="interfaceControlFrameLogoContainer">
                    <!--<div id="previewNotice">
                        Local Preview
                    </div>-->
                </div>
            </div>

            <div id="interfaceControlFrameRight">
                <div data-v-7ca801a7="" data-v-1b932aab="" data-v-59ecdcd7="" class="ax-menu share-button-container"><div data-v-7ca801a7="" role="button" aria-expanded="false" class="ax-menu__activator"><button data-v-be14ff3f="" data-v-59ecdcd7="" type="button" class="ax-button ax-button--primary ax-button--small share-button analytics-track-project-share" data-v-7ca801a7=""> Share </button></div><!----><!----></div>

                <div id="scaleMenuButton" title="Zoom and Scale" class="">
                    <div id="scaleValue">100%</div>
                    <div id="expandScaleArrow" class="caret"></div>
                </div>

                <div id="inspectControlFrameHeaderContainer">
                    <ul id="inspectControlFrameHeader">
                        <li id="overflowBtn">
                            <a id="overflowMenuButton" title="Options" class=""></a>
                        </li>
                    <li id="handoffHostBtn" style="display: none;"><a pluginid="handoffHost" title="Inspect" class=""><span></span></a></li><li id="pageNotesHostBtn"><a pluginid="pageNotesHost" title="Notes"><span></span></a></li><li id="feedbackHostBtn"><a pluginid="feedbackHost" title="Comments" class=""><span></span><div data-v-0885e1e7="" class="ax-comments-indicator"><!----></div></a></li></ul>
                </div>

                <div id="separatorContainer" class="hasLeft">
                    <div class="separator"></div>
                </div>

                <div id="overflowMadeWith"><a href="https://www.axure.com/" id="axureLogo" target="_blank"></a></div>

            </div>

        </div>
    </div>
    <div id="popupContainer">
        <div id="interfaceAdaptiveViewsListContainer" style="display: none;"><div class="adaptiveViewOption" val="auto"><div class="adapViewRadioButton selectedRadioButton"></div>Adaptive</div><div class="adaptiveViewOption currentAdaptiveView" val="default" data-dim="1922x0" cursor="1"><div class="adapViewRadioButton"></div> (1922 x any)</div></div>

        

        <div id="overflowMenuContainer" class="" style="display: none;"><div id="showCommentsOption" class="showOption" style="order: 2"><div class="overflowOptionCheckbox selected"></div>Show comments</div><div id="showNotesOption" class="showOption" style="order: 3"><div class="overflowOptionCheckbox selected"></div>Show note markers</div>
        <div id="showHotspotsOption" class="showOption" style="order: 1"><div class="overflowOptionCheckbox"></div>Show hotspots</div></div>

        <div id="scaleMenuContainer" class="" style="right: 207px; display: none;">
        <div class="vpZoomValue" val="25"><div class="zoomValue">25%</div></div><div class="vpZoomValue" val="50"><div class="zoomValue">50%</div></div><div class="vpZoomValue" val="75"><div class="zoomValue">75%</div></div><div class="vpZoomValue selected" val="100"><div class="zoomValue">100%</div></div><div class="vpZoomValue" val="150"><div class="zoomValue">150%</div></div><div class="vpZoomValue" val="200"><div class="zoomValue">200%</div></div><div class="vpZoomValue" val="250"><div class="zoomValue">250%</div></div><div class="vpZoomValue" val="300"><div class="zoomValue">300%</div></div><div class="vpZoomValue" val="400"><div class="zoomValue">400%</div></div><div id="interfaceScaleListContainer" style="display: block;">
        <div class="vpScaleOption" val="0"><div class="scaleRadioButton selectedRadioButton"></div>Default scale</div><div class="vpScaleOption" val="1"><div class="scaleRadioButton"></div>Scale to width</div><div class="vpScaleOption" val="2"><div class="scaleRadioButton"></div>Scale to fit</div><div class="vpScaleOption" val="3" hidden="" style="display: block;"><div class="scaleRadioButton"></div>User scale</div></div></div>
    </div>
    <div id="outerContainer" style="height: 746px; width: 1488px;"><div id="sitemapHost" class="leftPanel" style="width: 220px; display: none;"><div id="sitemapHeader" '="" class="sitemapHeader"><div id="sitemapToolbar" class="sitemapToolbar"><div class="toolbarRow"><div class="pluginNameHeader">大模型漏洞挖掘平台_RP11_B1.0 (1)</div><div class="leftArrow sitemapToolbarButton"></div><div class="rightArrow sitemapToolbarButton"></div></div><div class="toolbarRow"><div id="searchDiv"><span id="searchIcon" class="sitemapToolbarButton"></span><input id="searchBox" type="text" class="searchBoxHint"></div><div id="expandCollapseAll">Collapse all</div></div></div></div><div id="sitemapTreeContainer"><ul class="sitemapTree" style="clear:both;"><li class="sitemapNode sitemapExpandableNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer sitemapPlusMinusLink" style="margin-left:9px"><span class="sitemapMinus"></span><span class="sitemapPageIcon sitemapFolderIcon"></span><span class="sitemapPageName">原型-浅色</span></div></div><ul style=""><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover sitemapHighlight"><div class="sitemapPageLinkContainer" style="margin-left:36px"><a class="sitemapPageLink" nodeurl="index.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">index</span></a></div></div></li><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:36px"><a class="sitemapPageLink" nodeurl="index-__.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">index-收起</span></a></div></div></li><li class="sitemapNode sitemapExpandableNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer sitemapPlusMinusLink" style="margin-left:26px"><span class="sitemapPlus"></span><span class="sitemapPageIcon sitemapFolderIcon"></span><span class="sitemapPageName">项目管理</span></div></div><ul style="display: none;"><li class="sitemapNode sitemapExpandableNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:43px"><a class="sitemapPlusMinusLink"><span class="sitemapMinus"></span></a><a class="sitemapPageLink" nodeurl="项目列表.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">项目列表</span></a></div></div><ul><li class="sitemapNode sitemapExpandableNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:60px"><a class="sitemapPlusMinusLink"><span class="sitemapMinus"></span></a><a class="sitemapPageLink" nodeurl="任务列表.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">任务列表</span></a></div></div><ul><li class="sitemapNode sitemapExpandableNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:77px"><a class="sitemapPlusMinusLink"><span class="sitemapMinus"></span></a><a class="sitemapPageLink" nodeurl="任务概览.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">任务概览</span></a></div></div><ul><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:104px"><a class="sitemapPageLink" nodeurl="缺陷详情.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">缺陷详情</span></a></div></div></li><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:104px"><a class="sitemapPageLink" nodeurl="ai分析流程全屏.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">AI分析流程全屏</span></a></div></div></li></ul></li><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:87px"><a class="sitemapPageLink" nodeurl="任务概览全.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">任务概览全</span></a></div></div></li></ul></li></ul></li></ul></li><li class="sitemapNode sitemapExpandableNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer sitemapPlusMinusLink" style="margin-left:26px"><span class="sitemapPlus"></span><span class="sitemapPageIcon sitemapFolderIcon"></span><span class="sitemapPageName">缺陷管理</span></div></div><ul style="display: none;"><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:53px"><a class="sitemapPageLink" nodeurl="缺陷列表.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">缺陷列表</span></a></div></div></li><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:53px"><a class="sitemapPageLink" nodeurl="缺陷详情_1.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">缺陷详情</span></a></div></div></li></ul></li><li class="sitemapNode sitemapExpandableNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer sitemapPlusMinusLink" style="margin-left:26px"><span class="sitemapPlus"></span><span class="sitemapPageIcon sitemapFolderIcon"></span><span class="sitemapPageName">报告管理</span></div></div><ul style="display: none;"><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:53px"><a class="sitemapPageLink" nodeurl="报告管理.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">报告管理</span></a></div></div></li></ul></li><li class="sitemapNode sitemapExpandableNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer sitemapPlusMinusLink" style="margin-left:26px"><span class="sitemapPlus"></span><span class="sitemapPageIcon sitemapFolderIcon"></span><span class="sitemapPageName">模型配置</span></div></div><ul style="display: none;"><li class="sitemapNode sitemapExpandableNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:43px"><a class="sitemapPlusMinusLink"><span class="sitemapMinus"></span></a><a class="sitemapPageLink" nodeurl="配置列表.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">配置列表</span></a></div></div><ul><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:70px"><a class="sitemapPageLink" nodeurl="配置详情.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">配置详情</span></a></div></div></li></ul></li></ul></li></ul></li><li class="sitemapNode sitemapExpandableNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer sitemapPlusMinusLink" style="margin-left:9px"><span class="sitemapPlus"></span><span class="sitemapPageIcon sitemapFolderIcon"></span><span class="sitemapPageName">原型-深色</span></div></div><ul style="display: none;"><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:36px"><a class="sitemapPageLink" nodeurl="index_1.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">index</span></a></div></div></li><li class="sitemapNode sitemapExpandableNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer sitemapPlusMinusLink" style="margin-left:26px"><span class="sitemapPlus"></span><span class="sitemapPageIcon sitemapFolderIcon"></span><span class="sitemapPageName">项目管理</span></div></div><ul style="display: none;"><li class="sitemapNode sitemapExpandableNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:43px"><a class="sitemapPlusMinusLink"><span class="sitemapMinus"></span></a><a class="sitemapPageLink" nodeurl="项目列表_1.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">项目列表</span></a></div></div><ul><li class="sitemapNode sitemapExpandableNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:60px"><a class="sitemapPlusMinusLink"><span class="sitemapMinus"></span></a><a class="sitemapPageLink" nodeurl="任务列表_1.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">任务列表</span></a></div></div><ul><li class="sitemapNode sitemapExpandableNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:77px"><a class="sitemapPlusMinusLink"><span class="sitemapMinus"></span></a><a class="sitemapPageLink" nodeurl="任务概览_1.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">任务概览</span></a></div></div><ul><li class="sitemapNode sitemapLeafNode"><div class="sitemapHover"><div class="sitemapPageLinkContainer" style="margin-left:104px"><a class="sitemapPageLink" nodeurl="缺陷详情_2.html"><span class="sitemapPageIcon"></span><span class="sitemapPageName">缺陷详情</span></a></div></div></li></ul></li></ul></li></ul></li></ul></li></ul></li></ul></div><div id="changePageInstructions" class="pageSwapInstructions">Use  <span class="backKeys"></span>  and  <span class="forwardKeys"></span>  keys<br>to move between pages</div></div>
        <div id="clippingBounds" style="height: 746px; left: 0px; width: 1488px; opacity: 1;"><div id="notesOverlay">&nbsp;</div>
            <div id="clippingBoundsScrollContainer" style="left: 0px; top: 0px;"><div data-v-35d35e8c="" class="feedback-pins-container"><!----></div></div>
        </div>

        <div id="mHideSidebar"></div>
        <div id="lsplitbar" class="splitbar" style="left: 216px; display: none;"></div>
        <div id="mainPanel" style="height: 746px; opacity: 1; width: 1488px;">
            <div id="mainPanelContainer" style="position: relative; width: 100%; height: 746px;" data-page-dimensions-type="web" data-scale-n="0">
                <div id="clipFrameScroll" style="position: relative; width: 100%; height: 746px; background-color: rgb(255, 255, 255); overflow: hidden;">
                    <iframe id="mainFrame" name="mainFrame" width="100%" height="100%" src="./index_files/saved_resource.html" frameborder="0" style="display: block; position: absolute; width: 100%; height: 746px;" webkitallowfullscreen="" mozallowfullscreen="" allowfullscreen=""></iframe>
                <div id="handoffViewport" style="width: 1473px; height: 691px;"><div id="handoffMarkupContainer" style="pointer-events: none; width: 1922px; height: 918px; left: 0px; top: 0px; display: none;">
  <div id="handoffOver" class="handoffOver" style="left: 273px; top: 0px; width: 1624px; height: 691px; display: none;"></div>
  <div id="handoffOverT" class="handoffOver handoffOverGuide handoffOverGuideV" style="top: 0px; left: 1085px; height: 0px; display: none;">
    <div id="handoffOverTValue" class="handoffOverGuideValue">0px</div>
  </div>
  <div id="handoffOverR" class="handoffOver handoffOverGuide handoffOverGuideH" style="top: 0px; left: 0px; width: 273px; display: none;">
    <div id="handoffOverRValue" class="handoffOverGuideValue">273px</div>
  </div>
  <div id="handoffOverB" class="handoffOver handoffOverGuide handoffOverGuideV" style="top: 0px; left: 0px; height: 0px; display: none;">
    <div id="handoffOverBValue" class="handoffOverGuideValue"></div>
  </div>
  <div id="handoffOverL" class="handoffOver handoffOverGuide handoffOverGuideH" style="top: 0px; left: 0px; width: 0px; display: none;">
    <div id="handoffOverLValue" class="handoffOverGuideValue"></div>
  </div>
  <div id="handoffSelected" class="handoffSelected" style="display: none; left: 0px; top: 0px; width: 0px; height: 0px;">
    <div id="handoffSelectedWidth" class="handoffDim" style="display: none;"></div>
    <div id="handoffSelectedHeight" class="handoffDim" style="display: none;"></div>
  </div>
  <div id="handoffSelectedV" class="handoffSelected handoffSelectedGuide" style="display: none; left: 0px; width: 0px;"></div>
  <div id="handoffSelectedH" class="handoffSelected handoffSelectedGuide" style="display: none; top: 0px; height: 0px;"></div>
</div></div></div>
            </div>
        <div data-v-1383c65b="" class="feedback-add-pin" style="display: none;"><div data-v-1383c65b="" class="feedback-add-pin-overlay" style="border-width: 9px;"></div></div><!----><!----></div>

        <div id="rsplitbar" class="splitbar" style="left: 1488px; display: none;"></div>
    <div id="handoffHost" class="rightPanel" style="width: 0px; display: none;"><div class="closeButtonContainer"><button></button></div><div id="handoffScrollContainer">
  <div id="handoffContainer">
    <div id="handoffModeLinkContainer" class="handoffLinksContainer">
      <div class="dottedDivider"></div>
    </div>
  <div id="handoffProjectInfo" class="handoffLayerSection" style="">
<div id="handoffProjectName" class="handoffSectionName handoffLayerValue"></div><h1 class="handoff-inspect-header">Inspect</h1>
  <div class="handoff-call-to-action">
    <p><span class="handoff-caret handoff-caret-left"></span>&nbsp;Click on a layer in your prototype to view dimensions &amp; colors, download assets and copy CSS.</p>
  </div></div><div id="handoffArtboardColors" style="display: none;"><div class="handoffLayerViewRow">
  <div class="handoffLayerViewCell">
    <span class="handoffCloseSection handoffCloseIcon">×</span>
  </div>
</div><div class="handoffLayerSection"><div class="handoffSectionName">Colors</div><div class="handoffLayerSectionBody handoffLayerSectionContentBody"><div id="handoffArtboardFillsList"></div></div></div></div><div id="handoffArtboardAssets" style="display: none;"><div class="handoffLayerViewRow">
  <div class="handoffLayerViewCell">
    <span class="handoffCloseSection handoffCloseIcon">×</span>
  </div>
</div><div class="handoffLayerSection"><div class="handoffSectionName">Assets</div><div class="handoffLayerSectionBody handoffLayerSectionContentBody">
  <div id="handoffArtboardAssetsList"></div>
  <div id="handoffArtboardAssetsLayerList"></div>
<div></div></div></div></div><div id="handoffLayerView" style="display: none"><div id="handoffLayerNameSection">
  <div id="handoffLayerName"></div>
</div><div id="handoffStateSelectorSection" class="handoffLayerSection">
</div><div id="handoffDimensionsSection" class="handoffLayerSection">
  <div class="handoffSectionName">Size and Position</div>
  <div class="handoffLayerSectionBody">
    <div class="handoffLayerViewRow">
      <div class="handoffLayerViewCell">
        X: <span id="handoffDimensionsX" class="handoffLayerValue"></span>
      </div>
      <div class="handoffLayerViewCell">
        Y: <span id="handoffDimensionsY" class="handoffLayerValue"></span>
      </div>
    </div>
    <div class="handoffLayerViewRow">
      <div class="handoffLayerViewCell">
        Width: <span id="handoffDimensionsWidth" class="handoffLayerValue"></span>
      </div>
      <div class="handoffLayerViewCell">
        Height: <span id="handoffDimensionsHeight" class="handoffLayerValue"></span>
      </div>
    </div>
    <div class="handoffLayerViewRow">
      <div class="handoffLayerViewCell">
        Rotation: <span id="handoffDimensionsRotation" class="handoffLayerValue"></span>
      </div>
      <div class="handoffLayerViewCell">
        Radius: <span id="handoffDimensionsRadius" class="handoffLayerValue"></span>
      </div>
    </div>
    <div id="handoffDimensionsPaddingRow" class="handoffLayerViewRow">
      <div class="handoffLayerViewCell">
        Padding: <span id="handoffDimensionsPadding" class="handoffLayerValue"></span>
      </div>
    </div>
    <div class="handoffLayerViewRow">
      <div class="handoffLayerViewCell">
        Opacity: <span id="handoffDimensionsOpacity" class="handoffLayerValue"></span>
      </div>
    </div>
  </div>
</div><div id="handoffLayerAssetsSection" class="handoffLayerSection">
  <div class="handoffSectionName">
    <span>Assets</span>
    <div class="download-all-assets-container">
      <button type="button" class="handoff-download-btn">Download All</button>
    </div>
  </div>

  <div class="handoffLayerSectionBody">

  </div>
</div><div id="handoffLayerContentSection" class="handoffLayerSection">
  <div class="handoffSectionName">
    <span>Content</span>
    <div class="copy-to-clipboard-container"><div class="handoff-copy-to-clipboard">
  <div class="copy-to-clipboard-toast" style="display: none;">Copied To Clipboard</div>
  <button type="button" title="Copy to clipboard" class="copy-to-clipboard-btn">
  </button>
</div></div>
  </div>
  <div class="handoffLayerSectionBody handoffLayerSectionContentBody">
    <div class="handoffLayerViewRow">
      <div class="handoffLayerViewCell">
        <div class="handoffTextContentContainer">
          <div id="handoffTextContent" class="handoffLayerValue"></div>
        </div>
      </div>
    </div>
  </div>
</div><div id="handoffLayerTypefaceSection" class="handoffLayerSection">
  <div class="handoffSectionName">Typography</div>
  <div class="handoffLayerSectionBody">
    <div class="handoffLayerViewRow">
      <div class="handoffLayerViewCell">
        Typeface: <span id="handoffFontName" class="handoffLayerValue"></span>
      </div>
    </div>
    <div id="handoffFontProperties">
      <div class="handoffLayerViewRow">
        <div class="handoffLayerViewCell">
          Size: <span id="handoffFontSize" class="handoffLayerValue"></span>
        </div>
        <div class="handoffLayerViewCell">
          Align: <span id="handoffFontAlignment" class="handoffLayerValue"></span>
        </div>
      </div>
      <div class="handoffLayerViewRow">
        <div id="handoffTextColorContainer" class="handoffLayerViewCell">
      </div>
    </div>
  </div>
</div></div><div id="handoffLayerFillsSection" class="handoffLayerSection">
  <div class="handoffSectionName">Fill Color</div>
  <div class="handoffLayerSectionBody">
    <div class="handoffLayerViewRow">
      <div id="handoffFillsListContainer" class="handoffLayerViewCell">
      </div>
    </div>
  </div>
</div><div id="handoffLayerBordersSection" class="handoffLayerSection">
  <div class="handoffSectionName">Border</div>
  <div class="handoffLayerSectionBody">
    <div class="handoffLayerViewRow">
      <div class="handoffLayerViewCell">
        Width: <span id="handoffBorderWidth" class="handoffLayerValue"></span>
      </div>
      <div class="handoffLayerViewCell">
        Position: <span id="handoffBorderAlignment" class="handoffLayerValue"></span>
      </div>
    </div>
    <div class="handoffLayerViewRow">
      <div id="handoffFillsListContainer" class="handoffLayerViewCell">
      </div>
    </div>
  </div>
</div><div id="handoffLayerShadowsSection" class="handoffLayerSection">
  <div class="handoffSectionName">Shadows</div>
  <div class="handoffLayerSectionBody">
      <div id="handoffShadowsListContainer" class="handoffLayerViewCell">
      </div>
    </div>
</div><div id="handoffStyleEffects">
</div><div id="handoffLayerHtmlSection" class="handoffLayerSection">
  <div class="handoffSectionName">HTML</div>
  <div class="handoffLayerSectionBody handoffLayerSectionContentBody">
    <div class="handoffLayerViewRow">
      <div class="handoffLayerViewCell">
        <span id="handoffHtmlContent" class="handoffLayerValue"></span>
      </div>
    </div>
  </div>
</div><div id="cssContainer" class="handoffLayerSection">
  <div class="handoffSectionName">
    <span>CSS</span>
    <div class="copy-to-clipboard-container"><div class="handoff-copy-to-clipboard">
  <div class="copy-to-clipboard-toast" style="display: none;">Copied To Clipboard</div>
  <button type="button" title="Copy to clipboard" class="copy-to-clipboard-btn">
  </button>
</div></div>
  </div>
  <div class="handoffLayerSectionBody">
    <div class="handoffLayerViewRow">
      <div id="cssDiv" class="handoffLayerViewCell">
      <div class="cm-editor ͼ1 ͼ3 ͼ4 ͼo ͼ15" style="height: auto;"><div class="cm-announced" aria-live="polite"></div><div tabindex="-1" class="cm-scroller"><div class="cm-gutters" aria-hidden="true" style="min-height: 14px; position: sticky;"><div class="cm-gutter cm-lineNumbers"><div class="cm-gutterElement" style="height: 0px; visibility: hidden; pointer-events: none;">9</div><div class="cm-gutterElement" style="height: 14px;">1</div></div></div><div spellcheck="false" autocorrect="off" autocapitalize="off" translate="no" contenteditable="true" class="cm-content cm-lineWrapping" role="textbox" aria-multiline="true" aria-readonly="true" data-language="css" style="tab-size: 4;"><div class="cm-line"><br></div></div><div class="cm-layer cm-layer-above cm-cursorLayer" aria-hidden="true" style="z-index: 150; animation-duration: 1200ms;"></div><div class="cm-layer cm-selectionLayer" aria-hidden="true" style="z-index: -2;"></div></div></div></div>
    </div>
  </div>
</div></div></div>
</div></div><div id="pageNotesHost" class="rightPanel" style="display: none; width: 0px;"><div class="closeButtonContainer"><button></button></div><div id="pageNotesHeader"><div class="pluginNameHeader">Widget notes</div></div><div id="pageNotesScrollContainer"><div id="pageNotesContainer"><div id="pageNotesEmptyState" class="emptyStateContainer"><div class="emptyStateTitle">No notes</div><div class="emptyStateContent">Notes added in Axure RP will appear here</div></div><span id="pageNotesContent"></span></div></div></div><div id="feedbackHost" class="rightPanel" style="width: 0px; display: none;"><div class="closeButtonContainerFb"><button></button></div><div data-v-6eaac34b="" class="feedback-panel"><button data-v-6eaac34b="" type="button" style="display: none;"></button><div data-v-6eaac34b="" class="feedback-panel-add-comment"><button data-v-be14ff3f="" data-v-6eaac34b="" type="button" class="feedback-panel-add-comment-button ax-button ax-button--default ax-button--block ax-button--large"> Add comment </button></div><div data-v-6eaac34b="" class="feedback-panel-header"><div data-v-6eaac34b="" class="feedback-panel-header-row"><div data-v-6eaac34b="" class="feedback-panel-header__mode"><div data-v-7ca801a7="" data-v-1b932aab="" data-v-fc2ddb7f="" data-v-6eaac34b="" class="ax-menu" value="0"><div data-v-7ca801a7="" role="button" aria-expanded="false" class="ax-menu__activator"><button data-v-fc2ddb7f="" data-v-7ca801a7="" type="button" class="filter__activator"><svg data-v-fc2ddb7f="" width="15" height="16" fill="none" viewBox="0 0 15 16" class="icon-filter ax-svg-icon icon-filter--off" data-v-7ca801a7=""><path fill="currentColor" d="M5.543 15.696c.12.054.26.072.36.072.2 0 .36-.054.54-.18l2.8-2.016c.2-.144.3-.36.3-.576V7.812l4.76-5.796c.3-.378.36-.882.12-1.314S13.683 0 13.143 0H1.423c-.54 0-1 .27-1.26.702s-.2.936.12 1.314l4.76 5.796v7.236c.02.27.22.522.5.648M1.983 1.512h10.64l-4.6 5.598c-.12.126-.16.27-.16.45v5.094l-1.12.81V7.56a.8.8 0 0 0-.16-.45z"></path></svg>  </button></div><!----><!----></div></div><div data-v-6eaac34b="" class="feedback-panel-header-row__mark-as-read--hidden"><button data-v-be14ff3f="" data-v-6eaac34b="" type="button" class="feedback-panel-header-row__mark-as-read-button ax-button ax-button--link ax-button--small"> Mark all read </button></div></div></div><div data-v-6eaac34b="" class="feedback-panel__issues feedback-panel__loading-mask" style="display: none;"><div data-v-be17268c="" class="feedback-panel__loading-mask-item ax-loading-mask"><div data-v-be17268c="" class="mask-item" style="left: 40px; right: 0px; height: 14px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="left: 40px; top: 14px; width: 10px; height: 12px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="left: 175px; right: 0px; top: 14px; height: 12px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="left: 40px; right: 0px; top: 26px; height: 14px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="top: 40px; width: 100%; height: 15px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="top: 65px; width: 100%; height: 6px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="top: 81px; width: 100%; height: 6px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="top: 87px; width: 100%; height: 5px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="left: 60px; right: 0px; top: 92px; height: 10px; background-color: rgb(242, 242, 242);"></div></div><div data-v-be17268c="" class="feedback-panel__loading-mask-item ax-loading-mask"><div data-v-be17268c="" class="mask-item" style="left: 40px; right: 0px; height: 14px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="left: 40px; top: 14px; width: 10px; height: 12px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="left: 175px; right: 0px; top: 14px; height: 12px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="left: 40px; right: 0px; top: 26px; height: 14px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="top: 40px; width: 100%; height: 15px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="top: 65px; width: 100%; height: 6px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="top: 81px; width: 100%; height: 6px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="top: 87px; width: 100%; height: 5px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="left: 60px; right: 0px; top: 92px; height: 10px; background-color: rgb(242, 242, 242);"></div></div><div data-v-be17268c="" class="feedback-panel__loading-mask-item ax-loading-mask"><div data-v-be17268c="" class="mask-item" style="left: 40px; right: 0px; height: 14px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="left: 40px; top: 14px; width: 10px; height: 12px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="left: 175px; right: 0px; top: 14px; height: 12px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="left: 40px; right: 0px; top: 26px; height: 14px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="top: 40px; width: 100%; height: 15px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="top: 65px; width: 100%; height: 6px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="top: 81px; width: 100%; height: 6px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="top: 87px; width: 100%; height: 5px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="left: 60px; right: 0px; top: 92px; height: 10px; background-color: rgb(242, 242, 242);"></div></div><div data-v-be17268c="" class="feedback-panel__loading-mask-item ax-loading-mask"><div data-v-be17268c="" class="mask-item" style="left: 40px; right: 0px; height: 14px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="left: 40px; top: 14px; width: 10px; height: 12px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="left: 175px; right: 0px; top: 14px; height: 12px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="left: 40px; right: 0px; top: 26px; height: 14px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="top: 40px; width: 100%; height: 15px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="top: 65px; width: 100%; height: 6px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="top: 81px; width: 100%; height: 6px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="top: 87px; width: 100%; height: 5px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="left: 60px; right: 0px; top: 92px; height: 10px; background-color: rgb(242, 242, 242);"></div></div><div data-v-be17268c="" class="feedback-panel__loading-mask-item ax-loading-mask"><div data-v-be17268c="" class="mask-item" style="left: 40px; right: 0px; height: 14px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="left: 40px; top: 14px; width: 10px; height: 12px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="left: 175px; right: 0px; top: 14px; height: 12px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="left: 40px; right: 0px; top: 26px; height: 14px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="top: 40px; width: 100%; height: 15px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="top: 65px; width: 100%; height: 6px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="top: 81px; width: 100%; height: 6px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="top: 87px; width: 100%; height: 5px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="left: 60px; right: 0px; top: 92px; height: 10px; background-color: rgb(242, 242, 242);"></div></div><div data-v-be17268c="" class="feedback-panel__loading-mask-item ax-loading-mask"><div data-v-be17268c="" class="mask-item" style="left: 40px; right: 0px; height: 14px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="left: 40px; top: 14px; width: 10px; height: 12px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="left: 175px; right: 0px; top: 14px; height: 12px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="left: 40px; right: 0px; top: 26px; height: 14px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="top: 40px; width: 100%; height: 15px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="top: 65px; width: 100%; height: 6px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="top: 81px; width: 100%; height: 6px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="top: 87px; width: 100%; height: 5px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="left: 60px; right: 0px; top: 92px; height: 10px; background-color: rgb(242, 242, 242);"></div></div><div data-v-be17268c="" class="feedback-panel__loading-mask-item ax-loading-mask"><div data-v-be17268c="" class="mask-item" style="left: 40px; right: 0px; height: 14px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="left: 40px; top: 14px; width: 10px; height: 12px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="left: 175px; right: 0px; top: 14px; height: 12px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="left: 40px; right: 0px; top: 26px; height: 14px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="top: 40px; width: 100%; height: 15px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="top: 65px; width: 100%; height: 6px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="top: 81px; width: 100%; height: 6px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="top: 87px; width: 100%; height: 5px; background-color: rgb(242, 242, 242);"></div><div data-v-be17268c="" class="mask-item" style="left: 60px; right: 0px; top: 92px; height: 10px; background-color: rgb(242, 242, 242);"></div></div></div><div data-v-c8c36a1c="" data-v-6eaac34b="" class="feedback-panel-issues feedback-panel__issues" style=""><div data-v-c8c36a1c="" class="feedback-panel-empty-state"><div data-v-c8c36a1c="" class="feedback-panel-empty-state__title"> No Comments </div><div data-v-c8c36a1c=""> Add a comment to give feedback, ask a question, or request a change. </div></div></div><div data-v-0d08c8a2="" data-v-6eaac34b="" class="reload-panel feedback-panel__reload-button" style="display: none;"><span data-v-0d08c8a2="">New comments available </span><button data-v-be14ff3f="" data-v-0d08c8a2="" type="button" class="reload-link ax-button ax-button--link ax-button--small"> Click to Refresh </button></div><!----></div></div></div>

    <div id="maximizePanelContainer" class="maximizePanelContainer" style="display: block;">
        <div id="maximizePanelOver">
            <div id="maximizePanel" title="Expand" class="" style="left: -28px;">
                <div id="maximizeButton" class="maximizeButton maximizeIcon rotated"></div>
            </div>
        </div>
    </div>

    <div id="mobileControlFrameContainer"></div>

<!-- 11.0.0.4114 -->
<script src="./index_files/jquery-3.7.1.min.js.下载"></script>
<script src="./index_files/jquery.nicescroll.min.js.下载"></script>
<script src="./index_files/axutils.js.下载"></script>
<script src="./index_files/messagecenter.js.下载"></script>
<script src="./index_files/axplayer.js.下载"></script>
<script src="./index_files/init.js.下载"></script>


<script type="text/javascript">
                    (function() {
                        if ($axure && $axure.loadDocument) {
                            window.parent.postMessage("isAxurePrototype", "*");
                            var origLoadDocument = $axure.loadDocument;
                            $axure.loadDocument = function(doc) {
                                doc.configuration.prototypeId = 'VBQ4F3';
                                doc.configuration.projectName = '\u5927\u6A21\u578B\u6F0F\u6D1E\u6316\u6398\u5E73\u53F0_RP11_B1.0 (1)';
                                origLoadDocument(doc);
                            };
                        }
                    })();
                </script><script src="./index_files/Expo.zDk5XH0m.js.下载"></script><link href="./index_files/style-6uiOxtgW.css" rel="stylesheet"><script src="./index_files/AxLib.DsUmJCqb.js.下载"></script><link href="./index_files/style-C5VoKpGf.css" rel="stylesheet">

</body><div id="immersive-translate-popup" style="all: initial"><template shadowrootmode="open"><style>@charset "UTF-8";
/*!
 * Pico.css v1.5.6 (https://picocss.com)
 * Copyright 2019-2022 - Licensed under MIT
 */
/**
 * Theme: default
 */
#mount {
  --font-family: system-ui, -apple-system, "Segoe UI", "Roboto", "Ubuntu",
    "Cantarell", "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
    "Segoe UI Symbol", "Noto Color Emoji";
  --line-height: 1.5;
  --font-weight: 400;
  --font-size: 16px;
  --border-radius: 0.25rem;
  --border-width: 1px;
  --outline-width: 3px;
  --spacing: 1rem;
  --typography-spacing-vertical: 1.5rem;
  --block-spacing-vertical: calc(var(--spacing) * 2);
  --block-spacing-horizontal: var(--spacing);
  --grid-spacing-vertical: 0;
  --grid-spacing-horizontal: var(--spacing);
  --form-element-spacing-vertical: 0.75rem;
  --form-element-spacing-horizontal: 1rem;
  --nav-element-spacing-vertical: 1rem;
  --nav-element-spacing-horizontal: 0.5rem;
  --nav-link-spacing-vertical: 0.5rem;
  --nav-link-spacing-horizontal: 0.5rem;
  --form-label-font-weight: var(--font-weight);
  --transition: 0.2s ease-in-out;
  --modal-overlay-backdrop-filter: blur(0.25rem);
}
@media (min-width: 576px) {
  #mount {
    --font-size: 17px;
  }
}
@media (min-width: 768px) {
  #mount {
    --font-size: 18px;
  }
}
@media (min-width: 992px) {
  #mount {
    --font-size: 19px;
  }
}
@media (min-width: 1200px) {
  #mount {
    --font-size: 20px;
  }
}

@media (min-width: 576px) {
  #mount > header,
  #mount > main,
  #mount > footer,
  section {
    --block-spacing-vertical: calc(var(--spacing) * 2);
  }
}
@media (min-width: 768px) {
  #mount > header,
  #mount > main,
  #mount > footer,
  section {
    --block-spacing-vertical: calc(var(--spacing) * 2.5);
  }
}
@media (min-width: 992px) {
  #mount > header,
  #mount > main,
  #mount > footer,
  section {
    --block-spacing-vertical: calc(var(--spacing) * 3);
  }
}
@media (min-width: 1200px) {
  #mount > header,
  #mount > main,
  #mount > footer,
  section {
    --block-spacing-vertical: calc(var(--spacing) * 3.5);
  }
}

@media (min-width: 576px) {
  article {
    --block-spacing-horizontal: calc(var(--spacing) * 1.25);
  }
}
@media (min-width: 768px) {
  article {
    --block-spacing-horizontal: calc(var(--spacing) * 1.5);
  }
}
@media (min-width: 992px) {
  article {
    --block-spacing-horizontal: calc(var(--spacing) * 1.75);
  }
}
@media (min-width: 1200px) {
  article {
    --block-spacing-horizontal: calc(var(--spacing) * 2);
  }
}

dialog > article {
  --block-spacing-vertical: calc(var(--spacing) * 2);
  --block-spacing-horizontal: var(--spacing);
}
@media (min-width: 576px) {
  dialog > article {
    --block-spacing-vertical: calc(var(--spacing) * 2.5);
    --block-spacing-horizontal: calc(var(--spacing) * 1.25);
  }
}
@media (min-width: 768px) {
  dialog > article {
    --block-spacing-vertical: calc(var(--spacing) * 3);
    --block-spacing-horizontal: calc(var(--spacing) * 1.5);
  }
}

a {
  --text-decoration: none;
}
a.secondary,
a.contrast {
  --text-decoration: underline;
}

small {
  --font-size: 0.875em;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  --font-weight: 700;
}

h1 {
  --font-size: 2rem;
  --typography-spacing-vertical: 3rem;
}

h2 {
  --font-size: 1.75rem;
  --typography-spacing-vertical: 2.625rem;
}

h3 {
  --font-size: 1.5rem;
  --typography-spacing-vertical: 2.25rem;
}

h4 {
  --font-size: 1.25rem;
  --typography-spacing-vertical: 1.874rem;
}

h5 {
  --font-size: 1.125rem;
  --typography-spacing-vertical: 1.6875rem;
}

[type="checkbox"],
[type="radio"] {
  --border-width: 2px;
}

[type="checkbox"][role="switch"] {
  --border-width: 2px;
}

thead th,
thead td,
tfoot th,
tfoot td {
  --border-width: 3px;
}

:not(thead, tfoot) > * > td {
  --font-size: 0.875em;
}

pre,
code,
kbd,
samp {
  --font-family: "Menlo", "Consolas", "Roboto Mono", "Ubuntu Monospace",
    "Noto Mono", "Oxygen Mono", "Liberation Mono", monospace,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

kbd {
  --font-weight: bolder;
}

[data-theme="light"],
#mount:not([data-theme="dark"]) {
  --background-color: #fff;
  --background-light-green: #f5f7f9;
  --color: hsl(205deg, 20%, 32%);
  --h1-color: hsl(205deg, 30%, 15%);
  --h2-color: #24333e;
  --h3-color: hsl(205deg, 25%, 23%);
  --h4-color: #374956;
  --h5-color: hsl(205deg, 20%, 32%);
  --h6-color: #4d606d;
  --muted-color: hsl(205deg, 10%, 50%);
  --muted-border-color: hsl(205deg, 20%, 94%);
  --primary: hsl(195deg, 85%, 41%);
  --primary-hover: hsl(195deg, 90%, 32%);
  --primary-focus: rgba(16, 149, 193, 0.125);
  --primary-inverse: #fff;
  --secondary: hsl(205deg, 15%, 41%);
  --secondary-hover: hsl(205deg, 20%, 32%);
  --secondary-focus: rgba(89, 107, 120, 0.125);
  --secondary-inverse: #fff;
  --contrast: hsl(205deg, 30%, 15%);
  --contrast-hover: #000;
  --contrast-focus: rgba(89, 107, 120, 0.125);
  --contrast-inverse: #fff;
  --mark-background-color: #fff2ca;
  --mark-color: #543a26;
  --ins-color: #388e3c;
  --del-color: #c62828;
  --blockquote-border-color: var(--muted-border-color);
  --blockquote-footer-color: var(--muted-color);
  --button-box-shadow: 0 0 0 rgba(0, 0, 0, 0);
  --button-hover-box-shadow: 0 0 0 rgba(0, 0, 0, 0);
  --form-element-background-color: transparent;
  --form-element-border-color: hsl(205deg, 14%, 68%);
  --form-element-color: var(--color);
  --form-element-placeholder-color: var(--muted-color);
  --form-element-active-background-color: transparent;
  --form-element-active-border-color: var(--primary);
  --form-element-focus-color: var(--primary-focus);
  --form-element-disabled-background-color: hsl(205deg, 18%, 86%);
  --form-element-disabled-border-color: hsl(205deg, 14%, 68%);
  --form-element-disabled-opacity: 0.5;
  --form-element-invalid-border-color: #c62828;
  --form-element-invalid-active-border-color: #d32f2f;
  --form-element-invalid-focus-color: rgba(211, 47, 47, 0.125);
  --form-element-valid-border-color: #388e3c;
  --form-element-valid-active-border-color: #43a047;
  --form-element-valid-focus-color: rgba(67, 160, 71, 0.125);
  --switch-background-color: hsl(205deg, 16%, 77%);
  --switch-color: var(--primary-inverse);
  --switch-checked-background-color: var(--primary);
  --range-border-color: hsl(205deg, 18%, 86%);
  --range-active-border-color: hsl(205deg, 16%, 77%);
  --range-thumb-border-color: var(--background-color);
  --range-thumb-color: var(--secondary);
  --range-thumb-hover-color: var(--secondary-hover);
  --range-thumb-active-color: var(--primary);
  --table-border-color: var(--muted-border-color);
  --table-row-stripped-background-color: #f6f8f9;
  --code-background-color: hsl(205deg, 20%, 94%);
  --code-color: var(--muted-color);
  --code-kbd-background-color: var(--contrast);
  --code-kbd-color: var(--contrast-inverse);
  --code-tag-color: hsl(330deg, 40%, 50%);
  --code-property-color: hsl(185deg, 40%, 40%);
  --code-value-color: hsl(40deg, 20%, 50%);
  --code-comment-color: hsl(205deg, 14%, 68%);
  --accordion-border-color: var(--muted-border-color);
  --accordion-close-summary-color: var(--color);
  --accordion-open-summary-color: var(--muted-color);
  --card-background-color: var(--background-color);
  --card-border-color: var(--muted-border-color);
  --card-box-shadow: 0.0145rem 0.029rem 0.174rem rgba(27, 40, 50, 0.01698),
    0.0335rem 0.067rem 0.402rem rgba(27, 40, 50, 0.024),
    0.0625rem 0.125rem 0.75rem rgba(27, 40, 50, 0.03),
    0.1125rem 0.225rem 1.35rem rgba(27, 40, 50, 0.036),
    0.2085rem 0.417rem 2.502rem rgba(27, 40, 50, 0.04302),
    0.5rem 1rem 6rem rgba(27, 40, 50, 0.06),
    0 0 0 0.0625rem rgba(27, 40, 50, 0.015);
  --card-sectionning-background-color: #fbfbfc;
  --dropdown-background-color: #fbfbfc;
  --dropdown-border-color: #e1e6eb;
  --dropdown-box-shadow: var(--card-box-shadow);
  --dropdown-color: var(--color);
  --dropdown-hover-background-color: hsl(205deg, 20%, 94%);
  --modal-overlay-background-color: rgba(213, 220, 226, 0.7);
  --progress-background-color: hsl(205deg, 18%, 86%);
  --progress-color: var(--primary);
  --loading-spinner-opacity: 0.5;
  --tooltip-background-color: var(--contrast);
  --tooltip-color: var(--contrast-inverse);
  --icon-checkbox: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-chevron: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(65, 84, 98)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-chevron-button: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-chevron-button-inverse: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-close: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(115, 130, 140)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='18' y1='6' x2='6' y2='18'%3E%3C/line%3E%3Cline x1='6' y1='6' x2='18' y2='18'%3E%3C/line%3E%3C/svg%3E");
  --icon-date: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(65, 84, 98)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='4' width='18' height='18' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='16' y1='2' x2='16' y2='6'%3E%3C/line%3E%3Cline x1='8' y1='2' x2='8' y2='6'%3E%3C/line%3E%3Cline x1='3' y1='10' x2='21' y2='10'%3E%3C/line%3E%3C/svg%3E");
  --icon-invalid: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(198, 40, 40)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='12' y1='8' x2='12' y2='12'%3E%3C/line%3E%3Cline x1='12' y1='16' x2='12.01' y2='16'%3E%3C/line%3E%3C/svg%3E");
  --icon-minus: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='5' y1='12' x2='19' y2='12'%3E%3C/line%3E%3C/svg%3E");
  --icon-search: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(65, 84, 98)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'%3E%3C/circle%3E%3Cline x1='21' y1='21' x2='16.65' y2='16.65'%3E%3C/line%3E%3C/svg%3E");
  --icon-time: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(65, 84, 98)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cpolyline points='12 6 12 12 16 14'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-valid: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(56, 142, 60)' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-share: url("data:image/svg+xml;charset=utf-8;base64,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");
  --float-ball-more-button-border-color: #f6f6f6;
  --float-ball-more-button-background-color: #ffffff;
  --float-ball-more-button-svg-color: #6c6f73;
  color-scheme: light;
  --service-bg-hover: #f7faff;
  --service-bg: #fafbfb;
}

@media only screen and (prefers-color-scheme: dark) {
  #mount:not([data-theme="light"]) {
    --background-color: #11191f;
    --float-ball-more-button-background-color: #ffffff;
    --background-light-green: #141e26;
    --color: hsl(205deg, 16%, 77%);
    --h1-color: hsl(205deg, 20%, 94%);
    --h2-color: #e1e6eb;
    --h3-color: hsl(205deg, 18%, 86%);
    --h4-color: #c8d1d8;
    --h5-color: hsl(205deg, 16%, 77%);
    --h6-color: #afbbc4;
    --muted-color: hsl(205deg, 10%, 50%);
    --muted-border-color: #1f2d38;
    --primary: hsl(195deg, 85%, 41%);
    --primary-hover: hsl(195deg, 80%, 50%);
    --primary-focus: rgba(16, 149, 193, 0.25);
    --primary-inverse: #fff;
    --secondary: hsl(205deg, 15%, 41%);
    --secondary-hover: hsl(205deg, 10%, 50%);
    --secondary-focus: rgba(115, 130, 140, 0.25);
    --secondary-inverse: #fff;
    --contrast: hsl(205deg, 20%, 94%);
    --contrast-hover: #fff;
    --contrast-focus: rgba(115, 130, 140, 0.25);
    --contrast-inverse: #000;
    --mark-background-color: #d1c284;
    --mark-color: #11191f;
    --ins-color: #388e3c;
    --del-color: #c62828;
    --blockquote-border-color: var(--muted-border-color);
    --blockquote-footer-color: var(--muted-color);
    --button-box-shadow: 0 0 0 rgba(0, 0, 0, 0);
    --button-hover-box-shadow: 0 0 0 rgba(0, 0, 0, 0);
    --form-element-background-color: #11191f;
    --form-element-border-color: #374956;
    --form-element-color: var(--color);
    --form-element-placeholder-color: var(--muted-color);
    --form-element-active-background-color: var(
      --form-element-background-color
    );
    --form-element-active-border-color: var(--primary);
    --form-element-focus-color: var(--primary-focus);
    --form-element-disabled-background-color: hsl(205deg, 25%, 23%);
    --form-element-disabled-border-color: hsl(205deg, 20%, 32%);
    --form-element-disabled-opacity: 0.5;
    --form-element-invalid-border-color: #b71c1c;
    --form-element-invalid-active-border-color: #c62828;
    --form-element-invalid-focus-color: rgba(198, 40, 40, 0.25);
    --form-element-valid-border-color: #2e7d32;
    --form-element-valid-active-border-color: #388e3c;
    --form-element-valid-focus-color: rgba(56, 142, 60, 0.25);
    --switch-background-color: #374956;
    --switch-color: var(--primary-inverse);
    --switch-checked-background-color: var(--primary);
    --range-border-color: #24333e;
    --range-active-border-color: hsl(205deg, 25%, 23%);
    --range-thumb-border-color: var(--background-color);
    --range-thumb-color: var(--secondary);
    --range-thumb-hover-color: var(--secondary-hover);
    --range-thumb-active-color: var(--primary);
    --table-border-color: var(--muted-border-color);
    --table-row-stripped-background-color: rgba(115, 130, 140, 0.05);
    --code-background-color: #18232c;
    --code-color: var(--muted-color);
    --code-kbd-background-color: var(--contrast);
    --code-kbd-color: var(--contrast-inverse);
    --code-tag-color: hsl(330deg, 30%, 50%);
    --code-property-color: hsl(185deg, 30%, 50%);
    --code-value-color: hsl(40deg, 10%, 50%);
    --code-comment-color: #4d606d;
    --accordion-border-color: var(--muted-border-color);
    --accordion-active-summary-color: var(--primary);
    --accordion-close-summary-color: var(--color);
    --accordion-open-summary-color: var(--muted-color);
    --card-background-color: #141e26;
    --card-border-color: var(--card-background-color);
    --card-box-shadow: 0.0145rem 0.029rem 0.174rem rgba(0, 0, 0, 0.01698),
      0.0335rem 0.067rem 0.402rem rgba(0, 0, 0, 0.024),
      0.0625rem 0.125rem 0.75rem rgba(0, 0, 0, 0.03),
      0.1125rem 0.225rem 1.35rem rgba(0, 0, 0, 0.036),
      0.2085rem 0.417rem 2.502rem rgba(0, 0, 0, 0.04302),
      0.5rem 1rem 6rem rgba(0, 0, 0, 0.06), 0 0 0 0.0625rem rgba(0, 0, 0, 0.015);
    --card-sectionning-background-color: #18232c;
    --dropdown-background-color: hsl(205deg, 30%, 15%);
    --dropdown-border-color: #24333e;
    --dropdown-box-shadow: var(--card-box-shadow);
    --dropdown-color: var(--color);
    --dropdown-hover-background-color: rgba(36, 51, 62, 0.75);
    --modal-overlay-background-color: rgba(36, 51, 62, 0.8);
    --progress-background-color: #24333e;
    --progress-color: var(--primary);
    --loading-spinner-opacity: 0.5;
    --tooltip-background-color: var(--contrast);
    --tooltip-color: var(--contrast-inverse);
    --icon-checkbox: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
    --icon-chevron: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    --icon-chevron-button: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    --icon-chevron-button-inverse: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(0, 0, 0)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    --icon-close: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(115, 130, 140)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='18' y1='6' x2='6' y2='18'%3E%3C/line%3E%3Cline x1='6' y1='6' x2='18' y2='18'%3E%3C/line%3E%3C/svg%3E");
    --icon-date: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='4' width='18' height='18' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='16' y1='2' x2='16' y2='6'%3E%3C/line%3E%3Cline x1='8' y1='2' x2='8' y2='6'%3E%3C/line%3E%3Cline x1='3' y1='10' x2='21' y2='10'%3E%3C/line%3E%3C/svg%3E");
    --icon-invalid: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(183, 28, 28)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='12' y1='8' x2='12' y2='12'%3E%3C/line%3E%3Cline x1='12' y1='16' x2='12.01' y2='16'%3E%3C/line%3E%3C/svg%3E");
    --icon-minus: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='5' y1='12' x2='19' y2='12'%3E%3C/line%3E%3C/svg%3E");
    --icon-search: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'%3E%3C/circle%3E%3Cline x1='21' y1='21' x2='16.65' y2='16.65'%3E%3C/line%3E%3C/svg%3E");
    --icon-time: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cpolyline points='12 6 12 12 16 14'%3E%3C/polyline%3E%3C/svg%3E");
    --icon-valid: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(46, 125, 50)' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
    --icon-share: url("data:image/svg+xml;charset=utf-8;base64,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");
    color-scheme: dark;
    --service-bg-hover: #22292f;
    --service-bg: rgba(0, 0, 0, 0.1);
  }
}
[data-theme="dark"] {
  --background-color: #11191f;
  --float-ball-more-button-background-color: #ffffff;
  --background-light-green: #141e26;
  --color: hsl(205deg, 16%, 77%);
  --h1-color: hsl(205deg, 20%, 94%);
  --h2-color: #e1e6eb;
  --h3-color: hsl(205deg, 18%, 86%);
  --h4-color: #c8d1d8;
  --h5-color: hsl(205deg, 16%, 77%);
  --h6-color: #afbbc4;
  --muted-color: hsl(205deg, 10%, 50%);
  --muted-border-color: #1f2d38;
  --primary: hsl(195deg, 85%, 41%);
  --primary-hover: hsl(195deg, 80%, 50%);
  --primary-focus: rgba(16, 149, 193, 0.25);
  --primary-inverse: #fff;
  --secondary: hsl(205deg, 15%, 41%);
  --secondary-hover: hsl(205deg, 10%, 50%);
  --secondary-focus: rgba(115, 130, 140, 0.25);
  --secondary-inverse: #fff;
  --contrast: hsl(205deg, 20%, 94%);
  --contrast-hover: #fff;
  --contrast-focus: rgba(115, 130, 140, 0.25);
  --contrast-inverse: #000;
  --mark-background-color: #d1c284;
  --mark-color: #11191f;
  --ins-color: #388e3c;
  --del-color: #c62828;
  --blockquote-border-color: var(--muted-border-color);
  --blockquote-footer-color: var(--muted-color);
  --button-box-shadow: 0 0 0 rgba(0, 0, 0, 0);
  --button-hover-box-shadow: 0 0 0 rgba(0, 0, 0, 0);
  --form-element-background-color: #11191f;
  --form-element-border-color: #374956;
  --form-element-color: var(--color);
  --form-element-placeholder-color: var(--muted-color);
  --form-element-active-background-color: var(--form-element-background-color);
  --form-element-active-border-color: var(--primary);
  --form-element-focus-color: var(--primary-focus);
  --form-element-disabled-background-color: hsl(205deg, 25%, 23%);
  --form-element-disabled-border-color: hsl(205deg, 20%, 32%);
  --form-element-disabled-opacity: 0.5;
  --form-element-invalid-border-color: #b71c1c;
  --form-element-invalid-active-border-color: #c62828;
  --form-element-invalid-focus-color: rgba(198, 40, 40, 0.25);
  --form-element-valid-border-color: #2e7d32;
  --form-element-valid-active-border-color: #388e3c;
  --form-element-valid-focus-color: rgba(56, 142, 60, 0.25);
  --switch-background-color: #374956;
  --switch-color: var(--primary-inverse);
  --switch-checked-background-color: var(--primary);
  --range-border-color: #24333e;
  --range-active-border-color: hsl(205deg, 25%, 23%);
  --range-thumb-border-color: var(--background-color);
  --range-thumb-color: var(--secondary);
  --range-thumb-hover-color: var(--secondary-hover);
  --range-thumb-active-color: var(--primary);
  --table-border-color: var(--muted-border-color);
  --table-row-stripped-background-color: rgba(115, 130, 140, 0.05);
  --code-background-color: #18232c;
  --code-color: var(--muted-color);
  --code-kbd-background-color: var(--contrast);
  --code-kbd-color: var(--contrast-inverse);
  --code-tag-color: hsl(330deg, 30%, 50%);
  --code-property-color: hsl(185deg, 30%, 50%);
  --code-value-color: hsl(40deg, 10%, 50%);
  --code-comment-color: #4d606d;
  --accordion-border-color: var(--muted-border-color);
  --accordion-active-summary-color: var(--primary);
  --accordion-close-summary-color: var(--color);
  --accordion-open-summary-color: var(--muted-color);
  --card-background-color: #141e26;
  --card-border-color: var(--card-background-color);
  --card-box-shadow: 0.0145rem 0.029rem 0.174rem rgba(0, 0, 0, 0.01698),
    0.0335rem 0.067rem 0.402rem rgba(0, 0, 0, 0.024),
    0.0625rem 0.125rem 0.75rem rgba(0, 0, 0, 0.03),
    0.1125rem 0.225rem 1.35rem rgba(0, 0, 0, 0.036),
    0.2085rem 0.417rem 2.502rem rgba(0, 0, 0, 0.04302),
    0.5rem 1rem 6rem rgba(0, 0, 0, 0.06), 0 0 0 0.0625rem rgba(0, 0, 0, 0.015);
  --card-sectionning-background-color: #18232c;
  --dropdown-background-color: hsl(205deg, 30%, 15%);
  --dropdown-border-color: #24333e;
  --dropdown-box-shadow: var(--card-box-shadow);
  --dropdown-color: var(--color);
  --dropdown-hover-background-color: rgba(36, 51, 62, 0.75);
  --modal-overlay-background-color: rgba(36, 51, 62, 0.8);
  --progress-background-color: #24333e;
  --progress-color: var(--primary);
  --loading-spinner-opacity: 0.5;
  --tooltip-background-color: var(--contrast);
  --tooltip-color: var(--contrast-inverse);
  --icon-checkbox: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-chevron: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-chevron-button: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-chevron-button-inverse: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(0, 0, 0)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-close: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(115, 130, 140)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='18' y1='6' x2='6' y2='18'%3E%3C/line%3E%3Cline x1='6' y1='6' x2='18' y2='18'%3E%3C/line%3E%3C/svg%3E");
  --icon-date: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='4' width='18' height='18' rx='2' ry='2'%3E%3C/rect%3E%3Cline x1='16' y1='2' x2='16' y2='6'%3E%3C/line%3E%3Cline x1='8' y1='2' x2='8' y2='6'%3E%3C/line%3E%3Cline x1='3' y1='10' x2='21' y2='10'%3E%3C/line%3E%3C/svg%3E");
  --icon-invalid: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(183, 28, 28)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='12' y1='8' x2='12' y2='12'%3E%3C/line%3E%3Cline x1='12' y1='16' x2='12.01' y2='16'%3E%3C/line%3E%3C/svg%3E");
  --icon-minus: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(255, 255, 255)' stroke-width='4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='5' y1='12' x2='19' y2='12'%3E%3C/line%3E%3C/svg%3E");
  --icon-search: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'%3E%3C/circle%3E%3Cline x1='21' y1='21' x2='16.65' y2='16.65'%3E%3C/line%3E%3C/svg%3E");
  --icon-time: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(162, 175, 185)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cpolyline points='12 6 12 12 16 14'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-valid: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgb(46, 125, 50)' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
  --icon-share: url("data:image/svg+xml;charset=utf-8;base64,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");
  color-scheme: dark;
  --service-bg: rgba(0, 0, 0, 0.1);
}

progress,
[type="checkbox"],
[type="radio"],
[type="range"] {
  accent-color: var(--primary);
}

/**
 * Document
 * Content-box & Responsive typography
 */
*,
*::before,
*::after {
  box-sizing: border-box;
  background-repeat: no-repeat;
}

::before,
::after {
  text-decoration: inherit;
  vertical-align: inherit;
}

:where(#mount) {
  -webkit-tap-highlight-color: transparent;
  -webkit-text-size-adjust: 100%;
  -moz-text-size-adjust: 100%;
  text-size-adjust: 100%;
  background-color: var(--background-color);
  color: var(--color);
  font-weight: var(--font-weight);
  font-size: var(--font-size);
  line-height: var(--line-height);
  font-family: var(--font-family);
  text-rendering: optimizeLegibility;
  overflow-wrap: break-word;
  cursor: default;
  -moz-tab-size: 4;
  -o-tab-size: 4;
  tab-size: 4;
}

/**
 * Sectioning
 * Container and responsive spacings for header, main, footer
 */
main {
  display: block;
}

#mount {
  width: 100%;
  margin: 0;
}
#mount > header,
#mount > main,
#mount > footer {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding: var(--block-spacing-vertical) var(--block-spacing-horizontal);
}
@media (min-width: 576px) {
  #mount > header,
  #mount > main,
  #mount > footer {
    padding: 2px !important;
  }
}
@media (min-width: 992px) {
  #mount > header,
  #mount > main,
  #mount > footer {
    padding: 0 12px !important;
  }
}
@media (min-width: 1200px) {
  #mount > header,
  #mount > main,
  #mount > footer {
    padding: 0 24px !important;
  }
}

/**
* Container
*/
.container,
.container-fluid {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: var(--spacing);
  padding-left: var(--spacing);
}
/* 
@media (min-width: 576px) {
  .container {
    max-width: 510px;
    padding-right: 0;
    padding-left: 0;
  }
}
@media (min-width: 768px) {
  .container {
    max-width: 700px;
  }
} */
@media (min-width: 992px) {
  .container {
    max-width: 920px;
  }
}
@media (min-width: 1200px) {
  .container {
    max-width: 1130px;
  }
}

/**
 * Section
 * Responsive spacings for section
 */
section {
  margin-bottom: var(--block-spacing-vertical);
}

/**
* Grid
* Minimal grid system with auto-layout columns
*/
.grid {
  grid-column-gap: var(--grid-spacing-horizontal);
  grid-row-gap: var(--grid-spacing-vertical);
  display: grid;
  grid-template-columns: 1fr;
  margin: 0;
}
@media (min-width: 1280px) {
  .grid {
    grid-template-columns: repeat(auto-fit, minmax(0%, 1fr));
  }
}
.grid > * {
  min-width: 0;
}

/**
 * Horizontal scroller (<figure>)
 */
figure {
  display: block;
  margin: 0;
  padding: 0;
  overflow-x: auto;
}
figure figcaption {
  padding: calc(var(--spacing) * 0.5) 0;
  color: var(--muted-color);
}

/**
 * Typography
 */
b,
strong {
  font-weight: bolder;
}

sub,
sup {
  position: relative;
  font-size: 0.75em;
  line-height: 0;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

address,
blockquote,
dl,
figure,
form,
ol,
p,
pre,
table,
ul {
  margin-top: 0;
  margin-bottom: var(--typography-spacing-vertical);
  color: var(--color);
  font-style: normal;
  font-weight: var(--font-weight);
  font-size: var(--font-size);
}

a,
[role="link"] {
  --color: var(--primary);
  --background-color: transparent;
  outline: none;
  background-color: var(--background-color);
  color: var(--color);
  -webkit-text-decoration: var(--text-decoration);
  text-decoration: var(--text-decoration);
  transition: background-color var(--transition), color var(--transition),
    box-shadow var(--transition), -webkit-text-decoration var(--transition);
  transition: background-color var(--transition), color var(--transition),
    text-decoration var(--transition), box-shadow var(--transition);
  transition: background-color var(--transition), color var(--transition),
    text-decoration var(--transition), box-shadow var(--transition),
    -webkit-text-decoration var(--transition);
}
a:is([aria-current], :hover, :active, :focus),
[role="link"]:is([aria-current], :hover, :active, :focus) {
  --color: var(--primary-hover);
  --text-decoration: underline;
}
a:focus,
[role="link"]:focus {
  --background-color: var(--primary-focus);
}
a.secondary,
[role="link"].secondary {
  --color: var(--secondary);
}
a.secondary:is([aria-current], :hover, :active, :focus),
[role="link"].secondary:is([aria-current], :hover, :active, :focus) {
  --color: var(--secondary-hover);
}
a.secondary:focus,
[role="link"].secondary:focus {
  --background-color: var(--secondary-focus);
}
a.contrast,
[role="link"].contrast {
  --color: var(--contrast);
}
a.contrast:is([aria-current], :hover, :active, :focus),
[role="link"].contrast:is([aria-current], :hover, :active, :focus) {
  --color: var(--contrast-hover);
}
a.contrast:focus,
[role="link"].contrast:focus {
  --background-color: var(--contrast-focus);
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin-top: 0;
  margin-bottom: var(--typography-spacing-vertical);
  color: var(--color);
  font-weight: var(--font-weight);
  font-size: var(--font-size);
  font-family: var(--font-family);
}

h1 {
  --color: var(--h1-color);
}

h2 {
  --color: var(--h2-color);
}

h3 {
  --color: var(--h3-color);
}

h4 {
  --color: var(--h4-color);
}

h5 {
  --color: var(--h5-color);
}

h6 {
  --color: var(--h6-color);
}

:where(address, blockquote, dl, figure, form, ol, p, pre, table, ul)
  ~ :is(h1, h2, h3, h4, h5, h6) {
  margin-top: var(--typography-spacing-vertical);
}

hgroup,
.headings {
  margin-bottom: var(--typography-spacing-vertical);
}
hgroup > *,
.headings > * {
  margin-bottom: 0;
}
hgroup > *:last-child,
.headings > *:last-child {
  --color: var(--muted-color);
  --font-weight: unset;
  font-size: 1rem;
  font-family: unset;
}

p {
  margin-bottom: var(--typography-spacing-vertical);
}

small {
  font-size: var(--font-size);
}

:where(dl, ol, ul) {
  padding-right: 0;
  padding-left: var(--spacing);
  -webkit-padding-start: var(--spacing);
  padding-inline-start: var(--spacing);
  -webkit-padding-end: 0;
  padding-inline-end: 0;
}
:where(dl, ol, ul) li {
  margin-bottom: calc(var(--typography-spacing-vertical) * 0.25);
}

:where(dl, ol, ul) :is(dl, ol, ul) {
  margin: 0;
  margin-top: calc(var(--typography-spacing-vertical) * 0.25);
}

ul li {
  list-style: square;
}

mark {
  padding: 0.125rem 0.25rem;
  background-color: var(--mark-background-color);
  color: var(--mark-color);
  vertical-align: baseline;
}

blockquote {
  display: block;
  margin: var(--typography-spacing-vertical) 0;
  padding: var(--spacing);
  border-right: none;
  border-left: 0.25rem solid var(--blockquote-border-color);
  -webkit-border-start: 0.25rem solid var(--blockquote-border-color);
  border-inline-start: 0.25rem solid var(--blockquote-border-color);
  -webkit-border-end: none;
  border-inline-end: none;
}
blockquote footer {
  margin-top: calc(var(--typography-spacing-vertical) * 0.5);
  color: var(--blockquote-footer-color);
}

abbr[title] {
  border-bottom: 1px dotted;
  text-decoration: none;
  cursor: help;
}

ins {
  color: var(--ins-color);
  text-decoration: none;
}

del {
  color: var(--del-color);
}

::-moz-selection {
  background-color: var(--primary-focus);
}

::selection {
  background-color: var(--primary-focus);
}

/**
 * Embedded content
 */
:where(audio, canvas, iframe, img, svg, video) {
  vertical-align: middle;
}

audio,
video {
  display: inline-block;
}

audio:not([controls]) {
  display: none;
  height: 0;
}

:where(iframe) {
  border-style: none;
}

img {
  max-width: 100%;
  height: auto;
  border-style: none;
}

:where(svg:not([fill])) {
  fill: currentColor;
}

svg:not(#mount) {
  overflow: hidden;
}

/**
 * Button
 */
button {
  margin: 0;
  overflow: visible;
  font-family: inherit;
  text-transform: none;
}

button,
[type="button"],
[type="reset"],
[type="submit"] {
  -webkit-appearance: button;
}

button {
  display: block;
  width: 100%;
  margin-bottom: var(--spacing);
}

[role="button"] {
  display: inline-block;
  text-decoration: none;
}

button,
input[type="submit"],
input[type="button"],
input[type="reset"],
[role="button"] {
  --background-color: var(--primary);
  --border-color: var(--primary);
  --color: var(--primary-inverse);
  --box-shadow: var(--button-box-shadow, 0 0 0 rgba(0, 0, 0, 0));
  padding: var(--form-element-spacing-vertical)
    var(--form-element-spacing-horizontal);
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
  outline: none;
  background-color: var(--background-color);
  box-shadow: var(--box-shadow);
  color: var(--color);
  font-weight: var(--font-weight);
  font-size: 1rem;
  line-height: var(--line-height);
  text-align: center;
  cursor: pointer;
  transition: background-color var(--transition), border-color var(--transition),
    color var(--transition), box-shadow var(--transition);
}
button:is([aria-current], :hover, :active, :focus),
input[type="submit"]:is([aria-current], :hover, :active, :focus),
input[type="button"]:is([aria-current], :hover, :active, :focus),
input[type="reset"]:is([aria-current], :hover, :active, :focus),
[role="button"]:is([aria-current], :hover, :active, :focus) {
  --background-color: var(--primary-hover);
  --border-color: var(--primary-hover);
  --box-shadow: var(--button-hover-box-shadow, 0 0 0 rgba(0, 0, 0, 0));
  --color: var(--primary-inverse);
}
button:focus,
input[type="submit"]:focus,
input[type="button"]:focus,
input[type="reset"]:focus,
[role="button"]:focus {
  --box-shadow: var(--button-hover-box-shadow, 0 0 0 rgba(0, 0, 0, 0)),
    0 0 0 var(--outline-width) var(--primary-focus);
}

:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).secondary,
input[type="reset"] {
  --background-color: var(--secondary);
  --border-color: var(--secondary);
  --color: var(--secondary-inverse);
  cursor: pointer;
}
:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).secondary:is([aria-current], :hover, :active, :focus),
input[type="reset"]:is([aria-current], :hover, :active, :focus) {
  --background-color: var(--secondary-hover);
  --border-color: var(--secondary-hover);
  --color: var(--secondary-inverse);
}
:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).secondary:focus,
input[type="reset"]:focus {
  --box-shadow: var(--button-hover-box-shadow, 0 0 0 rgba(0, 0, 0, 0)),
    0 0 0 var(--outline-width) var(--secondary-focus);
}

:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).contrast {
  --background-color: var(--contrast);
  --border-color: var(--contrast);
  --color: var(--contrast-inverse);
}
:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).contrast:is([aria-current], :hover, :active, :focus) {
  --background-color: var(--contrast-hover);
  --border-color: var(--contrast-hover);
  --color: var(--contrast-inverse);
}
:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).contrast:focus {
  --box-shadow: var(--button-hover-box-shadow, 0 0 0 rgba(0, 0, 0, 0)),
    0 0 0 var(--outline-width) var(--contrast-focus);
}

:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).outline,
input[type="reset"].outline {
  --background-color: transparent;
  --color: var(--primary);
}
:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).outline:is([aria-current], :hover, :active, :focus),
input[type="reset"].outline:is([aria-current], :hover, :active, :focus) {
  --background-color: transparent;
  --color: var(--primary-hover);
}

:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).outline.secondary,
input[type="reset"].outline {
  --color: var(--secondary);
}
:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).outline.secondary:is([aria-current], :hover, :active, :focus),
input[type="reset"].outline:is([aria-current], :hover, :active, :focus) {
  --color: var(--secondary-hover);
}

:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).outline.contrast {
  --color: var(--contrast);
}
:is(
    button,
    input[type="submit"],
    input[type="button"],
    [role="button"]
  ).outline.contrast:is([aria-current], :hover, :active, :focus) {
  --color: var(--contrast-hover);
}

:where(
    button,
    [type="submit"],
    [type="button"],
    [type="reset"],
    [role="button"]
  )[disabled],
:where(fieldset[disabled])
  :is(
    button,
    [type="submit"],
    [type="button"],
    [type="reset"],
    [role="button"]
  ),
a[role="button"]:not([href]) {
  opacity: 0.5;
  pointer-events: none;
}

/**
 * Form elements
 */
input,
optgroup,
select,
textarea {
  margin: 0;
  font-size: 1rem;
  line-height: var(--line-height);
  font-family: inherit;
  letter-spacing: inherit;
}

input {
  overflow: visible;
}

select {
  text-transform: none;
}

legend {
  max-width: 100%;
  padding: 0;
  color: inherit;
  white-space: normal;
}

textarea {
  overflow: auto;
}

[type="checkbox"],
[type="radio"] {
  padding: 0;
}

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

[type="search"] {
  -webkit-appearance: textfield;
  outline-offset: -2px;
}

[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}

::-webkit-file-upload-button {
  -webkit-appearance: button;
  font: inherit;
}

::-moz-focus-inner {
  padding: 0;
  border-style: none;
}

:-moz-focusring {
  outline: none;
}

:-moz-ui-invalid {
  box-shadow: none;
}

::-ms-expand {
  display: none;
}

[type="file"],
[type="range"] {
  padding: 0;
  border-width: 0;
}

input:not([type="checkbox"], [type="radio"], [type="range"]) {
  height: calc(
    1rem * var(--line-height) + var(--form-element-spacing-vertical) * 2 +
      var(--border-width) * 2
  );
}

fieldset {
  margin: 0;
  margin-bottom: var(--spacing);
  padding: 0;
  border: 0;
}

label,
fieldset legend {
  display: block;
  margin-bottom: calc(var(--spacing) * 0.25);
  font-weight: var(--form-label-font-weight, var(--font-weight));
}

input:not([type="checkbox"], [type="radio"]),
select,
textarea {
  width: 100%;
}

input:not([type="checkbox"], [type="radio"], [type="range"], [type="file"]),
select,
textarea {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  padding: var(--form-element-spacing-vertical)
    var(--form-element-spacing-horizontal);
}

input,
select,
textarea {
  --background-color: var(--form-element-background-color);
  --border-color: var(--form-element-border-color);
  --color: var(--form-element-color);
  --box-shadow: none;
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
  outline: none;
  background-color: var(--background-color);
  box-shadow: var(--box-shadow);
  color: var(--color);
  font-weight: var(--font-weight);
  transition: background-color var(--transition), border-color var(--transition),
    color var(--transition), box-shadow var(--transition);
}

input:not(
    [type="submit"],
    [type="button"],
    [type="reset"],
    [type="checkbox"],
    [type="radio"],
    [readonly]
  ):is(:active, :focus),
:where(select, textarea):is(:active, :focus) {
  --background-color: var(--form-element-active-background-color);
}

input:not(
    [type="submit"],
    [type="button"],
    [type="reset"],
    [role="switch"],
    [readonly]
  ):is(:active, :focus),
:where(select, textarea):is(:active, :focus) {
  --border-color: var(--form-element-active-border-color);
}

input:not(
    [type="submit"],
    [type="button"],
    [type="reset"],
    [type="range"],
    [type="file"],
    [readonly]
  ):focus,
select:focus,
textarea:focus {
  --box-shadow: 0 0 0 var(--outline-width) var(--form-element-focus-color);
}

input:not([type="submit"], [type="button"], [type="reset"])[disabled],
select[disabled],
textarea[disabled],
:where(fieldset[disabled])
  :is(
    input:not([type="submit"], [type="button"], [type="reset"]),
    select,
    textarea
  ) {
  --background-color: var(--form-element-disabled-background-color);
  --border-color: var(--form-element-disabled-border-color);
  opacity: var(--form-element-disabled-opacity);
  pointer-events: none;
}

:where(input, select, textarea):not(
    [type="checkbox"],
    [type="radio"],
    [type="date"],
    [type="datetime-local"],
    [type="month"],
    [type="time"],
    [type="week"]
  )[aria-invalid] {
  padding-right: calc(
    var(--form-element-spacing-horizontal) + 1.5rem
  ) !important;
  padding-left: var(--form-element-spacing-horizontal);
  -webkit-padding-start: var(--form-element-spacing-horizontal) !important;
  padding-inline-start: var(--form-element-spacing-horizontal) !important;
  -webkit-padding-end: calc(
    var(--form-element-spacing-horizontal) + 1.5rem
  ) !important;
  padding-inline-end: calc(
    var(--form-element-spacing-horizontal) + 1.5rem
  ) !important;
  background-position: center right 0.75rem;
  background-size: 1rem auto;
  background-repeat: no-repeat;
}
:where(input, select, textarea):not(
    [type="checkbox"],
    [type="radio"],
    [type="date"],
    [type="datetime-local"],
    [type="month"],
    [type="time"],
    [type="week"]
  )[aria-invalid="false"] {
  background-image: var(--icon-valid);
}
:where(input, select, textarea):not(
    [type="checkbox"],
    [type="radio"],
    [type="date"],
    [type="datetime-local"],
    [type="month"],
    [type="time"],
    [type="week"]
  )[aria-invalid="true"] {
  background-image: var(--icon-invalid);
}
:where(input, select, textarea)[aria-invalid="false"] {
  --border-color: var(--form-element-valid-border-color);
}
:where(input, select, textarea)[aria-invalid="false"]:is(:active, :focus) {
  --border-color: var(--form-element-valid-active-border-color) !important;
  --box-shadow: 0 0 0 var(--outline-width) var(--form-element-valid-focus-color) !important;
}
:where(input, select, textarea)[aria-invalid="true"] {
  --border-color: var(--form-element-invalid-border-color);
}
:where(input, select, textarea)[aria-invalid="true"]:is(:active, :focus) {
  --border-color: var(--form-element-invalid-active-border-color) !important;
  --box-shadow: 0 0 0 var(--outline-width)
    var(--form-element-invalid-focus-color) !important;
}

[dir="rtl"]
  :where(input, select, textarea):not([type="checkbox"], [type="radio"]):is(
    [aria-invalid],
    [aria-invalid="true"],
    [aria-invalid="false"]
  ) {
  background-position: center left 0.75rem;
}

input::placeholder,
input::-webkit-input-placeholder,
textarea::placeholder,
textarea::-webkit-input-placeholder,
select:invalid {
  color: var(--form-element-placeholder-color);
  opacity: 1;
}

input:not([type="checkbox"], [type="radio"]),
select,
textarea {
  margin-bottom: var(--spacing);
}

select::-ms-expand {
  border: 0;
  background-color: transparent;
}
select:not([multiple], [size]) {
  padding-right: calc(var(--form-element-spacing-horizontal) + 1.5rem);
  padding-left: var(--form-element-spacing-horizontal);
  -webkit-padding-start: var(--form-element-spacing-horizontal);
  padding-inline-start: var(--form-element-spacing-horizontal);
  -webkit-padding-end: calc(var(--form-element-spacing-horizontal) + 1.5rem);
  padding-inline-end: calc(var(--form-element-spacing-horizontal) + 1.5rem);
  background-image: var(--icon-chevron);
  background-position: center right 0.75rem;
  background-size: 1rem auto;
  background-repeat: no-repeat;
}

[dir="rtl"] select:not([multiple], [size]) {
  background-position: center left 0.75rem;
}

:where(input, select, textarea) + small {
  display: block;
  width: 100%;
  margin-top: calc(var(--spacing) * -0.75);
  margin-bottom: var(--spacing);
  color: var(--muted-color);
}

label > :where(input, select, textarea) {
  margin-top: calc(var(--spacing) * 0.25);
}

/**
 * Form elements
 * Checkboxes & Radios
 */
[type="checkbox"],
[type="radio"] {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  width: 1.25em;
  height: 1.25em;
  margin-top: -0.125em;
  margin-right: 0.375em;
  margin-left: 0;
  -webkit-margin-start: 0;
  margin-inline-start: 0;
  -webkit-margin-end: 0.375em;
  margin-inline-end: 0.375em;
  border-width: var(--border-width);
  font-size: inherit;
  vertical-align: middle;
  cursor: pointer;
}
[type="checkbox"]::-ms-check,
[type="radio"]::-ms-check {
  display: none;
}
[type="checkbox"]:checked,
[type="checkbox"]:checked:active,
[type="checkbox"]:checked:focus,
[type="radio"]:checked,
[type="radio"]:checked:active,
[type="radio"]:checked:focus {
  --background-color: var(--primary);
  --border-color: var(--primary);
  background-image: var(--icon-checkbox);
  background-position: center;
  background-size: 0.75em auto;
  background-repeat: no-repeat;
}
[type="checkbox"] ~ label,
[type="radio"] ~ label {
  display: inline-block;
  margin-right: 0.375em;
  margin-bottom: 0;
  cursor: pointer;
}

[type="checkbox"]:indeterminate {
  --background-color: var(--primary);
  --border-color: var(--primary);
  background-image: var(--icon-minus);
  background-position: center;
  background-size: 0.75em auto;
  background-repeat: no-repeat;
}

[type="radio"] {
  border-radius: 50%;
}
[type="radio"]:checked,
[type="radio"]:checked:active,
[type="radio"]:checked:focus {
  --background-color: var(--primary-inverse);
  border-width: 0.35em;
  background-image: none;
}

[type="checkbox"][role="switch"] {
  --background-color: var(--switch-background-color);
  --border-color: var(--switch-background-color);
  --color: var(--switch-color);
  width: 2.25em;
  height: 1.25em;
  border: var(--border-width) solid var(--border-color);
  border-radius: 1.25em;
  background-color: var(--background-color);
  line-height: 1.25em;
}
[type="checkbox"][role="switch"]:focus {
  --background-color: var(--switch-background-color);
  --border-color: var(--switch-background-color);
}
[type="checkbox"][role="switch"]:checked {
  --background-color: var(--switch-checked-background-color);
  --border-color: var(--switch-checked-background-color);
}
[type="checkbox"][role="switch"]:before {
  display: block;
  width: calc(1.25em - (var(--border-width) * 2));
  height: 100%;
  border-radius: 50%;
  background-color: var(--color);
  content: "";
  transition: margin 0.1s ease-in-out;
}
[type="checkbox"][role="switch"]:checked {
  background-image: none;
}
[type="checkbox"][role="switch"]:checked::before {
  margin-left: calc(1.125em - var(--border-width));
  -webkit-margin-start: calc(1.125em - var(--border-width));
  margin-inline-start: calc(1.125em - var(--border-width));
}

[type="checkbox"][aria-invalid="false"],
[type="checkbox"]:checked[aria-invalid="false"],
[type="radio"][aria-invalid="false"],
[type="radio"]:checked[aria-invalid="false"],
[type="checkbox"][role="switch"][aria-invalid="false"],
[type="checkbox"][role="switch"]:checked[aria-invalid="false"] {
  --border-color: var(--form-element-valid-border-color);
}
[type="checkbox"][aria-invalid="true"],
[type="checkbox"]:checked[aria-invalid="true"],
[type="radio"][aria-invalid="true"],
[type="radio"]:checked[aria-invalid="true"],
[type="checkbox"][role="switch"][aria-invalid="true"],
[type="checkbox"][role="switch"]:checked[aria-invalid="true"] {
  --border-color: var(--form-element-invalid-border-color);
}

/**
 * Form elements
 * Alternatives input types (Not Checkboxes & Radios)
 */
[type="color"]::-webkit-color-swatch-wrapper {
  padding: 0;
}
[type="color"]::-moz-focus-inner {
  padding: 0;
}
[type="color"]::-webkit-color-swatch {
  border: 0;
  border-radius: calc(var(--border-radius) * 0.5);
}
[type="color"]::-moz-color-swatch {
  border: 0;
  border-radius: calc(var(--border-radius) * 0.5);
}

input:not([type="checkbox"], [type="radio"], [type="range"], [type="file"]):is(
    [type="date"],
    [type="datetime-local"],
    [type="month"],
    [type="time"],
    [type="week"]
  ) {
  --icon-position: 0.75rem;
  --icon-width: 1rem;
  padding-right: calc(var(--icon-width) + var(--icon-position));
  background-image: var(--icon-date);
  background-position: center right var(--icon-position);
  background-size: var(--icon-width) auto;
  background-repeat: no-repeat;
}
input:not(
    [type="checkbox"],
    [type="radio"],
    [type="range"],
    [type="file"]
  )[type="time"] {
  background-image: var(--icon-time);
}

[type="date"]::-webkit-calendar-picker-indicator,
[type="datetime-local"]::-webkit-calendar-picker-indicator,
[type="month"]::-webkit-calendar-picker-indicator,
[type="time"]::-webkit-calendar-picker-indicator,
[type="week"]::-webkit-calendar-picker-indicator {
  width: var(--icon-width);
  margin-right: calc(var(--icon-width) * -1);
  margin-left: var(--icon-position);
  opacity: 0;
}

[dir="rtl"]
  :is(
    [type="date"],
    [type="datetime-local"],
    [type="month"],
    [type="time"],
    [type="week"]
  ) {
  text-align: right;
}

[type="file"] {
  --color: var(--muted-color);
  padding: calc(var(--form-element-spacing-vertical) * 0.5) 0;
  border: 0;
  border-radius: 0;
  background: none;
}
[type="file"]::file-selector-button {
  --background-color: var(--secondary);
  --border-color: var(--secondary);
  --color: var(--secondary-inverse);
  margin-right: calc(var(--spacing) / 2);
  margin-left: 0;
  -webkit-margin-start: 0;
  margin-inline-start: 0;
  -webkit-margin-end: calc(var(--spacing) / 2);
  margin-inline-end: calc(var(--spacing) / 2);
  padding: calc(var(--form-element-spacing-vertical) * 0.5)
    calc(var(--form-element-spacing-horizontal) * 0.5);
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
  outline: none;
  background-color: var(--background-color);
  box-shadow: var(--box-shadow);
  color: var(--color);
  font-weight: var(--font-weight);
  font-size: 1rem;
  line-height: var(--line-height);
  text-align: center;
  cursor: pointer;
  transition: background-color var(--transition), border-color var(--transition),
    color var(--transition), box-shadow var(--transition);
}
[type="file"]::file-selector-button:is(:hover, :active, :focus) {
  --background-color: var(--secondary-hover);
  --border-color: var(--secondary-hover);
}
[type="file"]::-webkit-file-upload-button {
  --background-color: var(--secondary);
  --border-color: var(--secondary);
  --color: var(--secondary-inverse);
  margin-right: calc(var(--spacing) / 2);
  margin-left: 0;
  -webkit-margin-start: 0;
  margin-inline-start: 0;
  -webkit-margin-end: calc(var(--spacing) / 2);
  margin-inline-end: calc(var(--spacing) / 2);
  padding: calc(var(--form-element-spacing-vertical) * 0.5)
    calc(var(--form-element-spacing-horizontal) * 0.5);
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
  outline: none;
  background-color: var(--background-color);
  box-shadow: var(--box-shadow);
  color: var(--color);
  font-weight: var(--font-weight);
  font-size: 1rem;
  line-height: var(--line-height);
  text-align: center;
  cursor: pointer;
  -webkit-transition: background-color var(--transition),
    border-color var(--transition), color var(--transition),
    box-shadow var(--transition);
  transition: background-color var(--transition), border-color var(--transition),
    color var(--transition), box-shadow var(--transition);
}
[type="file"]::-webkit-file-upload-button:is(:hover, :active, :focus) {
  --background-color: var(--secondary-hover);
  --border-color: var(--secondary-hover);
}
[type="file"]::-ms-browse {
  --background-color: var(--secondary);
  --border-color: var(--secondary);
  --color: var(--secondary-inverse);
  margin-right: calc(var(--spacing) / 2);
  margin-left: 0;
  margin-inline-start: 0;
  margin-inline-end: calc(var(--spacing) / 2);
  padding: calc(var(--form-element-spacing-vertical) * 0.5)
    calc(var(--form-element-spacing-horizontal) * 0.5);
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
  outline: none;
  background-color: var(--background-color);
  box-shadow: var(--box-shadow);
  color: var(--color);
  font-weight: var(--font-weight);
  font-size: 1rem;
  line-height: var(--line-height);
  text-align: center;
  cursor: pointer;
  -ms-transition: background-color var(--transition),
    border-color var(--transition), color var(--transition),
    box-shadow var(--transition);
  transition: background-color var(--transition), border-color var(--transition),
    color var(--transition), box-shadow var(--transition);
}
[type="file"]::-ms-browse:is(:hover, :active, :focus) {
  --background-color: var(--secondary-hover);
  --border-color: var(--secondary-hover);
}

[type="range"] {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  width: 100%;
  height: 1.25rem;
  background: none;
}
[type="range"]::-webkit-slider-runnable-track {
  width: 100%;
  height: 0.25rem;
  border-radius: var(--border-radius);
  background-color: var(--range-border-color);
  -webkit-transition: background-color var(--transition),
    box-shadow var(--transition);
  transition: background-color var(--transition), box-shadow var(--transition);
}
[type="range"]::-moz-range-track {
  width: 100%;
  height: 0.25rem;
  border-radius: var(--border-radius);
  background-color: var(--range-border-color);
  -moz-transition: background-color var(--transition),
    box-shadow var(--transition);
  transition: background-color var(--transition), box-shadow var(--transition);
}
[type="range"]::-ms-track {
  width: 100%;
  height: 0.25rem;
  border-radius: var(--border-radius);
  background-color: var(--range-border-color);
  -ms-transition: background-color var(--transition),
    box-shadow var(--transition);
  transition: background-color var(--transition), box-shadow var(--transition);
}
[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 1.25rem;
  height: 1.25rem;
  margin-top: -0.5rem;
  border: 2px solid var(--range-thumb-border-color);
  border-radius: 50%;
  background-color: var(--range-thumb-color);
  cursor: pointer;
  -webkit-transition: background-color var(--transition),
    transform var(--transition);
  transition: background-color var(--transition), transform var(--transition);
}
[type="range"]::-moz-range-thumb {
  -webkit-appearance: none;
  width: 1.25rem;
  height: 1.25rem;
  margin-top: -0.5rem;
  border: 2px solid var(--range-thumb-border-color);
  border-radius: 50%;
  background-color: var(--range-thumb-color);
  cursor: pointer;
  -moz-transition: background-color var(--transition),
    transform var(--transition);
  transition: background-color var(--transition), transform var(--transition);
}
[type="range"]::-ms-thumb {
  -webkit-appearance: none;
  width: 1.25rem;
  height: 1.25rem;
  margin-top: -0.5rem;
  border: 2px solid var(--range-thumb-border-color);
  border-radius: 50%;
  background-color: var(--range-thumb-color);
  cursor: pointer;
  -ms-transition: background-color var(--transition),
    transform var(--transition);
  transition: background-color var(--transition), transform var(--transition);
}
[type="range"]:hover,
[type="range"]:focus {
  --range-border-color: var(--range-active-border-color);
  --range-thumb-color: var(--range-thumb-hover-color);
}
[type="range"]:active {
  --range-thumb-color: var(--range-thumb-active-color);
}
[type="range"]:active::-webkit-slider-thumb {
  transform: scale(1.25);
}
[type="range"]:active::-moz-range-thumb {
  transform: scale(1.25);
}
[type="range"]:active::-ms-thumb {
  transform: scale(1.25);
}

input:not(
    [type="checkbox"],
    [type="radio"],
    [type="range"],
    [type="file"]
  )[type="search"] {
  -webkit-padding-start: calc(var(--form-element-spacing-horizontal) + 1.75rem);
  padding-inline-start: calc(var(--form-element-spacing-horizontal) + 1.75rem);
  border-radius: 5rem;
  background-image: var(--icon-search);
  background-position: center left 1.125rem;
  background-size: 1rem auto;
  background-repeat: no-repeat;
}
input:not(
    [type="checkbox"],
    [type="radio"],
    [type="range"],
    [type="file"]
  )[type="search"][aria-invalid] {
  -webkit-padding-start: calc(
    var(--form-element-spacing-horizontal) + 1.75rem
  ) !important;
  padding-inline-start: calc(
    var(--form-element-spacing-horizontal) + 1.75rem
  ) !important;
  background-position: center left 1.125rem, center right 0.75rem;
}
input:not(
    [type="checkbox"],
    [type="radio"],
    [type="range"],
    [type="file"]
  )[type="search"][aria-invalid="false"] {
  background-image: var(--icon-search), var(--icon-valid);
}
input:not(
    [type="checkbox"],
    [type="radio"],
    [type="range"],
    [type="file"]
  )[type="search"][aria-invalid="true"] {
  background-image: var(--icon-search), var(--icon-invalid);
}

[type="search"]::-webkit-search-cancel-button {
  -webkit-appearance: none;
  display: none;
}

[dir="rtl"]
  :where(input):not(
    [type="checkbox"],
    [type="radio"],
    [type="range"],
    [type="file"]
  )[type="search"] {
  background-position: center right 1.125rem;
}
[dir="rtl"]
  :where(input):not(
    [type="checkbox"],
    [type="radio"],
    [type="range"],
    [type="file"]
  )[type="search"][aria-invalid] {
  background-position: center right 1.125rem, center left 0.75rem;
}

/**
 * Table
 */
:where(table) {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  text-indent: 0;
}

th,
td {
  padding: calc(var(--spacing) / 2) var(--spacing);
  border-bottom: var(--border-width) solid var(--table-border-color);
  color: var(--color);
  font-weight: var(--font-weight);
  font-size: var(--font-size);
  text-align: left;
  text-align: start;
}

tfoot th,
tfoot td {
  border-top: var(--border-width) solid var(--table-border-color);
  border-bottom: 0;
}

table[role="grid"] tbody tr:nth-child(odd) {
  background-color: var(--table-row-stripped-background-color);
}

/**
 * Code
 */
pre,
code,
kbd,
samp {
  font-size: 0.875em;
  font-family: var(--font-family);
}

pre {
  -ms-overflow-style: scrollbar;
  overflow: auto;
}

pre,
code,
kbd {
  border-radius: var(--border-radius);
  background: var(--code-background-color);
  color: var(--code-color);
  font-weight: var(--font-weight);
  line-height: initial;
}

code,
kbd {
  display: inline-block;
  padding: 0.375rem 0.5rem;
}

pre {
  display: block;
  margin-bottom: var(--spacing);
  overflow-x: auto;
}
pre > code {
  display: block;
  padding: var(--spacing);
  background: none;
  font-size: 14px;
  line-height: var(--line-height);
}

code b {
  color: var(--code-tag-color);
  font-weight: var(--font-weight);
}
code i {
  color: var(--code-property-color);
  font-style: normal;
}
code u {
  color: var(--code-value-color);
  text-decoration: none;
}
code em {
  color: var(--code-comment-color);
  font-style: normal;
}

kbd {
  background-color: var(--code-kbd-background-color);
  color: var(--code-kbd-color);
  vertical-align: baseline;
}

/**
 * Miscs
 */
hr {
  height: 0;
  border: 0;
  border-top: 1px solid var(--muted-border-color);
  color: inherit;
}

[hidden],
template {
  display: none !important;
}

canvas {
  display: inline-block;
}

/**
 * Accordion (<details>)
 */
details {
  display: block;
  margin-bottom: var(--spacing);
  padding-bottom: var(--spacing);
  border-bottom: var(--border-width) solid var(--accordion-border-color);
}
details summary {
  line-height: 1rem;
  list-style-type: none;
  cursor: pointer;
  transition: color var(--transition);
}
details summary:not([role]) {
  color: var(--accordion-close-summary-color);
}
details summary::-webkit-details-marker {
  display: none;
}
details summary::marker {
  display: none;
}
details summary::-moz-list-bullet {
  list-style-type: none;
}
details summary::after {
  display: block;
  width: 1rem;
  height: 1rem;
  -webkit-margin-start: calc(var(--spacing, 1rem) * 0.5);
  margin-inline-start: calc(var(--spacing, 1rem) * 0.5);
  float: right;
  transform: rotate(-90deg);
  background-image: var(--icon-chevron);
  background-position: right center;
  background-size: 1rem auto;
  background-repeat: no-repeat;
  content: "";
  transition: transform var(--transition);
}
details summary:focus {
  outline: none;
}
details summary:focus:not([role="button"]) {
  color: var(--accordion-active-summary-color);
}
details summary[role="button"] {
  width: 100%;
  text-align: left;
}
details summary[role="button"]::after {
  height: calc(1rem * var(--line-height, 1.5));
  background-image: var(--icon-chevron-button);
}
details summary[role="button"]:not(.outline).contrast::after {
  background-image: var(--icon-chevron-button-inverse);
}
details[open] > summary {
  margin-bottom: calc(var(--spacing));
}
details[open] > summary:not([role]):not(:focus) {
  color: var(--accordion-open-summary-color);
}
details[open] > summary::after {
  transform: rotate(0);
}

[dir="rtl"] details summary {
  text-align: right;
}
[dir="rtl"] details summary::after {
  float: left;
  background-position: left center;
}

/**
 * Card (<article>)
 */
article {
  margin: var(--block-spacing-vertical) 0;
  padding: var(--block-spacing-vertical) var(--block-spacing-horizontal);
  border-radius: var(--border-radius);
  background: var(--card-background-color);
  box-shadow: var(--card-box-shadow);
}
article > header,
article > footer {
  margin-right: calc(var(--block-spacing-horizontal) * -1);
  margin-left: calc(var(--block-spacing-horizontal) * -1);
  padding: calc(var(--block-spacing-vertical) * 0.66)
    var(--block-spacing-horizontal);
  background-color: var(--card-sectionning-background-color);
}
article > header {
  margin-top: calc(var(--block-spacing-vertical) * -1);
  margin-bottom: var(--block-spacing-vertical);
  border-bottom: var(--border-width) solid var(--card-border-color);
  border-top-right-radius: var(--border-radius);
  border-top-left-radius: var(--border-radius);
}
article > footer {
  margin-top: var(--block-spacing-vertical);
  margin-bottom: calc(var(--block-spacing-vertical) * -1);
  border-top: var(--border-width) solid var(--card-border-color);
  border-bottom-right-radius: var(--border-radius);
  border-bottom-left-radius: var(--border-radius);
}

/**
 * Modal (<dialog>)
 */
#mount {
  --scrollbar-width: 0px;
}

dialog {
  display: flex;
  z-index: 999;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  align-items: center;
  justify-content: center;
  width: inherit;
  min-width: 100%;
  height: inherit;
  min-height: 100%;
  padding: var(--spacing);
  border: 0;
  -webkit-backdrop-filter: var(--modal-overlay-backdrop-filter);
  backdrop-filter: var(--modal-overlay-backdrop-filter);
  background-color: var(--modal-overlay-background-color);
  color: var(--color);
}
dialog article {
  max-height: calc(100vh - var(--spacing) * 2);
  overflow: auto;
}
@media (min-width: 576px) {
  dialog article {
    max-width: 510px;
  }
}
@media (min-width: 768px) {
  dialog article {
    max-width: 700px;
  }
}
dialog article > header,
dialog article > footer {
  padding: calc(var(--block-spacing-vertical) * 0.5)
    var(--block-spacing-horizontal);
}
dialog article > header .close {
  margin: 0;
  margin-left: var(--spacing);
  float: right;
}
dialog article > footer {
  text-align: right;
}
dialog article > footer [role="button"] {
  margin-bottom: 0;
}
dialog article > footer [role="button"]:not(:first-of-type) {
  margin-left: calc(var(--spacing) * 0.5);
}
dialog article p:last-of-type {
  margin: 0;
}
dialog article .close {
  display: block;
  width: 1rem;
  height: 1rem;
  margin-top: calc(var(--block-spacing-vertical) * -0.5);
  margin-bottom: var(--typography-spacing-vertical);
  margin-left: auto;
  background-image: var(--icon-close);
  background-position: center;
  background-size: auto 1rem;
  background-repeat: no-repeat;
  opacity: 0.5;
  transition: opacity var(--transition);
}
dialog article .close:is([aria-current], :hover, :active, :focus) {
  opacity: 1;
}
dialog:not([open]),
dialog[open="false"] {
  display: none;
}

.modal-is-open {
  padding-right: var(--scrollbar-width, 0px);
  overflow: hidden;
  pointer-events: none;
}
.modal-is-open dialog {
  pointer-events: auto;
}

:where(.modal-is-opening, .modal-is-closing) dialog,
:where(.modal-is-opening, .modal-is-closing) dialog > article {
  animation-duration: 0.2s;
  animation-timing-function: ease-in-out;
  animation-fill-mode: both;
}
:where(.modal-is-opening, .modal-is-closing) dialog {
  animation-duration: 0.8s;
  animation-name: modal-overlay;
}
:where(.modal-is-opening, .modal-is-closing) dialog > article {
  animation-delay: 0.2s;
  animation-name: modal;
}

.modal-is-closing dialog,
.modal-is-closing dialog > article {
  animation-delay: 0s;
  animation-direction: reverse;
}

@keyframes modal-overlay {
  from {
    -webkit-backdrop-filter: none;
    backdrop-filter: none;
    background-color: transparent;
  }
}
@keyframes modal {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
}
/**
 * Nav
 */
:where(nav li)::before {
  float: left;
  content: "​";
}

nav,
nav ul {
  display: flex;
}

nav {
  justify-content: space-between;
}
nav ol,
nav ul {
  align-items: center;
  margin-bottom: 0;
  padding: 0;
  list-style: none;
}
nav ol:first-of-type,
nav ul:first-of-type {
  margin-left: calc(var(--nav-element-spacing-horizontal) * -1);
}
nav ol:last-of-type,
nav ul:last-of-type {
  margin-right: calc(var(--nav-element-spacing-horizontal) * -1);
}
nav li {
  display: inline-block;
  margin: 0;
  padding: var(--nav-element-spacing-vertical)
    var(--nav-element-spacing-horizontal);
}
nav li > * {
  --spacing: 0;
}
nav :where(a, [role="link"]) {
  display: inline-block;
  margin: calc(var(--nav-link-spacing-vertical) * -1)
    calc(var(--nav-link-spacing-horizontal) * -1);
  padding: var(--nav-link-spacing-vertical) var(--nav-link-spacing-horizontal);
  border-radius: var(--border-radius);
  text-decoration: none;
}
nav :where(a, [role="link"]):is([aria-current], :hover, :active, :focus) {
  text-decoration: none;
}
nav[aria-label="breadcrumb"] {
  align-items: center;
  justify-content: start;
}
nav[aria-label="breadcrumb"] ul li:not(:first-child) {
  -webkit-margin-start: var(--nav-link-spacing-horizontal);
  margin-inline-start: var(--nav-link-spacing-horizontal);
}
nav[aria-label="breadcrumb"] ul li:not(:last-child) ::after {
  position: absolute;
  width: calc(var(--nav-link-spacing-horizontal) * 2);
  -webkit-margin-start: calc(var(--nav-link-spacing-horizontal) / 2);
  margin-inline-start: calc(var(--nav-link-spacing-horizontal) / 2);
  content: "/";
  color: var(--muted-color);
  text-align: center;
}
nav[aria-label="breadcrumb"] a[aria-current] {
  background-color: transparent;
  color: inherit;
  text-decoration: none;
  pointer-events: none;
}
nav [role="button"] {
  margin-right: inherit;
  margin-left: inherit;
  padding: var(--nav-link-spacing-vertical) var(--nav-link-spacing-horizontal);
}

aside nav,
aside ol,
aside ul,
aside li {
  display: block;
}
aside li {
  padding: calc(var(--nav-element-spacing-vertical) * 0.5)
    var(--nav-element-spacing-horizontal);
}
aside li a {
  display: block;
}
aside li [role="button"] {
  margin: inherit;
}

[dir="rtl"] nav[aria-label="breadcrumb"] ul li:not(:last-child) ::after {
  content: "\\";
}

/**
 * Progress
 */
progress {
  display: inline-block;
  vertical-align: baseline;
}

progress {
  -webkit-appearance: none;
  -moz-appearance: none;
  display: inline-block;
  appearance: none;
  width: 100%;
  height: 0.5rem;
  margin-bottom: calc(var(--spacing) * 0.5);
  overflow: hidden;
  border: 0;
  border-radius: var(--border-radius);
  background-color: var(--progress-background-color);
  color: var(--progress-color);
}
progress::-webkit-progress-bar {
  border-radius: var(--border-radius);
  background: none;
}
progress[value]::-webkit-progress-value {
  background-color: var(--progress-color);
}
progress::-moz-progress-bar {
  background-color: var(--progress-color);
}
@media (prefers-reduced-motion: no-preference) {
  progress:indeterminate {
    background: var(--progress-background-color)
      linear-gradient(
        to right,
        var(--progress-color) 30%,
        var(--progress-background-color) 30%
      )
      top left/150% 150% no-repeat;
    animation: progress-indeterminate 1s linear infinite;
  }
  progress:indeterminate[value]::-webkit-progress-value {
    background-color: transparent;
  }
  progress:indeterminate::-moz-progress-bar {
    background-color: transparent;
  }
}

@media (prefers-reduced-motion: no-preference) {
  [dir="rtl"] progress:indeterminate {
    animation-direction: reverse;
  }
}

@keyframes progress-indeterminate {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
/**
 * Dropdown ([role="list"])
 */
details[role="list"],
li[role="list"] {
  position: relative;
}

details[role="list"] summary + ul,
li[role="list"] > ul {
  display: flex;
  z-index: 99;
  position: absolute;
  top: auto;
  right: 0;
  left: 0;
  flex-direction: column;
  margin: 0;
  padding: 0;
  border: var(--border-width) solid var(--dropdown-border-color);
  border-radius: var(--border-radius);
  border-top-right-radius: 0;
  border-top-left-radius: 0;
  background-color: var(--dropdown-background-color);
  box-shadow: var(--card-box-shadow);
  color: var(--dropdown-color);
  white-space: nowrap;
}
details[role="list"] summary + ul li,
li[role="list"] > ul li {
  width: 100%;
  margin-bottom: 0;
  padding: calc(var(--form-element-spacing-vertical) * 0.5)
    var(--form-element-spacing-horizontal);
  list-style: none;
}
details[role="list"] summary + ul li:first-of-type,
li[role="list"] > ul li:first-of-type {
  margin-top: calc(var(--form-element-spacing-vertical) * 0.5);
}
details[role="list"] summary + ul li:last-of-type,
li[role="list"] > ul li:last-of-type {
  margin-bottom: calc(var(--form-element-spacing-vertical) * 0.5);
}
details[role="list"] summary + ul li a,
li[role="list"] > ul li a {
  display: block;
  margin: calc(var(--form-element-spacing-vertical) * -0.5)
    calc(var(--form-element-spacing-horizontal) * -1);
  padding: calc(var(--form-element-spacing-vertical) * 0.5)
    var(--form-element-spacing-horizontal);
  overflow: hidden;
  color: var(--dropdown-color);
  text-decoration: none;
  text-overflow: ellipsis;
}
details[role="list"] summary + ul li a:hover,
li[role="list"] > ul li a:hover {
  background-color: var(--dropdown-hover-background-color);
}

details[role="list"] summary::after,
li[role="list"] > a::after {
  display: block;
  width: 1rem;
  height: calc(1rem * var(--line-height, 1.5));
  -webkit-margin-start: 0.5rem;
  margin-inline-start: 0.5rem;
  float: right;
  transform: rotate(0deg);
  background-position: right center;
  background-size: 1rem auto;
  background-repeat: no-repeat;
  content: "";
}

details[role="list"] {
  padding: 0;
  border-bottom: none;
}
details[role="list"] summary {
  margin-bottom: 0;
}
details[role="list"] summary:not([role]) {
  height: calc(
    1rem * var(--line-height) + var(--form-element-spacing-vertical) * 2 +
      var(--border-width) * 2
  );
  padding: var(--form-element-spacing-vertical)
    var(--form-element-spacing-horizontal);
  border: var(--border-width) solid var(--form-element-border-color);
  border-radius: var(--border-radius);
  background-color: var(--form-element-background-color);
  color: var(--form-element-placeholder-color);
  line-height: inherit;
  cursor: pointer;
  transition: background-color var(--transition), border-color var(--transition),
    color var(--transition), box-shadow var(--transition);
}
details[role="list"] summary:not([role]):active,
details[role="list"] summary:not([role]):focus {
  border-color: var(--form-element-active-border-color);
  background-color: var(--form-element-active-background-color);
}
details[role="list"] summary:not([role]):focus {
  box-shadow: 0 0 0 var(--outline-width) var(--form-element-focus-color);
}
details[role="list"][open] summary {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
details[role="list"][open] summary::before {
  display: block;
  z-index: 1;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: none;
  content: "";
  cursor: default;
}

nav details[role="list"] summary,
nav li[role="list"] a {
  display: flex;
  direction: ltr;
}

nav details[role="list"] summary + ul,
nav li[role="list"] > ul {
  min-width: -moz-fit-content;
  min-width: fit-content;
  border-radius: var(--border-radius);
}
nav details[role="list"] summary + ul li a,
nav li[role="list"] > ul li a {
  border-radius: 0;
}

nav details[role="list"] summary,
nav details[role="list"] summary:not([role]) {
  height: auto;
  padding: var(--nav-link-spacing-vertical) var(--nav-link-spacing-horizontal);
}
nav details[role="list"][open] summary {
  border-radius: var(--border-radius);
}
nav details[role="list"] summary + ul {
  margin-top: var(--outline-width);
  -webkit-margin-start: 0;
  margin-inline-start: 0;
}
nav details[role="list"] summary[role="link"] {
  margin-bottom: calc(var(--nav-link-spacing-vertical) * -1);
  line-height: var(--line-height);
}
nav details[role="list"] summary[role="link"] + ul {
  margin-top: calc(var(--nav-link-spacing-vertical) + var(--outline-width));
  -webkit-margin-start: calc(var(--nav-link-spacing-horizontal) * -1);
  margin-inline-start: calc(var(--nav-link-spacing-horizontal) * -1);
}

li[role="list"]:hover > ul,
li[role="list"] a:active ~ ul,
li[role="list"] a:focus ~ ul {
  display: flex;
}
li[role="list"] > ul {
  display: none;
  margin-top: calc(var(--nav-link-spacing-vertical) + var(--outline-width));
  -webkit-margin-start: calc(
    var(--nav-element-spacing-horizontal) - var(--nav-link-spacing-horizontal)
  );
  margin-inline-start: calc(
    var(--nav-element-spacing-horizontal) - var(--nav-link-spacing-horizontal)
  );
}
li[role="list"] > a::after {
  background-image: var(--icon-chevron);
}

/**
 * Loading ([aria-busy=true])
 */
[aria-busy="true"] {
  cursor: progress;
}

[aria-busy="true"]:not(input, select, textarea)::before {
  display: inline-block;
  width: 1em;
  height: 1em;
  border: 0.1875em solid currentColor;
  border-radius: 1em;
  border-right-color: transparent;
  content: "";
  vertical-align: text-bottom;
  vertical-align: -0.125em;
  animation: spinner 0.75s linear infinite;
  opacity: var(--loading-spinner-opacity);
}
[aria-busy="true"]:not(input, select, textarea):not(:empty)::before {
  margin-right: calc(var(--spacing) * 0.5);
  margin-left: 0;
  -webkit-margin-start: 0;
  margin-inline-start: 0;
  -webkit-margin-end: calc(var(--spacing) * 0.5);
  margin-inline-end: calc(var(--spacing) * 0.5);
}
[aria-busy="true"]:not(input, select, textarea):empty {
  text-align: center;
}

button[aria-busy="true"],
input[type="submit"][aria-busy="true"],
input[type="button"][aria-busy="true"],
input[type="reset"][aria-busy="true"],
a[aria-busy="true"] {
  pointer-events: none;
}

@keyframes spinner {
  to {
    transform: rotate(360deg);
  }
}
/**
 * Tooltip ([data-tooltip])
 */
[data-tooltip] {
  position: relative;
}
[data-tooltip]:not(a, button, input) {
  border-bottom: 1px dotted;
  text-decoration: none;
  cursor: help;
}
[data-tooltip][data-placement="top"]::before,
[data-tooltip][data-placement="top"]::after,
[data-tooltip]::before,
[data-tooltip]::after {
  display: block;
  z-index: 99;
  position: absolute;
  bottom: 100%;
  left: 50%;
  padding: 0.25rem 0.5rem;
  overflow: hidden;
  transform: translate(-50%, -0.25rem);
  border-radius: var(--border-radius);
  background: var(--tooltip-background-color);
  content: attr(data-tooltip);
  color: var(--tooltip-color);
  font-style: normal;
  font-weight: var(--font-weight);
  font-size: 0.875rem;
  text-decoration: none;
  text-overflow: ellipsis;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
}
[data-tooltip][data-placement="top"]::after,
[data-tooltip]::after {
  padding: 0;
  transform: translate(-50%, 0rem);
  border-top: 0.3rem solid;
  border-right: 0.3rem solid transparent;
  border-left: 0.3rem solid transparent;
  border-radius: 0;
  background-color: transparent;
  content: "";
  color: var(--tooltip-background-color);
}
[data-tooltip][data-placement="bottom"]::before,
[data-tooltip][data-placement="bottom"]::after {
  top: 100%;
  bottom: auto;
  transform: translate(-50%, 0.25rem);
}
[data-tooltip][data-placement="bottom"]:after {
  transform: translate(-50%, -0.3rem);
  border: 0.3rem solid transparent;
  border-bottom: 0.3rem solid;
}
[data-tooltip][data-placement="left"]::before,
[data-tooltip][data-placement="left"]::after {
  top: 50%;
  right: 100%;
  bottom: auto;
  left: auto;
  transform: translate(-0.25rem, -50%);
}
[data-tooltip][data-placement="left"]:after {
  transform: translate(0.3rem, -50%);
  border: 0.3rem solid transparent;
  border-left: 0.3rem solid;
}
[data-tooltip][data-placement="right"]::before,
[data-tooltip][data-placement="right"]::after {
  top: 50%;
  right: auto;
  bottom: auto;
  left: 100%;
  transform: translate(0.25rem, -50%);
}
[data-tooltip][data-placement="right"]:after {
  transform: translate(-0.3rem, -50%);
  border: 0.3rem solid transparent;
  border-right: 0.3rem solid;
}
[data-tooltip]:focus::before,
[data-tooltip]:focus::after,
[data-tooltip]:hover::before,
[data-tooltip]:hover::after {
  opacity: 1;
}
@media (hover: hover) and (pointer: fine) {
  [data-tooltip][data-placement="bottom"]:focus::before,
  [data-tooltip][data-placement="bottom"]:focus::after,
  [data-tooltip][data-placement="bottom"]:hover [data-tooltip]:focus::before,
  [data-tooltip][data-placement="bottom"]:hover [data-tooltip]:focus::after,
  [data-tooltip]:hover::before,
  [data-tooltip]:hover::after {
    animation-duration: 0.2s;
    animation-name: tooltip-slide-top;
  }
  [data-tooltip][data-placement="bottom"]:focus::after,
  [data-tooltip][data-placement="bottom"]:hover [data-tooltip]:focus::after,
  [data-tooltip]:hover::after {
    animation-name: tooltip-caret-slide-top;
  }
  [data-tooltip][data-placement="bottom"]:focus::before,
  [data-tooltip][data-placement="bottom"]:focus::after,
  [data-tooltip][data-placement="bottom"]:hover::before,
  [data-tooltip][data-placement="bottom"]:hover::after {
    animation-duration: 0.2s;
    animation-name: tooltip-slide-bottom;
  }
  [data-tooltip][data-placement="bottom"]:focus::after,
  [data-tooltip][data-placement="bottom"]:hover::after {
    animation-name: tooltip-caret-slide-bottom;
  }
  [data-tooltip][data-placement="left"]:focus::before,
  [data-tooltip][data-placement="left"]:focus::after,
  [data-tooltip][data-placement="left"]:hover::before,
  [data-tooltip][data-placement="left"]:hover::after {
    animation-duration: 0.2s;
    animation-name: tooltip-slide-left;
  }
  [data-tooltip][data-placement="left"]:focus::after,
  [data-tooltip][data-placement="left"]:hover::after {
    animation-name: tooltip-caret-slide-left;
  }
  [data-tooltip][data-placement="right"]:focus::before,
  [data-tooltip][data-placement="right"]:focus::after,
  [data-tooltip][data-placement="right"]:hover::before,
  [data-tooltip][data-placement="right"]:hover::after {
    animation-duration: 0.2s;
    animation-name: tooltip-slide-right;
  }
  [data-tooltip][data-placement="right"]:focus::after,
  [data-tooltip][data-placement="right"]:hover::after {
    animation-name: tooltip-caret-slide-right;
  }
}
@keyframes tooltip-slide-top {
  from {
    transform: translate(-50%, 0.75rem);
    opacity: 0;
  }
  to {
    transform: translate(-50%, -0.25rem);
    opacity: 1;
  }
}
@keyframes tooltip-caret-slide-top {
  from {
    opacity: 0;
  }
  50% {
    transform: translate(-50%, -0.25rem);
    opacity: 0;
  }
  to {
    transform: translate(-50%, 0rem);
    opacity: 1;
  }
}
@keyframes tooltip-slide-bottom {
  from {
    transform: translate(-50%, -0.75rem);
    opacity: 0;
  }
  to {
    transform: translate(-50%, 0.25rem);
    opacity: 1;
  }
}
@keyframes tooltip-caret-slide-bottom {
  from {
    opacity: 0;
  }
  50% {
    transform: translate(-50%, -0.5rem);
    opacity: 0;
  }
  to {
    transform: translate(-50%, -0.3rem);
    opacity: 1;
  }
}
@keyframes tooltip-slide-left {
  from {
    transform: translate(0.75rem, -50%);
    opacity: 0;
  }
  to {
    transform: translate(-0.25rem, -50%);
    opacity: 1;
  }
}
@keyframes tooltip-caret-slide-left {
  from {
    opacity: 0;
  }
  50% {
    transform: translate(0.05rem, -50%);
    opacity: 0;
  }
  to {
    transform: translate(0.3rem, -50%);
    opacity: 1;
  }
}
@keyframes tooltip-slide-right {
  from {
    transform: translate(-0.75rem, -50%);
    opacity: 0;
  }
  to {
    transform: translate(0.25rem, -50%);
    opacity: 1;
  }
}
@keyframes tooltip-caret-slide-right {
  from {
    opacity: 0;
  }
  50% {
    transform: translate(-0.05rem, -50%);
    opacity: 0;
  }
  to {
    transform: translate(-0.3rem, -50%);
    opacity: 1;
  }
}

/**
 * Accessibility & User interaction
 */
[aria-controls] {
  cursor: pointer;
}

[aria-disabled="true"],
[disabled] {
  cursor: not-allowed;
}

[aria-hidden="false"][hidden] {
  display: initial;
}

[aria-hidden="false"][hidden]:not(:focus) {
  clip: rect(0, 0, 0, 0);
  position: absolute;
}

a,
area,
button,
input,
label,
select,
summary,
textarea,
[tabindex] {
  -ms-touch-action: manipulation;
}

[dir="rtl"] {
  direction: rtl;
}

/**
* Reduce Motion Features
*/
@media (prefers-reduced-motion: reduce) {
  *:not([aria-busy="true"]),
  :not([aria-busy="true"])::before,
  :not([aria-busy="true"])::after {
    background-attachment: initial !important;
    animation-duration: 1ms !important;
    animation-delay: -1ms !important;
    animation-iteration-count: 1 !important;
    scroll-behavior: auto !important;
    transition-delay: 0s !important;
    transition-duration: 0s !important;
  }
}

#mount#mount {
  /* --primary: rgb(227, 59, 126); */
  --primary: #ea4c89;
  --primary-hover: #f082ac;
  --icon-xia: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGcgaWQ9IkZyYW1lIj4KPHBhdGggaWQ9IlZlY3RvciIgZD0iTTguMDAyOTEgOS42Nzk4M0wzLjgzMzM5IDUuNTEyMjFMMy4wMjUzOSA2LjMxOTgzTDguMDAzMjkgMTEuMjk1MUwxMi45NzYyIDYuMzE5ODNMMTIuMTY3OSA1LjUxMjIxTDguMDAyOTEgOS42Nzk4M1oiIGZpbGw9IiM4MzgzODMiLz4KPC9nPgo8L3N2Zz4K");
  --switch-checked-background-color: var(--primary);
}

li.select-link.select-link:hover > ul {
  display: none;
}
li.select-link.select-link > ul {
  display: none;
}
li.select-link.select-link a:focus ~ ul {
  display: none;
}

li.select-link.select-link a:active ~ ul {
  display: none;
}
li.select-link-active.select-link-active > ul {
  display: flex;
}
li.select-link-active.select-link-active:hover > ul {
  display: flex;
}

li.select-link-active.select-link-active a:focus ~ ul {
  display: flex;
}

li.select-link-active.select-link-active a:active ~ ul {
  display: flex;
}
ul.select-link-ul.select-link-ul {
  right: 0px;
  left: auto;
}

a.select-link-selected {
  background-color: var(--primary-focus);
}
.immersive-translate-no-select {
  -webkit-touch-callout: none; /* iOS Safari */
  -webkit-user-select: none; /* Safari */
  -khtml-user-select: none; /* Konqueror HTML */
  -moz-user-select: none; /* Old versions of Firefox */
  -ms-user-select: none; /* Internet Explorer/Edge */
  user-select: none;
}

/* li[role="list"].no-arrow > a::after { */
/*   background-image: none; */
/*   width: 0; */
/*   color: var(--color); */
/* } */
li[role="list"].no-arrow {
  margin-left: 8px;
  padding-right: 0;
}
li[role="list"] > a::after {
  -webkit-margin-start: 0.2rem;
  margin-inline-start: 0.2rem;
}

li[role="list"].no-arrow > a,
li[role="list"].no-arrow > a:link,
li[role="list"].no-arrow > a:visited {
  color: var(--secondary);
}

select.min-select {
  --form-element-spacing-horizontal: 0;
  margin-bottom: 4px;
  max-width: 128px;
  overflow: hidden;
  color: var(--primary);
  font-size: 13px;
  border: none;
  padding: 0;
  padding-right: 20px;
  padding-left: 8px;
  text-overflow: ellipsis;
  color: var(--color);

}
select.min-select-secondary {
  color: var(--color);
}
select.min-select:focus {
  outline: none;
  border: none;
  --box-shadow: none;
}
select.min-select-no-arrow {
  background-image: none;
  padding-right: 0;
}

select.min-select-left {
  padding-right: 0px;
  /* padding-left: 24px; */
  /* background-position: center left 0; */
  text-overflow: ellipsis;
  text-align: left;
}

.muted {
  color: var(--muted-color);
}

.select.button-select {
  --background-color: var(--secondary-hover);
  --border-color: var(--secondary-hover);
  --color: var(--secondary-inverse);
  cursor: pointer;
  --box-shadow: var(--button-box-shadow, 0 0 0 rgba(0, 0, 0, 0));
  padding: var(--form-element-spacing-vertical)
    var(--form-element-spacing-horizontal);
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
  outline: none;
  background-color: var(--background-color);
  box-shadow: var(--box-shadow);
  color: var(--color);
  font-weight: var(--font-weight);
  font-size: 16px;
  line-height: var(--line-height);
  text-align: center;
  cursor: pointer;
  transition: background-color var(--transition), border-color var(--transition),
    color var(--transition), box-shadow var(--transition);
  -webkit-appearance: button;
  margin: 0;
  margin-bottom: 0px;
  overflow: visible;
  font-family: inherit;
  text-transform: none;
}

body {
  padding: 0;
  margin: 0 auto;
  min-width: 268px;
  border-radius: 10px;
}

.popup-container {
  font-size: 16px;
  --font-size: 16px;
  color: #666;
  background-color: var(--popup-footer-background-color);
  width: 316px;
  min-width: 316px;
}

.popup-content {
  background-color: var(--popup-content-background-color);
  border-radius: 0px 0px 12px 12px;
  padding: 16px 20px;
}

.immersive-translate-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  touch-action: none;
}

.immersive-translate-popup-wrapper {
  background: var(--background-color);
  border-radius: 10px;
  border: 1px solid var(--muted-border-color);
}

#mount#mount {
  --font-family: system-ui, -apple-system, "Segoe UI", "Roboto", "Ubuntu",
    "Cantarell", "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
    "Segoe UI Symbol", "Noto Color Emoji";
  --line-height: 1.5;
  --font-weight: 400;
  --font-size: 16px;
  --border-radius: 4px;
  --border-width: 1px;
  --outline-width: 3px;
  --spacing: 16px;
  --typography-spacing-vertical: 24px;
  --block-spacing-vertical: calc(var(--spacing) * 2);
  --block-spacing-horizontal: var(--spacing);
  --grid-spacing-vertical: 0;
  --grid-spacing-horizontal: var(--spacing);
  --form-element-spacing-vertical: 12px;
  --form-element-spacing-horizontal: 16px;
  --nav-element-spacing-vertical: 16px;
  --nav-element-spacing-horizontal: 8px;
  --nav-link-spacing-vertical: 8px;
  --nav-link-spacing-horizontal: 8px;
  --form-label-font-weight: var(--font-weight);
  --transition: 0.2s ease-in-out;
  --modal-overlay-backdrop-filter: blur(4px);
}

[data-theme="light"],
#mount:not([data-theme="dark"]) {
  --popup-footer-background-color: #e8eaeb;
  --popup-content-background-color: #ffffff;
  --popup-item-background-color: #f3f5f6;
  --popup-item-hover-background-color: #eaeced;
  --popup-trial-pro-background-color: #f9fbfc;
  --text-black-2: #222222;
  --text-gray-2: #222222;
  --text-gray-6: #666666;
  --text-gray-9: #999999;
  --text-gray-c2: #c2c2c2;
  --service-select-content-shadow: 0px 2px 12px 0px rgba(75, 76, 77, 0.2);
  --service-select-border-color: #fafafa;
  --service-select-selected-background-color: #f3f5f6;
}

@media only screen and (prefers-color-scheme: dark) {
  #mount:not([data-theme="light"]) {
    --popup-footer-background-color: #0d0d0d;
    --popup-content-background-color: #191919;
    --popup-item-background-color: #272727;
    --popup-item-hover-background-color: #333333;
    --popup-trial-pro-background-color: #222222;
    --text-black-2: #ffffff;
    --text-gray-2: #dbdbdb;
    --text-gray-6: #b3b3b3;
    --text-gray-9: #777777;
    --text-gray-c2: #5b5b5b;
    --service-select-content-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.9);
    --service-select-border-color: #2c2c2c;
    --service-select-selected-background-color: #333333;
  }
}

[data-theme="dark"] {
  --popup-footer-background-color: #0d0d0d;
  --popup-content-background-color: #191919;
  --popup-item-background-color: #272727;
  --popup-item-hover-background-color: #333333;
  --popup-trial-pro-background-color: #222222;
  --text-black-2: #ffffff;
  --text-gray-2: #dbdbdb;
  --text-gray-6: #b3b3b3;
  --text-gray-9: #777777;
  --text-gray-c2: #5b5b5b;
  --service-select-content-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.9);
  --service-select-border-color: #2c2c2c;
  --service-select-selected-background-color: #333333;
}

.text-balck {
  color: var(--text-black-2);
}

.text-gray-2 {
  color: var(--text-gray-2);
}

.text-gray-6 {
  color: var(--text-gray-6);
}

.text-gray-9 {
  color: var(--text-gray-9);
}

.text-gray-c2 {
  color: var(--text-gray-c2);
}

#mount {
  min-width: 268px;
}

.main-button {
  font-size: 15px;
  vertical-align: middle;
  border-radius: 12px;
  padding: unset;
  height: 44px;
  line-height: 44px;
}

.pt-4 {
  padding-top: 16px;
}

.p-2 {
  padding: 8px;
}

.pl-5 {
  padding-left: 48px;
}

.p-0 {
  padding: 0;
}

.pl-2 {
  padding-left: 8px;
}

.pl-4 {
  padding-left: 24px;
}

.pt-2 {
  padding-top: 8px;
}

.pb-2 {
  padding-bottom: 8px;
}

.pb-4 {
  padding-bottom: 16px;
}

.pb-5 {
  padding-bottom: 20px;
}

.pr-5 {
  padding-right: 48px;
}

.text-sm {
  font-size: 13px;
}

.text-base {
  font-size: 16px;
}

.w-full {
  width: 100%;
}

.flex {
  display: flex;
}

.flex-row {
  flex-direction: row;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-end {
  justify-content: flex-end;
}

.flex-grow {
  flex-grow: 1;
}

.justify-between {
  justify-content: space-between;
}

.mb-0 {
  margin-bottom: 0px;
}

.mb-2 {
  margin-bottom: 8px;
}

.mb-4 {
  margin-bottom: 16px;
}

.mb-3 {
  margin-bottom: 12px;
}

.inline-block {
  display: inline-block;
}

.py-2 {
  padding-top: 8px;
  padding-bottom: 8px;
}

.py-2-5 {
  padding-top: 6px;
  padding-bottom: 6px;
}

.mt-0 {
  margin-top: 0;
}

.mt-2 {
  margin-top: 8px;
}

.mt-3 {
  margin-top: 12px;
}

.mt-4 {
  margin-top: 16px;
}

.mt-5 {
  margin-top: 20px;
}

.mt-6 {
  margin-top: 24px;
}

.mb-1 {
  margin-bottom: 4px;
}

.ml-4 {
  margin-left: 24px;
}

.ml-3 {
  margin-left: 16px;
}

.ml-2 {
  margin-left: 8px;
}

.ml-1 {
  margin-left: 4px;
}

.mr-1 {
  margin-right: 4px;
}

.mr-2 {
  margin-right: 8px;
}

.mr-3 {
  margin-right: 16px;
}

.mx-2 {
  margin-left: 8px;
  margin-right: 8px;
}

.pl-3 {
  padding-left: 12px;
}

.pr-3 {
  padding-right: 12px;
}

.p-3 {
  padding: 12px;
}

.px-1 {
  padding-left: 4px;
  padding-right: 4px;
}

.px-3 {
  padding-left: 12px;
  padding-right: 12px;
}

.pt-3 {
  padding-top: 12px;
}

.px-6 {
  padding-left: 18px;
  padding-right: 18px;
}

.px-4 {
  padding-left: 16px;
  padding-right: 16px;
}

.pt-6 {
  padding-top: 20px;
}

.py-3 {
  padding-top: 12px;
  padding-bottom: 12px;
}

.py-0 {
  padding-top: 0;
  padding-bottom: 0;
}

.left-auto {
  left: auto !important;
}

.max-h-28 {
  max-height: 112px;
}

.max-h-30 {
  max-height: 120px;
}

.overflow-y-scroll {
  overflow-y: scroll;
}

.text-xs {
  font-size: 12px;
}

.flex-1 {
  flex: 1;
}

.flex-3 {
  flex: 3;
}

.flex-4 {
  flex: 4;
}

.flex-2 {
  flex: 2;
}

.items-center {
  align-items: center;
}

.max-content {
  width: max-content;
}

.justify-center {
  justify-content: center;
}

.items-end {
  align-items: flex-end;
}

.items-baseline {
  align-items: baseline;
}

.my-5 {
  margin-top: 48px;
  margin-bottom: 48px;
}

.my-4 {
  margin-top: 24px;
  margin-bottom: 24px;
}

.my-3 {
  margin-top: 16px;
  margin-bottom: 16px;
}

.pt-3 {
  padding-top: 12px;
}

.px-3 {
  padding-left: 12px;
  padding-right: 12px;
}

.pt-2 {
  padding-top: 8px;
}

.px-2 {
  padding-left: 8px;
  padding-right: 8px;
}

.pt-1 {
  padding-top: 4px;
}

.px-1 {
  padding-left: 4px;
  padding-right: 4px;
}

.pb-2 {
  padding-bottom: 8px;
}

.justify-end {
  justify-content: flex-end;
}

.w-auto {
  width: auto;
}

.shrink-0 {
  flex-shrink: 0;
}

select.language-select,
select.translate-service,
select.min-select {
  --form-element-spacing-horizontal: 0;
  margin-bottom: 0px;
  max-width: unset;
  flex: 1;
  overflow: hidden;
  font-size: 13px;
  border: none;
  border-radius: 8px;
  padding-right: 30px;
  padding-left: 0px;
  background-position: center right 12px;
  background-size: 16px auto;
  background-image: var(--icon-xia);
  text-overflow: ellipsis;
  color: var(--text-gray-2);
  background-color: transparent;
  box-shadow: unset !important;
  cursor: pointer;
}

select.more {
  background-position: center right;
  padding-right: 20px;
}

select.transform-padding-left {
  padding-left: 12px;
  transform: translateX(-12px);
  background-position: center right 0px;
}

select.translate-service {
  color: var(--text-black-2);
}

/* dark use black, for windows */
@media (prefers-color-scheme: dark) {
  select.language-select option,
  select.translate-service option,
  select.min-select option {
    background-color: #666666;
  }
}

.text-overflow-ellipsis {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.max-w-20 {
  max-width: 180px;
  white-space: nowrap;
}

select.min-select-secondary {
  color: var(--color);
}

select.min-select:focus {
  outline: none;
  border: none;
  --box-shadow: none;
}

select.min-select-no-arrow {
  background-image: none;
  padding-right: 0;
}

select.min-select-left {
  padding-right: 0px;
  /* padding-left: 24px; */
  /* background-position: center left 0; */
  text-overflow: ellipsis;
  text-align: left;
}

.popup-footer {
  background-color: var(--popup-footer-background-color);
  height: 40px;
}

.text-right {
  text-align: right;
}

.clickable {
  cursor: pointer;
}

.close {
  cursor: pointer;
  width: 16px;
  height: 16px;
  background-image: var(--icon-close);
  background-position: center;
  background-size: auto 1rem;
  background-repeat: no-repeat;
  opacity: 0.5;
  transition: opacity var(--transition);
}

.padding-two-column {
  padding-left: 40px;
  padding-right: 40px;
}

.muted {
  color: #999;
}

.text-label {
  color: #666;
}

.display-none {
  display: none;
}

/* dark use #18232c */
@media (prefers-color-scheme: dark) {
  .text-label {
    color: #9ca3af;
  }
}

.text-decoration-none {
  text-decoration: none;
}

.text-decoration-none:is([aria-current], :hover, :active, :focus),
[role="link"]:is([aria-current], :hover, :active, :focus) {
  --text-decoration: none !important;
  background-color: transparent !important;
}

.language-select-container {
  position: relative;
  width: 100%;
  background-color: var(--popup-item-background-color);
  height: 55px;
  border-radius: 12px;
}

select.language-select {
  color: var(--text-black-2);
  font-size: 14px;
  padding: 8px 24px 24px 16px;
  position: absolute;
  border-radius: 12px;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}

select.text-gray-6 {
  color: var(--text-gray-6);
}

.language-select-container label {
  position: absolute;
  bottom: 10px;
  left: 16px;
  font-size: 12px;
  color: var(--text-gray-9);
  line-height: 12px;
  margin: 0;
}

.translation-service-container {
  background-color: var(--popup-item-background-color);
  border-radius: 12px;
}

.min-select-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 44px;
  background-color: var(--popup-item-background-color);
  padding-left: 16px;
}

.min-select-container:first-child {
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}

.min-select-container:last-child {
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
}

.min-select-container:only-child {
  border-radius: 10px;
}

.translate-mode {
  width: 44px;
  height: 44px;
  border-radius: 22px;
  background-color: var(--popup-item-background-color);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  cursor: pointer;
}

.translate-mode svg {
  fill: var(--text-gray-2);
}

.widgets-container {
  display: flex;
  align-items: stretch;
  justify-content: space-between;
  width: 100%;
  gap: 9px;
}

.widget-item {
  display: flex;
  max-width: 70px;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: var(--popup-item-background-color);
  font-size: 12px;
  min-height: 59px;
  height: 100%;
  border-radius: 8px;
  cursor: pointer;
  flex: 1;
  padding: 8px 4px;
  text-align: center;
}

.widget-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 30px;
  width: 100%;
  margin-bottom: 4px;
}

.widget-title-container {
  display: flex;
  align-items: flex-start;
  justify-content: center;
  height: 24px;
  width: 100%;
  padding-bottom: 4px;
}

.widget-icon {
  margin-bottom: 4px;
  display: flex;
  justify-content: center;
}

.widget-title {
  color: var(--text-gray-6);
  font-size: 12px;
  text-align: center;
  width: 100%;
  font-weight: 400;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 0 2px 2px;
}

.widget-item svg {
  fill: var(--text-gray-2);
}

.share-button-container {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 2px 3px 0 8px;
}

.share-button-container svg {
  fill: var(--text-gray-9);
}

.min-select-container:hover,
.language-select-container:hover,
.widget-item:hover,
.translate-mode:hover {
  background-color: var(--popup-item-hover-background-color);
}

.main-button:hover {
  background-color: #f5508f;
}

.share-button-container:hover {
  background-color: var(--popup-item-background-color);
  border-radius: 6px;
}

.error-boundary {
  background: #fff2f0;
  border: 1px solid #ffccc7;
  display: flex;
  padding: 12px;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.88);
  word-break: break-all;
  margin: 12px;
  border-radius: 12px;
  flex-direction: column;
}

.upgrade-pro {
  border-radius: 11px;
  background: linear-gradient(57deg, #272727 19.8%, #696969 82.2%);
  padding: 2px 8px;
  transform: scale(0.85);
}

.upgrade-pro span {
  background: linear-gradient(180deg, #ffeab4 17.65%, #f8c235 85.29%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: 12px;
  margin-left: 4px;
}

.upgrade-pro svg {
  margin-top: -2px;
}

.upgrade-pro:hover {
  background: linear-gradient(57deg, #3d3d3d 19.8%, #949494 82.2%);
}

.border-bottom-radius-0 {
  border-bottom-left-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}

.trial-pro-container {
  border-radius: 0px 0px 12px 12px;
  background: var(--popup-trial-pro-background-color);
  display: flex;
  align-items: center;
  height: 44px;
  padding-left: 16px;
  padding-right: 12px;
  font-size: 12px;
}

.trial-pro-container label {
  line-height: 13px;
  color: var(--text-black-2);
}

.trial-pro-container img {
  margin-left: 5px;
}

.cursor-pointer {
  cursor: pointer;
}

.upgrade-pro-discount-act {
  height: 25px;
  display: flex;
  padding: 0 4px;
  align-items: center;
  border-radius: 15px;
  background: linear-gradient(
    90deg,
    #cefbfa 11.33%,
    #d7f56f 63.75%,
    #fccd5e 100%
  );
  transform: scale(0.9);
  box-shadow: 0px 1.8px 3.6px 0px rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.upgrade-pro-discount-act span {
  font-size: 12px;
  font-weight: 700;
  margin-left: 4px;
  color: #222222;
}

.upgrade-pro-discount-act:hover {
  text-decoration: unset;
  background: linear-gradient(
    90deg,
    #e2fffe 11.33%,
    #e6ff91 63.75%,
    #ffdf93 100%
  );
}

.custom-select-container {
  width: 200px;
  position: relative;
  flex: 1;
}

#translation-service-select {
  padding-right: 12px;
  padding-left: 6px;
}

.custom-select-content {
  border-radius: 12px;
  background: var(--popup-content-background-color);
  box-shadow: var(--service-select-content-shadow);
  border: 1px solid var(--service-select-border-color);
  padding: 4px 5px;
  position: absolute;
  left: 0;
  right: 0;
  z-index: 100;
  overflow-y: auto;
}

.custom-select-item.default {
  width: 100%;
  padding: 0;
}

.custom-select-item {
  font-size: 13px;
  padding: 5px 6px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  cursor: pointer;
  color: var(--text-black-2);
  width: auto;
  overflow: hidden;
  height: 30px;
  line-height: 30px;
}

.custom-select-item-img {
  width: 20px;
  height: 20px;
  margin-right: 4px;
}

@media (prefers-color-scheme: dark) {
  .custom-select-item-img {
    margin-right: 6px;
  }
}

.custom-select-content .custom-select-item.selected,
.custom-select-content .custom-select-item:hover {
  background: var(--service-select-selected-background-color);
}

.custom-select-item > span {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.custom-select-item-pro {
  font-size: 12px;
  margin-left: 6px;
}

.custom-select-item-pro img {
  margin: 0 3px;
  width: 20px;
}

.custom-select-group-header {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-gray-9);
  padding: 6px 8px 4px;
  margin-top: 2px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.more-container {
  position: relative;
}

.new-menu-indicator {
  position: absolute;
  width: 8px;
  height: 8px;
  background-color: #ef3434;
  border-radius: 50%;
  right: 18px;
  top: 4px;
}

html {
  font-size: 17px;
}

@media print {
  .imt-fb-container {
    display: none !important;
  }
}

#mount#mount {
  position: absolute;
  display: none;
  min-width: 250px;
  height: auto;
  --font-size: 17px;
  font-size: 17px;
}

/* float-ball */
.imt-fb-container {
  position: fixed;
  padding: 0;
  top: 335px;
  width: fit-content;
  display: flex;
  flex-direction: column;
  display: none;
  direction: ltr;
}

.imt-fb-container.left {
  align-items: flex-start;
  left: 0;
}

.imt-fb-container.right {
  align-items: flex-end;
  right: 0;
}

.imt-fb-btn {
  cursor: pointer;
  background: var(--float-ball-more-button-background-color);
  height: 36px;
  width: 56px;
  box-shadow: 2px 6px 10px 0px #0e121629;
}

.imt-fb-btn.left {
  border-top-right-radius: 36px;
  border-bottom-right-radius: 36px;
}

.imt-fb-btn.right {
  border-top-left-radius: 36px;
  border-bottom-left-radius: 36px;
}

.imt-fb-btn div {
  background: var(--float-ball-more-button-background-color);
  height: 36px;
  width: 54px;
  display: flex;
  align-items: center;
}

.imt-fb-btn.left div {
  border-top-right-radius: 34px;
  border-bottom-right-radius: 34px;
  justify-content: flex-end;
}

.imt-fb-btn.right div {
  border-top-left-radius: 34px;
  border-bottom-left-radius: 34px;
}

.imt-fb-logo-img {
  width: 20px;
  height: 20px;
  margin: 0 10px;
}

.imt-fb-logo-img-big-bg {
  width: 28px;
  height: 28px;
  margin: 0;
  padding: 4px;
  background-color: #ed6d8f;
  border-radius: 50%;
  margin: 0 5px;
}

.imt-float-ball-translated {
  position: absolute;
  width: 11px;
  height: 11px;
  bottom: 4px;
  right: 20px;
}

.btn-animate {
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  -webkit-transition: -webkit-transform ease-out 250ms;
  transition: -webkit-transform ease-out 250ms;
  transition: transform ease-out 250ms;
  transition: transform ease-out 250ms, -webkit-transform ease-out 250ms;
}

.imt-fb-setting-btn {
  margin-right: 18px;
  width: 28px;
  height: 28px;
}

.immersive-translate-popup-wrapper {
  background: var(--background-color);
  border-radius: 20px;
  box-shadow: 2px 10px 24px 0px #0e121614;
  border: none;
}

.popup-container {
  border-radius: 20px;
}

.popup-content {
  border-radius: 20px 20px 12px 12px;
}
.popup-footer {
  border-radius: 20px;
}

.imt-fb-close-button {
  pointer-events: all;
  cursor: pointer;
  position: absolute;
  margin-top: -10px;
}

.imt-fb-close-content {
  padding: 22px;
  width: 320px;
  pointer-events: all;
}

.imt-fb-close-title {
  font-weight: 500;
  color: var(--h2-color);
}

.imt-fb-close-radio-content {
  background-color: var(--background-light-green);
  padding: 8px 20px;
}

.imt-fb-radio-sel,
.imt-fb-radio-nor {
  width: 16px;
  height: 16px;
  border-radius: 8px;
  flex-shrink: 0;
}

.imt-fb-radio-sel {
  border: 2px solid var(--primary);
  display: flex;
  align-items: center;
  justify-content: center;
}

.imt-fb-radio-sel div {
  width: 8px;
  height: 8px;
  border-radius: 4px;
  background-color: var(--primary);
}

.imt-fb-radio-nor {
  border: 2px solid #d3d4d6;
}

.imt-fb-primary-btn {
  background-color: var(--primary);
  width: 72px;
  height: 32px;
  color: white;
  border-radius: 8px;
  text-align: center;
  line-height: 32px;
  font-size: 16px;
  cursor: pointer;
}

.imt-fb-default-btn {
  border: 1px solid var(--primary);
  width: 72px;
  height: 32px;
  border-radius: 8px;
  color: var(--primary);
  line-height: 32px;
  text-align: center;
  font-size: 16px;
  cursor: pointer;
}

.imt-fb-guide-container {
  width: 312px;
  transform: translateY(-45%);
}

.imt-fb-guide-bg {
  position: absolute;
  left: 30px;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: -1;
  height: 100%;
  width: 90%;
}

.imt-fb-guide-bg.left {
  transform: scaleX(-1);
}

.imt-fb-guide-content {
  margin: 16px -30px 80px 0px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.imt-fb-guide-content.left {
  margin: 16px 21px 60px 32px;
}

.imt-fb-guide-img {
  width: 220px;
  height: 112px;
}

.imt-fb-guide-message {
  font-size: 16px;
  line-height: 28px;
  color: #333333;
  white-space: pre-wrap;
  text-align: center;
  font-weight: 700;
  margin-bottom: 20px;
}

.imt-fb-guide-button {
  margin-top: 16px;
  line-height: 40px;
  height: 40px;
  padding: 0 20px;
  width: unset;
}

.imt-fb-more-buttons {
  box-shadow: 0px 2px 10px 0px #00000014;
  border: none;
  background: var(--float-ball-more-button-background-color);
  width: 36px;
  display: flex;
  flex-direction: column;
  border-radius: 18px;
  margin-top: 0px;
  padding: 7px 0 7px 0;
}

.imt-fb-more-buttons > div {
  margin: auto;
}

.imt-fb-side,
.imt-fb-reward {
  border-radius: 50%;
  cursor: pointer;
  pointer-events: all;
  position: relative;
}

.imt-fb-side {
  margin: 10px 0;
}

.imt-fb-new-badge {
  width: 26px;
  height: 14px;
  padding: 3px;
  background-color: #f53f3f;
  border-radius: 4px;
  position: absolute;
  top: -5px;
  right: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.imt-fb-side *,
.imt-fb-reward * {
  pointer-events: all;
}

.imt-fb-more-button {
  width: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
/* Sheet.css */
.immersive-translate-sheet {
  position: fixed;
  transform: translateY(100%);
  /* Start off screen */
  left: 0;
  right: 0;
  background-color: white;
  transition: transform 0.3s ease-out;
  /* Smooth slide transition */
  box-shadow: 0px -2px 10px rgba(0, 0, 0, 0.1);
  /* Ensure it's above other content */
  bottom: 0;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  overflow: hidden;
}

.immersive-translate-sheet.visible {
  transform: translateY(0);
}

.immersive-translate-sheet-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 0;
  transition: opacity 0.3s ease-out;
}

.immersive-translate-sheet-backdrop.visible {
  opacity: 1;
}

.popup-container-sheet {
  max-width: 100vw;
  width: 100vw;
}

.imt-no-events svg * {
  pointer-events: none !important;
}

.imt-manga-button {
  width: 36px;
  display: flex;
  flex-direction: column;
  position: relative;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  pointer-events: all;
  margin: 0 0 10px 0;
  background-color: var(--float-ball-more-button-background-color);
  border-radius: 18px;
  filter: drop-shadow(0px 2px 10px rgba(0, 0, 0, 0.08));
  opacity: 0.5;
  right: 8px;
  padding: 10px 0 4px 0;
}

.imt-manga-feedback {
  cursor: pointer;
  margin-bottom: 10px;
}

.imt-fb-feedback {
  cursor: pointer;
  margin-top: 10px;
}

.imt-fb-upgrade-button {
  cursor: pointer;
  margin-top: 10px;
}

.imt-manga-button:hover {
  opacity: 1;
}

.imt-manga-translated {
  position: absolute;
  left: 24px;
  top: 20px;
}

.imt-float-ball-loading {
  animation: imt-loading-animation 0.6s infinite linear !important;
}

.imt-manga-guide-bg {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: -1;
  width: 372px;
  transform: translateY(-50%);
}
.imt-manga-guide-content {
  position: absolute;
  top: 15px;
  left: 0;
  right: 0;
  margin: 0 40px 0;
}

.img-manga-guide-button {
  width: fit-content;
  margin: 16px auto;
}

.img-manga-close {
  position: absolute;
  bottom: -200px;
  width: 32px;
  height: 32px;
  left: 0;
  right: 0;
  margin: auto;
  cursor: pointer;
}

.imt-fb-container.dragging .imt-fb-more-buttons,
.imt-fb-container.dragging .imt-manga-button,
.imt-fb-container.dragging .btn-animate:not(.imt-fb-btn) {
  display: none !important;
}

.imt-fb-container.dragging .imt-fb-btn {
  border-radius: 50% !important;
  width: 36px !important;
  height: 36px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: move !important;
}

.imt-fb-container.dragging .imt-fb-btn div {
  border-radius: 50% !important;
  width: 36px !important;
  height: 36px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin: 0 !important;
}

.imt-fb-container.dragging .imt-fb-btn.left,
.imt-fb-container.dragging .imt-fb-btn.right {
  border-radius: 50% !important;
}

.imt-fb-container.dragging .imt-fb-btn.left div,
.imt-fb-container.dragging .imt-fb-btn.right div {
  border-radius: 50% !important;
}

.imt-fb-container.dragging .imt-fb-logo-img {
  margin: 0 !important;
  padding: 4px !important;
}

.imt-fb-container.dragging .imt-float-ball-translated {
  right: 2px !important;
  bottom: 2px !important;
}

@-webkit-keyframes imt-loading-animation {
  from {
    -webkit-transform: rotate(0deg);
  }

  to {
    -webkit-transform: rotate(359deg);
  }
}

@keyframes imt-loading-animation {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(359deg);
  }
}
</style><div id="mount" style="display: block;"><div class="imt-fb-container right notranslate " style="z-index: 2147483637; pointer-events: none; right: 0px; top: 546px; display: flex;"><div class="btn-animate" style="transform: translateX(60px); opacity: 1;"><div class=" btn-animate" style="position: relative; pointer-events: all; display: inline-block; opacity: 1;"><div><div class="imt-fb-btn imt-fb-more-button imt-fb-reward"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="18" viewBox="0 0 16 18" fill="none"><path d="M0.737382 16.9325C0.737382 17.3776 0.858941 17.4998 1.23932 17.4998H7.26262V8.99504H0.737382V16.9325ZM8.73738 17.4998H14.7607C15.1418 17.4998 15.2626 17.3784 15.2626 16.9325V8.99504H8.73667V17.4998H8.73738ZM15.2728 4.36327H11.6364C12.5143 4.15085 13.4414 3.46316 13.5556 2.65636C13.6895 1.7352 12.6597 0.338955 11.4136 0.515904C9.92369 0.734577 8.83127 2.13177 8.06033 3.21528C7.29528 2.1532 6.29818 0.911526 4.86983 0.550611C3.48646 0.202972 2.53898 1.72648 2.56433 2.66945C2.58914 3.47074 3.46111 4.15161 4.3636 4.36327H0.727208C0.277745 4.36327 0 4.6322 0 5.13611V8.22542L7.27279 8.22694V4.36327H8.72721V8.22694L16 8.22542V5.13611C16 4.6322 15.7215 4.36327 15.2728 4.36327ZM3.56214 2.95393C3.31046 2.65636 3.48074 2.2522 3.63854 1.94705C3.92878 1.40918 4.33308 1.08241 5.02173 1.3868C5.98188 1.81258 6.68874 2.71288 7.26476 3.52024C6.04507 3.66324 4.22544 3.71881 3.56214 2.95393ZM8.8684 3.5064C9.46173 2.69809 10.1506 1.87744 11.1039 1.37296C12.1035 0.848181 13.0305 2.26624 12.5564 2.9471C12.1914 3.46392 11.0345 3.51266 10.4097 3.54187C9.89583 3.56912 9.38073 3.55726 8.8684 3.5064Z" fill="#ED6D8F"></path><path d="M7.2619 8.99518H3.21817C2.52624 10.2879 2.15346 11.7457 2.13455 13.2328C2.11563 14.7198 2.4512 16.1878 3.10999 17.4999H7.2619V8.99518ZM8.72649 4.73418V8.22707L15.9993 8.22555V6.8496C15.0303 5.91196 13.8661 5.23144 12.602 4.86376C11.3379 4.49607 10.0098 4.45167 8.72649 4.73418ZM15.2619 16.9327V8.99518H8.73595V17.4999H14.76C15.1411 17.4999 15.2619 17.3785 15.2619 16.9327ZM7.27207 8.22707V5.2068C5.83313 5.83641 4.58945 6.88261 3.68262 8.22631L7.27207 8.22707Z" fill="#E55884"></path><path d="M8.73723 17.4998H14.7612C15.1423 17.4998 15.2632 17.3784 15.2632 16.9325V8.99502H8.73723V17.4998ZM5.37501 15.3432C5.37449 16.073 5.47179 16.7991 5.664 17.4998H7.26318V10.1671C6.04626 11.5806 5.3725 13.4275 5.37501 15.3432ZM12.6044 7.66212C11.67 7.66082 10.7443 7.85258 9.87873 8.22672L15.3273 8.22558C14.4625 7.85221 13.5378 7.66085 12.6044 7.66212Z" fill="#DB3E77"></path><path d="M14.9676 11.0576C12.3136 11.0576 9.99368 12.5771 8.73633 14.842V17.5H14.7603C15.1414 17.5 15.2623 17.3787 15.2623 16.9328V11.0639C15.1645 11.0597 15.0662 11.0576 14.9676 11.0576Z" fill="#D63070"></path><path d="M4.20238 5.06029C4.33786 5.2763 3.62511 5.54334 2.87327 6.07551C2.12143 6.60769 1.6152 7.20397 1.47954 6.98795C1.34388 6.77193 1.75979 6.03132 2.51163 5.49896C3.26347 4.9666 4.0669 4.84408 4.20238 5.06029Z" fill="white"></path></svg><svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg" style="opacity: 0; position: absolute; right: 0px; top: 0px; display: block; transform: translate(30%, -30%);"><g clip-path="url(#clip0_2589_9951)"><path d="M7 14C5.14348 14 3.36301 13.2625 2.05025 11.9497C0.737498 10.637 0 8.85652 0 7C0 5.14348 0.737498 3.36301 2.05025 2.05025C3.36301 0.737498 5.14348 0 7 0C8.85652 0 10.637 0.737498 11.9497 2.05025C13.2625 3.36301 14 5.14348 14 7C14 8.85652 13.2625 10.637 11.9497 11.9497C10.637 13.2625 8.85652 14 7 14ZM4.183 5.064L6.118 7L4.183 8.936C4.12409 8.99361 4.07719 9.06234 4.04502 9.1382C4.01285 9.21406 3.99605 9.29554 3.99559 9.37794C3.99513 9.46034 4.01101 9.54201 4.04234 9.61823C4.07366 9.69444 4.11978 9.76369 4.17805 9.82195C4.23631 9.88022 4.30556 9.92634 4.38177 9.95766C4.45799 9.98898 4.53966 10.0049 4.62206 10.0044C4.70446 10.004 4.78594 9.98715 4.8618 9.95498C4.93766 9.92281 5.00639 9.87591 5.064 9.817L7 7.882L8.936 9.817C9.05327 9.93168 9.21104 9.99548 9.37506 9.99457C9.53908 9.99365 9.69612 9.92809 9.8121 9.8121C9.92809 9.69612 9.99365 9.53908 9.99457 9.37506C9.99548 9.21104 9.93168 9.05327 9.817 8.936L7.882 7L9.817 5.064C9.87591 5.00639 9.92281 4.93766 9.95498 4.8618C9.98715 4.78594 10.004 4.70446 10.0044 4.62206C10.0049 4.53966 9.98898 4.45799 9.95766 4.38177C9.92634 4.30556 9.88022 4.23631 9.82195 4.17805C9.76369 4.11978 9.69444 4.07366 9.61823 4.04234C9.54201 4.01101 9.46034 3.99513 9.37794 3.99559C9.29554 3.99605 9.21406 4.01285 9.1382 4.04502C9.06234 4.07719 8.99361 4.12409 8.936 4.183L7 6.118L5.064 4.183C4.94673 4.06832 4.78896 4.00452 4.62494 4.00543C4.46092 4.00635 4.30388 4.07191 4.1879 4.1879C4.07191 4.30388 4.00635 4.46092 4.00543 4.62494C4.00452 4.78896 4.06832 4.94673 4.183 5.064Z" fill="#B1B1B1" fill-opacity="0.32"></path></g><defs><clippath id="clip0_2589_9951"><rect width="14" height="14" fill="white"></rect></clippath></defs></svg></div></div></div></div><div style="position: relative; width: 100%; opacity: 0;"></div><div class="btn-animate" style="transform: translateX(60px); opacity: 1; padding-left: 10px;"><div class=" btn-animate" style="position: relative; pointer-events: all; display: inline-block; opacity: 1;"><div><div class="imt-fb-btn imt-fb-more-button imt-fb-side"><svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M8.60547 12.9228C8.84029 12.9228 9.03755 13.0022 9.19629 13.161C9.3551 13.3198 9.43457 13.5171 9.43457 13.7519V18.5107C9.43457 18.7453 9.35513 18.9426 9.19629 19.1015C9.03755 19.2602 8.84029 19.3398 8.60547 19.3398H3.8457C3.61127 19.3397 3.41464 19.26 3.25586 19.1015C3.09712 18.9426 3.01758 18.7453 3.01758 18.5107V13.7519C3.01758 13.517 3.09712 13.3198 3.25586 13.161C3.41465 13.0023 3.61125 12.9229 3.8457 12.9228H8.60547ZM17.208 12.9228C17.4427 12.9228 17.6399 13.0022 17.7988 13.161C17.9575 13.3198 18.0371 13.5171 18.0371 13.7519V18.5107C18.0371 18.7453 17.9576 18.9426 17.7988 19.1015C17.6399 19.2602 17.4427 19.3398 17.208 19.3398H12.4492C12.2144 19.3398 12.0171 19.2602 11.8584 19.1015C11.6995 18.9426 11.6201 18.7453 11.6201 18.5107V13.7519C11.6201 13.517 11.6995 13.3198 11.8584 13.161C12.0171 13.0022 12.2144 12.9228 12.4492 12.9228H17.208ZM4.39258 17.9648H8.05957V14.2978H4.39258V17.9648ZM12.9951 17.9648H16.6621V14.2978H12.9951V17.9648ZM14.7598 2.92179C14.8641 2.57295 15.3576 2.57295 15.4619 2.92179L15.9561 4.57511C16.1376 5.18219 16.5965 5.66815 17.1924 5.8837L18.7412 6.44327C19.0635 6.56002 19.0633 7.01583 18.7412 7.13273L17.1924 7.69327C16.5966 7.90881 16.1376 8.39389 15.9561 9.00089L15.4619 10.6552C15.3575 11.0038 14.8642 11.0037 14.7598 10.6552L14.2646 9.00089C14.0831 8.39401 13.625 7.90881 13.0293 7.69327L11.4805 7.13273C11.158 7.01598 11.1579 6.55996 11.4805 6.44327L13.0293 5.8837C13.6251 5.66814 14.0831 5.18219 14.2646 4.57511L14.7598 2.92179ZM8.60547 4.32023C8.84029 4.32023 9.03755 4.39977 9.19629 4.55851C9.35496 4.71727 9.43448 4.91396 9.43457 5.14835V9.90812C9.43457 10.1429 9.35518 10.3402 9.19629 10.4989C9.03755 10.6578 8.84029 10.7372 8.60547 10.7372H3.8457C3.61131 10.7371 3.41463 10.6576 3.25586 10.4989C3.09712 10.3402 3.01758 10.1429 3.01758 9.90812V5.14835C3.01767 4.91386 3.09721 4.71731 3.25586 4.55851C3.41466 4.39986 3.61121 4.32032 3.8457 4.32023H8.60547ZM4.39258 9.36222H8.05957V5.69523H4.39258V9.36222Z" fill="#666666"></path></svg><svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg" style="position: absolute; right: 0px; top: 0px; display: none; transform: translate(30%, -30%);"><g clip-path="url(#clip0_34242_2370)"><path d="M7 14C5.14348 14 3.36301 13.2625 2.05025 11.9497C0.737498 10.637 0 8.85652 0 7C0 5.14348 0.737498 3.36301 2.05025 2.05025C3.36301 0.737498 5.14348 0 7 0C8.85652 0 10.637 0.737498 11.9497 2.05025C13.2625 3.36301 14 5.14348 14 7C14 8.85652 13.2625 10.637 11.9497 11.9497C10.637 13.2625 8.85652 14 7 14Z" fill="#B1B1B1" fill-opacity="0.32"></path><mask id="mask0_34242_2370" maskUnits="userSpaceOnUse" x="1" y="1" width="12" height="12" style="mask-type: alpha;"><rect x="1" y="1" width="12" height="12" fill="#D9D9D9"></rect></mask><g mask="url(#mask0_34242_2370)"><path d="M8.90169 3.66124V6.89549L9.94631 7.94012C9.9939 7.9877 10.0317 8.04208 10.0597 8.10324C10.0877 8.16441 10.1017 8.23328 10.1017 8.30987V8.61487C10.1017 8.76095 10.0516 8.88433 9.95144 8.98499C9.85119 9.08566 9.72835 9.13599 9.58294 9.13599H7.51881V11.8871C7.51881 12.0325 7.46898 12.1553 7.36931 12.2555C7.26956 12.3557 7.14735 12.4059 7.00269 12.4059C6.85802 12.4059 6.73494 12.3557 6.63344 12.2555C6.53202 12.1553 6.48131 12.0325 6.48131 11.8871V9.13599H4.41719C4.27177 9.13599 4.14894 9.08566 4.04869 8.98499C3.94852 8.88433 3.89844 8.76095 3.89844 8.61487V8.30987C3.89844 8.23328 3.91244 8.16441 3.94044 8.10324C3.96844 8.04208 4.00623 7.9877 4.05381 7.94012L5.09844 6.89549V3.66124H4.94844C4.82094 3.64733 4.71406 3.59299 4.62781 3.49824C4.54156 3.40358 4.49844 3.28987 4.49844 3.15712C4.49844 3.01245 4.54852 2.88937 4.64869 2.78787C4.74894 2.68645 4.87177 2.63574 5.01719 2.63574H8.98294C9.12835 2.63574 9.25119 2.68558 9.35144 2.78524C9.4516 2.88491 9.50169 3.00712 9.50169 3.15187C9.50169 3.28462 9.45856 3.3992 9.37231 3.49562C9.28606 3.59212 9.17919 3.64733 9.05169 3.66124H8.90169ZM5.36169 8.09849H8.63844L7.86419 7.32424V3.67324H6.13594V7.32424L5.36169 8.09849Z" fill="white"></path></g></g><defs><clippath id="clip0_34242_2370"><rect width="14" height="14" fill="white"></rect></clippath></defs></svg></div></div></div></div><div hidden="" class="imt-no-events btn-animate " id="manga-button" style="position: relative;"><div class="imt-manga-button" style="transform: translateX(2px);"><div class=" " style="position: relative; pointer-events: all; display: inline-block; opacity: 1;"><div><svg class="imt-manga-feedback" width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M11.0003 14.2749C11.213 14.2749 11.3895 14.2047 11.5299 14.0643C11.6705 13.9239 11.7408 13.7473 11.7408 13.5345C11.7408 13.3218 11.6705 13.1453 11.5299 13.0049C11.3895 12.8645 11.213 12.7943 11.0003 12.7943C10.7877 12.7943 10.6111 12.8645 10.4707 13.0049C10.3302 13.1453 10.2599 13.3218 10.2599 13.5345C10.2599 13.7473 10.3302 13.9239 10.4707 14.0643C10.6111 14.2047 10.7877 14.2749 11.0003 14.2749ZM11.0003 11.0842C11.1954 11.0842 11.3587 11.0185 11.4903 10.8869C11.622 10.7552 11.6878 10.5918 11.6878 10.3967V6.23645C11.6878 6.04135 11.622 5.87803 11.4903 5.74649C11.3587 5.6148 11.1954 5.54895 11.0003 5.54895C10.8052 5.54895 10.6419 5.6148 10.5104 5.74649C10.3787 5.87803 10.3128 6.04135 10.3128 6.23645V10.3967C10.3128 10.5918 10.3787 10.7552 10.5104 10.8869C10.6419 11.0185 10.8052 11.0842 11.0003 11.0842ZM5.53562 16.8311L3.70045 18.666C3.43966 18.9269 3.13968 18.9861 2.80051 18.8434C2.4615 18.7005 2.29199 18.4434 2.29199 18.072V4.73816C2.29199 4.27509 2.45241 3.88314 2.77324 3.5623C3.09408 3.24147 3.48603 3.08105 3.9491 3.08105H18.0516C18.5146 3.08105 18.9066 3.24147 19.2274 3.5623C19.5482 3.88314 19.7087 4.27509 19.7087 4.73816V15.174C19.7087 15.637 19.5482 16.029 19.2274 16.3498C18.9066 16.6706 18.5146 16.8311 18.0516 16.8311H5.53562ZM4.95033 15.4561H18.0516C18.1221 15.4561 18.1868 15.4266 18.2454 15.3678C18.3042 15.3092 18.3337 15.2445 18.3337 15.174V4.73816C18.3337 4.66758 18.3042 4.60295 18.2454 4.54428C18.1868 4.48546 18.1221 4.45605 18.0516 4.45605H3.9491C3.87851 4.45605 3.81389 4.48546 3.75522 4.54428C3.6964 4.60295 3.66699 4.66758 3.66699 4.73816V16.7254L4.95033 15.4561Z" fill="#666666"></path></svg></div></div><div style="position: relative;"><svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="manhua"><path id="Vector" d="M14.8853 4.92364C14.8853 4.92364 16.3905 10.4362 22.6668 4C22.6668 4 20.3381 10.8907 25.3364 10.0843C25.3364 10.0843 22.0563 15.6994 29 18.0599C29 18.0599 22.9934 19.306 21.1617 28C21.1617 28 17.7679 24.54 14.8853 27.3549C14.8853 27.3549 13.3233 23.5724 7.33097 26.27C7.33097 26.27 10.1141 20.6549 4.83179 21.0507C4.83179 21.0507 7.16057 18.8955 3 15.9047C3 15.9047 7.50137 16.1833 6.33697 11.7117C6.33697 11.7117 10.0005 12.3421 8.66576 6.82957C8.65156 6.81491 12.4855 9.80574 14.8853 4.92364Z" fill="#ED6D8F"></path><path id="Vector_2" d="M20.8599 13.7022C20.885 13.1361 20.9543 12.5713 20.9959 12.0052C21.0337 11.568 20.8107 11.2794 20.3876 11.18C20.0759 11.1013 19.7508 11.0867 19.433 11.137C19.1951 11.1945 18.9542 11.2396 18.7113 11.2721C18.2403 11.3028 17.9973 11.5275 17.9796 11.988C17.977 12.0833 17.9596 12.1777 17.928 12.268C17.3034 13.9102 16.6774 15.5499 16.0503 17.1873C16.0301 17.2401 16.0062 17.2904 15.9671 17.3776C15.7291 16.8975 15.4281 16.4898 15.2745 15.9986C14.8073 14.5152 14.3186 13.033 13.8312 11.5594C13.6826 11.1112 13.3489 10.9344 12.8754 11.0216C12.7889 11.0365 12.7008 11.0398 12.6134 11.0314C12.2241 10.9938 11.8311 11.0404 11.4623 11.1677C11.0946 11.2991 10.9498 11.557 11.0152 11.9254C11.0428 12.0371 11.0643 12.1503 11.0795 12.2643C11.1223 13.1902 11.1777 14.1087 11.2054 15.0321C11.257 16.7992 11.2117 18.5651 11.0858 20.3284C11.0644 20.6354 11.0304 20.9424 11.0228 21.2494C11.0115 21.6092 11.1613 21.7811 11.5266 21.8143C11.9976 21.8573 12.4711 21.8708 12.9421 21.9088C13.0309 21.9201 13.121 21.9003 13.1962 21.8528C13.2714 21.8053 13.3268 21.7334 13.3527 21.6497C13.3996 21.5394 13.4252 21.4216 13.4282 21.3022C13.4295 20.8258 13.4207 20.3493 13.4081 19.8741C13.393 19.3264 13.3917 18.7763 13.3438 18.231C13.2857 17.5839 13.266 16.934 13.2847 16.2847C13.2847 16.2466 13.291 16.2073 13.2985 16.1312C13.3338 16.2024 13.3514 16.2356 13.3665 16.2712C13.9017 17.5228 14.3617 18.8037 14.7443 20.1074C14.7928 20.2421 14.7928 20.3889 14.7443 20.5237C14.6322 20.8196 14.7141 21.037 14.9659 21.1377C15.4445 21.3268 15.9331 21.4926 16.4155 21.6731C16.4865 21.7033 16.566 21.7091 16.6408 21.6895C16.7157 21.6698 16.7815 21.6259 16.8273 21.565C16.9085 21.4643 16.9743 21.3526 17.0225 21.2335C17.0537 21.1374 17.0798 21.0399 17.1006 20.9412C17.3185 20.2425 17.5653 19.5499 17.7517 18.8438C17.9785 17.9723 18.2624 17.1158 18.6018 16.2798C18.6201 16.2439 18.6411 16.2094 18.6647 16.1766C18.6761 16.2319 18.6761 16.254 18.6761 16.2761C18.6345 17.59 18.5955 18.8978 18.5501 20.2056C18.5363 20.5949 18.491 20.9829 18.4809 21.3722C18.4721 21.705 18.6207 21.8708 18.9557 21.9002C19.4355 21.9432 19.9191 21.9592 20.4002 21.9973C20.4888 22.0079 20.5784 21.9875 20.653 21.9399C20.7277 21.8922 20.7827 21.8203 20.8082 21.7369C20.8531 21.6305 20.8766 21.5167 20.8775 21.4017C20.88 20.7668 20.8674 20.132 20.8674 19.4971C20.8662 19.2846 20.8687 19.0722 20.8523 18.8622C20.8158 18.3968 20.7264 17.9314 20.7339 17.4685C20.7515 16.2122 20.8044 14.9572 20.8599 13.7022Z" fill="white"></path></g></svg><svg hidden="true" class="imt-manga-translated" width="11" height="11" viewBox="0 0 11 11" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="5.5" cy="5.5" r="5.5" fill="#68CD52"></circle><path d="M1.40857 5.87858L2.24148 5.18962L4.15344 6.64214C4.15344 6.64214 6.33547 4.15566 9.00658 2.48145L9.32541 2.87514C9.32541 2.87514 6.28665 5.55844 4.71735 9.07881L1.40857 5.87858Z" fill="white"></path></svg></div><svg class="imt-float-ball-loading" hidden="true" width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg" style="margin: 9px;"><path d="M9.42859 0C9.84288 0 10.1929 0.387143 10.1929 0.847143V3.99429C10.1929 4.45429 9.84431 4.84143 9.42859 4.84143C9.01431 4.84143 8.66431 4.45571 8.66431 3.99429V0.847143C8.66431 0.387143 9.01288 0 9.42859 0Z" fill="#E9E9E9"></path><path d="M14.1301 1.38877C14.5158 1.62591 14.6301 2.12163 14.4258 2.52305L12.9515 5.19448C12.901 5.28714 12.8325 5.36876 12.75 5.43455C12.6675 5.50035 12.5727 5.54898 12.4712 5.5776C12.3696 5.60621 12.2634 5.61424 12.1586 5.60119C12.0539 5.58814 11.9529 5.55429 11.8615 5.50163C11.6787 5.38432 11.5468 5.20237 11.4923 4.9921C11.4377 4.78184 11.4645 4.55874 11.5672 4.36734L13.0415 1.69591C13.2686 1.29448 13.7443 1.15305 14.1301 1.38877Z" fill="#989697"></path><path d="M17.4685 4.75707C17.5813 4.95451 17.6123 5.18824 17.5549 5.40825C17.4975 5.62826 17.3563 5.81705 17.1614 5.93422L14.4971 7.52564C14.0971 7.76993 13.6014 7.62422 13.3657 7.20707C13.2532 7.00994 13.2222 6.77667 13.2793 6.55702C13.3365 6.33737 13.4771 6.14874 13.6714 6.03136L16.3357 4.43993C16.7371 4.21993 17.2557 4.34136 17.4685 4.7585V4.75707Z" fill="#9B999A"></path><path d="M18.8572 9.42835C18.8572 9.84263 18.47 10.1926 18.01 10.1926H14.8629C14.4029 10.1926 14.0157 9.84406 14.0157 9.42835C14.0157 9.01406 14.4029 8.66406 14.8629 8.66406H18.01C18.47 8.66406 18.8572 9.01263 18.8572 9.42835Z" fill="#A3A1A2"></path><path d="M17.4686 14.1303C17.3515 14.3134 17.1697 14.4455 16.9594 14.5003C16.7491 14.5552 16.5259 14.5286 16.3343 14.426L13.6629 12.9517C13.5702 12.9012 13.4886 12.8327 13.4228 12.7503C13.357 12.6678 13.3084 12.573 13.2798 12.4714C13.2512 12.3698 13.2431 12.2636 13.2562 12.1589C13.2692 12.0542 13.3031 11.9532 13.3558 11.8617C13.4731 11.6789 13.655 11.547 13.8653 11.4925C14.0755 11.4379 14.2986 11.4647 14.49 11.5674L17.1615 13.0417C17.5629 13.2689 17.7043 13.7446 17.4686 14.1303Z" fill="#ABA9AA"></path><path opacity="0.7" d="M14.1 17.4686C13.9026 17.5814 13.6689 17.6124 13.4489 17.555C13.2288 17.4976 13.04 17.3564 12.9229 17.1615L11.3315 14.4972C11.0872 14.0972 11.2329 13.6015 11.65 13.3658C11.8472 13.2533 12.0804 13.2224 12.3001 13.2795C12.5197 13.3366 12.7084 13.4773 12.8257 13.6715L14.4172 16.3358C14.6372 16.7372 14.5157 17.2558 14.0986 17.4686H14.1Z" fill="#B2B2B2"></path><path opacity="0.6" d="M9.42859 18.8571C9.01431 18.8571 8.66431 18.4699 8.66431 18.0099V14.8628C8.66431 14.4028 9.01288 14.0156 9.42859 14.0156C9.84288 14.0156 10.1929 14.4028 10.1929 14.8628V18.0099C10.1929 18.4699 9.84431 18.8571 9.42859 18.8571Z" fill="#BAB8B9"></path><path opacity="0.5" d="M4.72717 17.4685C4.5441 17.3514 4.41195 17.1696 4.35713 16.9593C4.30231 16.749 4.32885 16.5258 4.43145 16.3342L5.90574 13.6628C5.95622 13.5701 6.02472 13.4885 6.1072 13.4227C6.18969 13.3569 6.2845 13.3083 6.38606 13.2797C6.48762 13.251 6.59387 13.243 6.69857 13.2561C6.80327 13.2691 6.90431 13.303 6.99574 13.3556C7.38145 13.5914 7.49431 14.0885 7.29002 14.4899L5.81574 17.1614C5.5886 17.5628 5.11288 17.7042 4.72717 17.4685Z" fill="#C2C0C1"></path><path opacity="0.4" d="M1.38862 14.1002C1.27584 13.9027 1.24483 13.669 1.30223 13.449C1.35964 13.229 1.50089 13.0402 1.69576 12.923L4.36004 11.3316C4.76004 11.0873 5.25576 11.233 5.49147 11.6502C5.60393 11.8473 5.63491 12.0806 5.5778 12.3002C5.52069 12.5199 5.38 12.7085 5.18576 12.8259L2.52004 14.4173C2.12004 14.6373 1.60004 14.5159 1.38862 14.0987V14.1002Z" fill="#CBCBCB"></path><path d="M0 9.42835C0 9.01406 0.387143 8.66406 0.847143 8.66406H3.99429C4.45429 8.66406 4.84143 9.01263 4.84143 9.42835C4.84143 9.84263 4.45571 10.1926 3.99429 10.1926H0.847143C0.387143 10.1926 0 9.84406 0 9.42835Z" fill="#D2D2D2"></path><path opacity="0.2" d="M1.38852 4.72705C1.50561 4.54398 1.68746 4.41183 1.89774 4.35701C2.10803 4.30219 2.33125 4.32873 2.52281 4.43133L5.19424 5.90562C5.28689 5.9561 5.36851 6.0246 5.43431 6.10708C5.5001 6.18957 5.54874 6.28438 5.57735 6.38594C5.60597 6.48749 5.61399 6.59375 5.60094 6.69845C5.5879 6.80315 5.55405 6.90419 5.50138 6.99562C5.38407 7.17844 5.20212 7.31029 4.99186 7.36484C4.78159 7.4194 4.55849 7.39263 4.3671 7.2899L1.69567 5.81562C1.29424 5.58847 1.15281 5.11276 1.38852 4.72705Z" fill="#DADADA"></path><path d="M4.75719 1.38849C4.95463 1.27571 5.18837 1.24471 5.40838 1.30211C5.62838 1.35952 5.81718 1.50077 5.93434 1.69564L7.52577 4.35992C7.77005 4.75992 7.62434 5.25564 7.20719 5.49135C7.01006 5.60381 6.77679 5.63479 6.55714 5.57768C6.33749 5.52056 6.14886 5.37988 6.03148 5.18564L4.44005 2.51992C4.22005 2.11992 4.34148 1.59992 4.75862 1.38849H4.75719Z" fill="#E2E2E2"></path></svg></div></div><div class=" " style="position: relative; pointer-events: all; display: inline-block; opacity: 1;"><div><div style="display: flex; align-items: center; flex-direction: row;"><svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg" style="display: block; opacity: 0;"><g clip-path="url(#clip0_2589_9951)"><path d="M7 14C5.14348 14 3.36301 13.2625 2.05025 11.9497C0.737498 10.637 0 8.85652 0 7C0 5.14348 0.737498 3.36301 2.05025 2.05025C3.36301 0.737498 5.14348 0 7 0C8.85652 0 10.637 0.737498 11.9497 2.05025C13.2625 3.36301 14 5.14348 14 7C14 8.85652 13.2625 10.637 11.9497 11.9497C10.637 13.2625 8.85652 14 7 14ZM4.183 5.064L6.118 7L4.183 8.936C4.12409 8.99361 4.07719 9.06234 4.04502 9.1382C4.01285 9.21406 3.99605 9.29554 3.99559 9.37794C3.99513 9.46034 4.01101 9.54201 4.04234 9.61823C4.07366 9.69444 4.11978 9.76369 4.17805 9.82195C4.23631 9.88022 4.30556 9.92634 4.38177 9.95766C4.45799 9.98898 4.53966 10.0049 4.62206 10.0044C4.70446 10.004 4.78594 9.98715 4.8618 9.95498C4.93766 9.92281 5.00639 9.87591 5.064 9.817L7 7.882L8.936 9.817C9.05327 9.93168 9.21104 9.99548 9.37506 9.99457C9.53908 9.99365 9.69612 9.92809 9.8121 9.8121C9.92809 9.69612 9.99365 9.53908 9.99457 9.37506C9.99548 9.21104 9.93168 9.05327 9.817 8.936L7.882 7L9.817 5.064C9.87591 5.00639 9.92281 4.93766 9.95498 4.8618C9.98715 4.78594 10.004 4.70446 10.0044 4.62206C10.0049 4.53966 9.98898 4.45799 9.95766 4.38177C9.92634 4.30556 9.88022 4.23631 9.82195 4.17805C9.76369 4.11978 9.69444 4.07366 9.61823 4.04234C9.54201 4.01101 9.46034 3.99513 9.37794 3.99559C9.29554 3.99605 9.21406 4.01285 9.1382 4.04502C9.06234 4.07719 8.99361 4.12409 8.936 4.183L7 6.118L5.064 4.183C4.94673 4.06832 4.78896 4.00452 4.62494 4.00543C4.46092 4.00635 4.30388 4.07191 4.1879 4.1879C4.07191 4.30388 4.00635 4.46092 4.00543 4.62494C4.00452 4.78896 4.06832 4.94673 4.183 5.064Z" fill="#B1B1B1" fill-opacity="0.32"></path></g><defs><clippath id="clip0_2589_9951"><rect width="14" height="14" fill="white"></rect></clippath></defs></svg><div class="imt-fb-btn  right btn-animate " dir="ltr" style="opacity: 0.7; transform: translateX(15px);"><div><svg class="imt-fb-logo-img imt-fb-logo-img-big-bg" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20"><path fill="none" d="M0 0h24v24H0z"></path><path d="M5 15v2a2 2 0 0 0 1.85 1.995L7 19h3v2H7a4 4 0 0 1-4-4v-2h2zm13-5l4.4 11h-2.155l-1.201-3h-4.09l-1.199 3h-2.154L16 10h2zm-1 2.885L15.753 16h2.492L17 12.885zM8 2v2h4v7H8v3H6v-3H2V4h4V2h2zm9 1a4 4 0 0 1 4 4v2h-2V7a2 2 0 0 0-2-2h-3V3h3zM6 6H4v3h2V6zm4 0H8v3h2V6z" fill="rgba(255,255,255,1)"></path></svg><svg hidden="true" class="imt-float-ball-translated" width="11" height="11" viewBox="0 0 11 11" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="5.5" cy="5.5" r="5.5" fill="#68CD52"></circle><path d="M1.40857 5.87858L2.24148 5.18962L4.15344 6.64214C4.15344 6.64214 6.33547 4.15566 9.00658 2.48145L9.32541 2.87514C9.32541 2.87514 6.28665 5.55844 4.71735 9.07881L1.40857 5.87858Z" fill="white"></path></svg></div></div><svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg" style="display: none; opacity: 0;"><g clip-path="url(#clip0_2589_9951)"><path d="M7 14C5.14348 14 3.36301 13.2625 2.05025 11.9497C0.737498 10.637 0 8.85652 0 7C0 5.14348 0.737498 3.36301 2.05025 2.05025C3.36301 0.737498 5.14348 0 7 0C8.85652 0 10.637 0.737498 11.9497 2.05025C13.2625 3.36301 14 5.14348 14 7C14 8.85652 13.2625 10.637 11.9497 11.9497C10.637 13.2625 8.85652 14 7 14ZM4.183 5.064L6.118 7L4.183 8.936C4.12409 8.99361 4.07719 9.06234 4.04502 9.1382C4.01285 9.21406 3.99605 9.29554 3.99559 9.37794C3.99513 9.46034 4.01101 9.54201 4.04234 9.61823C4.07366 9.69444 4.11978 9.76369 4.17805 9.82195C4.23631 9.88022 4.30556 9.92634 4.38177 9.95766C4.45799 9.98898 4.53966 10.0049 4.62206 10.0044C4.70446 10.004 4.78594 9.98715 4.8618 9.95498C4.93766 9.92281 5.00639 9.87591 5.064 9.817L7 7.882L8.936 9.817C9.05327 9.93168 9.21104 9.99548 9.37506 9.99457C9.53908 9.99365 9.69612 9.92809 9.8121 9.8121C9.92809 9.69612 9.99365 9.53908 9.99457 9.37506C9.99548 9.21104 9.93168 9.05327 9.817 8.936L7.882 7L9.817 5.064C9.87591 5.00639 9.92281 4.93766 9.95498 4.8618C9.98715 4.78594 10.004 4.70446 10.0044 4.62206C10.0049 4.53966 9.98898 4.45799 9.95766 4.38177C9.92634 4.30556 9.88022 4.23631 9.82195 4.17805C9.76369 4.11978 9.69444 4.07366 9.61823 4.04234C9.54201 4.01101 9.46034 3.99513 9.37794 3.99559C9.29554 3.99605 9.21406 4.01285 9.1382 4.04502C9.06234 4.07719 8.99361 4.12409 8.936 4.183L7 6.118L5.064 4.183C4.94673 4.06832 4.78896 4.00452 4.62494 4.00543C4.46092 4.00635 4.30388 4.07191 4.1879 4.1879C4.07191 4.30388 4.00635 4.46092 4.00543 4.62494C4.00452 4.78896 4.06832 4.94673 4.183 5.064Z" fill="#B1B1B1" fill-opacity="0.32"></path></g><defs><clippath id="clip0_2589_9951"><rect width="14" height="14" fill="white"></rect></clippath></defs></svg></div></div></div><div style="position: relative; width: 100%; opacity: 0;"><div title="关闭悬浮球" class="imt-fb-close-button" style="transform: translateX(100%);"><svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_2589_9951)"><path d="M7 14C5.14348 14 3.36301 13.2625 2.05025 11.9497C0.737498 10.637 0 8.85652 0 7C0 5.14348 0.737498 3.36301 2.05025 2.05025C3.36301 0.737498 5.14348 0 7 0C8.85652 0 10.637 0.737498 11.9497 2.05025C13.2625 3.36301 14 5.14348 14 7C14 8.85652 13.2625 10.637 11.9497 11.9497C10.637 13.2625 8.85652 14 7 14ZM4.183 5.064L6.118 7L4.183 8.936C4.12409 8.99361 4.07719 9.06234 4.04502 9.1382C4.01285 9.21406 3.99605 9.29554 3.99559 9.37794C3.99513 9.46034 4.01101 9.54201 4.04234 9.61823C4.07366 9.69444 4.11978 9.76369 4.17805 9.82195C4.23631 9.88022 4.30556 9.92634 4.38177 9.95766C4.45799 9.98898 4.53966 10.0049 4.62206 10.0044C4.70446 10.004 4.78594 9.98715 4.8618 9.95498C4.93766 9.92281 5.00639 9.87591 5.064 9.817L7 7.882L8.936 9.817C9.05327 9.93168 9.21104 9.99548 9.37506 9.99457C9.53908 9.99365 9.69612 9.92809 9.8121 9.8121C9.92809 9.69612 9.99365 9.53908 9.99457 9.37506C9.99548 9.21104 9.93168 9.05327 9.817 8.936L7.882 7L9.817 5.064C9.87591 5.00639 9.92281 4.93766 9.95498 4.8618C9.98715 4.78594 10.004 4.70446 10.0044 4.62206C10.0049 4.53966 9.98898 4.45799 9.95766 4.38177C9.92634 4.30556 9.88022 4.23631 9.82195 4.17805C9.76369 4.11978 9.69444 4.07366 9.61823 4.04234C9.54201 4.01101 9.46034 3.99513 9.37794 3.99559C9.29554 3.99605 9.21406 4.01285 9.1382 4.04502C9.06234 4.07719 8.99361 4.12409 8.936 4.183L7 6.118L5.064 4.183C4.94673 4.06832 4.78896 4.00452 4.62494 4.00543C4.46092 4.00635 4.30388 4.07191 4.1879 4.1879C4.07191 4.30388 4.00635 4.46092 4.00543 4.62494C4.00452 4.78896 4.06832 4.94673 4.183 5.064Z" fill="#B1B1B1" fill-opacity="0.32"></path></g><defs><clippath id="clip0_2589_9951"><rect width="14" height="14" fill="white"></rect></clippath></defs></svg></div></div><div class="imt-fb-more-buttons btn-animate" style="margin-top: 10px; transform: translateX(60px);"><div class=" btn-animate" style="position: relative; pointer-events: all; display: inline-block; opacity: 1;"><div><div class="imt-fb-more-button"><svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg" style="width: 22px; height: 22px;"><path d="M16 7.66699H10.375" stroke="#666666" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"></path><path d="M11.625 14.333L6 14.333" stroke="#666666" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"></path><path d="M14.125 16C15.1605 16 16 15.1605 16 14.125C16 13.0895 15.1605 12.25 14.125 12.25C13.0895 12.25 12.25 13.0895 12.25 14.125C12.25 15.1605 13.0895 16 14.125 16Z" stroke="#666666" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"></path><path d="M7.875 9.75C8.91053 9.75 9.75 8.91053 9.75 7.875C9.75 6.83947 8.91053 6 7.875 6C6.83947 6 6 6.83947 6 7.875C6 8.91053 6.83947 9.75 7.875 9.75Z" stroke="#666666" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round"></path><rect x="3" y="3" width="16" height="16" rx="1.66667" stroke="#666666" stroke-width="1.4"></rect></svg></div></div></div><div class=" btn-animate" style="position: relative; pointer-events: all; display: inline-block; opacity: 1;"><div><div class="imt-fb-more-button"><svg class="imt-fb-feedback" width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M11.0003 14.2749C11.213 14.2749 11.3895 14.2047 11.5299 14.0643C11.6705 13.9239 11.7408 13.7473 11.7408 13.5345C11.7408 13.3218 11.6705 13.1453 11.5299 13.0049C11.3895 12.8645 11.213 12.7943 11.0003 12.7943C10.7877 12.7943 10.6111 12.8645 10.4707 13.0049C10.3302 13.1453 10.2599 13.3218 10.2599 13.5345C10.2599 13.7473 10.3302 13.9239 10.4707 14.0643C10.6111 14.2047 10.7877 14.2749 11.0003 14.2749ZM11.0003 11.0842C11.1954 11.0842 11.3587 11.0185 11.4903 10.8869C11.622 10.7552 11.6878 10.5918 11.6878 10.3967V6.23645C11.6878 6.04135 11.622 5.87803 11.4903 5.74649C11.3587 5.6148 11.1954 5.54895 11.0003 5.54895C10.8052 5.54895 10.6419 5.6148 10.5104 5.74649C10.3787 5.87803 10.3128 6.04135 10.3128 6.23645V10.3967C10.3128 10.5918 10.3787 10.7552 10.5104 10.8869C10.6419 11.0185 10.8052 11.0842 11.0003 11.0842ZM5.53562 16.8311L3.70045 18.666C3.43966 18.9269 3.13968 18.9861 2.80051 18.8434C2.4615 18.7005 2.29199 18.4434 2.29199 18.072V4.73816C2.29199 4.27509 2.45241 3.88314 2.77324 3.5623C3.09408 3.24147 3.48603 3.08105 3.9491 3.08105H18.0516C18.5146 3.08105 18.9066 3.24147 19.2274 3.5623C19.5482 3.88314 19.7087 4.27509 19.7087 4.73816V15.174C19.7087 15.637 19.5482 16.029 19.2274 16.3498C18.9066 16.6706 18.5146 16.8311 18.0516 16.8311H5.53562ZM4.95033 15.4561H18.0516C18.1221 15.4561 18.1868 15.4266 18.2454 15.3678C18.3042 15.3092 18.3337 15.2445 18.3337 15.174V4.73816C18.3337 4.66758 18.3042 4.60295 18.2454 4.54428C18.1868 4.48546 18.1221 4.45605 18.0516 4.45605H3.9491C3.87851 4.45605 3.81389 4.48546 3.75522 4.54428C3.6964 4.60295 3.66699 4.66758 3.66699 4.73816V16.7254L4.95033 15.4561Z" fill="#666666"></path></svg></div></div></div></div></div></div></template></div><div class="xl-chrome-ext-bar_4DB361DE-01F7-4376-B494-639E489D19ED" id="xl_chrome_ext_4DB361DE-01F7-4376-B494-639E489D19ED" data-v-app="" style="display: block;"><div class=""><div id="xl_chrome_ext_video_tag_wrapper_4DB361DE-01F7-4376-B494-639E489D19ED" config="[object Object]" uiversion="v1" exception="false" latestvideosrc="" isshowvideotag="false" isshowdownloadbar="false" isshowcloudaddbar="false"><!----><div class="_options-wrapper_pujw6_3" style="display: none;"><a id="xl_chrome_ext_bar_close" href="javascript:;"></a><div class="_xl-chrome-ext-bar__logo_pujw6_30"></div><!----><!----><div class="_xl-chrome-ext-tips_pujw6_62"><a id="xl_chrome_ext_bar_screen" href="javascript:;" class="_xl-chrome-ext-bar__option_pujw6_78"></a><div class="_xl-chrome-ext-title_pujw6_69">投屏</div></div><div class="_xl-chrome-ext-tips_pujw6_62"><a id="xl_chrome_ext_bar_copy" class="_xl-chrome-ext-bar__option_pujw6_78" href="javascript:;"></a><div class="_xl-chrome-ext-title_pujw6_69">复制链接</div></div></div></div><div class="badge_wrapper_4DB361DE-01F7-4376-B494-639E489D19ED _badge_wrapper_eor63_1" id="badge_wrapper_4DB361DE-01F7-4376-B494-639E489D19ED"><div class="_logo_eor63_20"></div><span class="_line_eor63_27"></span><div class="_text_wrapper_eor63_33"><span class="_download_icon_eor63_39"></span><span class="_text_eor63_33">高速下载</span></div></div><!----><!----><!----><!----><div class="_container_wq4mj_4"><!----></div></div></div></html>