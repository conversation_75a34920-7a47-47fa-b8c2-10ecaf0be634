openapi: 3.0.1
info:
  title: 下一代智能安全防控平台
  description: ''
  version: 1.0.0
tags:
  - name: 报告管理
paths:
  /sast/reports/list:
    get:
      summary: 获取报告列表（分页）
      deprecated: false
      description: 分页查询报告信息列表，支持搜索和筛选
      operationId: getReportList
      tags:
        - 报告管理
      parameters:
        - name: page
          in: query
          description: 当前页码
          required: false
          schema:
            type: integer
            minimum: 1
            default: 1
            example: 1
        - name: size
          in: query
          description: 每页记录数
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 10
            example: 10
        - name: keyword
          in: query
          description: 搜索关键词（项目名称，任务名称）
          required: false
          schema:
            type: string
            example: 漏洞检测
        - name: Authorization
          in: header
          description: ''
          example: >-
            Bearer
            eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiIxIiwicm5TdHIiOiJ2blJ3dmx2M1lQVkQycFJ0a3ZqU2JFcmtNSVRnYVR6MyIsInVzZXJJZCI6IjEiLCJ1c2VyTmFtZSI6ImFkbWluIiwib3JnSWQiOiIxMDAiLCJvcmdOYW1lIjoi6YeN5bqG5biC6L2o6YGT5Lqk6YCa77yI6ZuG5Zui77yJ5pyJ6ZmQ5YWs5Y-4In0.0bueZdo9qQB6U3l-JBzllgdpa6GUxpaK0Zaj__vMZ_0
          schema:
            type: string
            default: >-
              Bearer
              eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiIxIiwicm5TdHIiOiJ2blJ3dmx2M1lQVkQycFJ0a3ZqU2JFcmtNSVRnYVR6MyIsInVzZXJJZCI6IjEiLCJ1c2VyTmFtZSI6ImFkbWluIiwib3JnSWQiOiIxMDAiLCJvcmdOYW1lIjoi6YeN5bqG5biC6L2o6YGT5Lqk6YCa77yI6ZuG5Zui77yJ5pyJ6ZmQ5YWs5Y-4In0.0bueZdo9qQB6U3l-JBzllgdpa6GUxpaK0Zaj__vMZ_0
      responses:
        '200':
          description: 成功获取报告列表
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 0
                  msg:
                    type: string
                    example: string
                  data:
                    type: object
                    properties:
                      records:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: string
                              description: 报告ID
                            reportName:
                              type: string
                              description: 报告名称
                              example: 漏洞测试分析报告
                            projectName:
                              type: string
                              description: 项目名称
                            taskName:
                              type: string
                              description: 任务名称
                              example: 漏洞检测测试
                            defectCount:
                              type: integer
                              description: 缺陷数量
                              example: 134
                            vulnerabilityStats:
                              type: object
                              description: 危险分类
                              properties:
                                highRisk:
                                  type: integer
                                  description: 高危数量
                                  example: 100
                                mediumRisk:
                                  type: integer
                                  description: 中危数量
                                  example: 30
                                lowRisk:
                                  type: integer
                                  description: 低危数量
                                  example: 4
                            verificationSuccess:
                              type: integer
                              description: 验证成功数量
                              example: 130
                            verificationFailure:
                              type: integer
                              description: 验证失败数量
                              example: 4
                            reportGenerationTime:
                              type: string
                              description: 报告生成时间
                              example: '2024-03-12 12:00:00'
                          required:
                            - projectName
                            - vulnerabilityStats
                            - reportGenerationTime
                            - verificationFailure
                            - verificationSuccess
                            - taskName
                            - defectCount
                            - reportName
                            - id
                      total:
                        type: integer
                        description: 总记录数
                        example: 10
                      size:
                        type: integer
                        description: 每页记录数
                        example: 10
                      current:
                        type: integer
                        description: 当前页码
                        example: 1
                      pages:
                        type: integer
                        description: 总页数
                        example: 1
              example:
                code: 0
                msg: string
                data:
                  records:
                    - id: '1'
                      reportName: 漏洞测试分析报告
                      projectName: cms系统项目
                      taskName: 漏洞检测测试
                      defectCount: 134
                      vulnerabilityStats:
                        highRisk: 100
                        mediumRisk: 30
                        lowRisk: 4
                      verificationSuccess: 130
                      verificationFailure: 4
                      reportGenerationTime: '2024-03-12 12:00:00'
                    - id: '2'
                      reportName: 漏洞测试分析报告
                      projectName: cms系统项目
                      taskName: 漏洞检测测试
                      defectCount: 134
                      vulnerabilityStats:
                        highRisk: 100
                        mediumRisk: 30
                        lowRisk: 4
                      verificationSuccess: 130
                      verificationFailure: 4
                      reportGenerationTime: '2024-03-12 12:00:00'
                  total: 10
                  size: 10
                  current: 1
                  pages: 1
          headers: {}
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
          headers: {}
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
          headers: {}
      security: []
components:
  schemas:
    ErrorResponse:
      type: object
      description: 错误响应
      required:
        - code
        - msg
      properties:
        code:
          type: integer
          description: 错误码
          example: 500
        msg:
          type: string
          description: 错误消息
          example: 操作失败
        data:
          type: object
          description: 错误详情
          properties: {}
          nullable: true
          example: null
  securitySchemes: {}
servers: []
security: []
