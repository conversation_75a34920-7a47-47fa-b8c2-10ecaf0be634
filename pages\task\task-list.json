{"type": "page", "title": "任务列表", "remark": null, "name": "task-list-page", "body": [{"type": "container", "className": "bg-white p-3 m-b-sm", "body": [{"type": "flex", "justify": "space-between", "items": [{"type": "container", "body": [{"type": "button", "label": "返回项目列表", "level": "default", "icon": "fa fa-arrow-left", "actionType": "url", "url": "/project"}]}, {"type": "container", "body": [{"type": "input-text", "name": "keywords", "placeholder": "请输入任务名称", "clearable": true, "className": "w-md", "addOn": {"type": "button", "icon": "fa fa-search", "level": "light"}}]}, {"type": "button-group", "buttons": [{"type": "button", "label": "新建任务", "level": "primary", "icon": "fa fa-plus", "actionType": "dialog", "dialog": {"title": "新建任务", "size": "lg", "body": {"type": "form", "api": "post:/api/tasks", "body": [{"type": "input-text", "name": "taskName", "label": "任务名称", "required": true, "placeholder": "请输入任务名称"}, {"type": "select", "name": "taskType", "label": "任务类型", "required": true, "options": [{"label": "SQL注入检测", "value": "sql_injection"}, {"label": "XSS漏洞扫描", "value": "xss_scan"}, {"label": "CSRF检测", "value": "csrf_check"}, {"label": "文件上传漏洞", "value": "file_upload"}, {"label": "权限绕过检测", "value": "auth_bypass"}]}, {"type": "textarea", "name": "description", "label": "任务描述", "placeholder": "请输入任务描述"}, {"type": "select", "name": "priority", "label": "优先级", "required": true, "options": [{"label": "高", "value": "high"}, {"label": "中", "value": "medium"}, {"label": "低", "value": "low"}]}]}}}, {"type": "button", "label": "批量执行", "level": "info", "icon": "fa fa-play", "actionType": "ajax", "api": "post:/api/tasks/batch-execute", "confirmText": "确定要批量执行选中的任务吗？"}]}]}]}, {"type": "crud", "name": "taskList", "api": "/pages/task/task-data.json", "syncLocation": false, "columns": [{"name": "id", "label": "序号", "width": 80, "type": "text"}, {"name": "taskName", "label": "任务名称", "sortable": true, "searchable": true}, {"name": "taskType", "label": "任务类型", "type": "mapping", "map": {"sql_injection": "<span class='label label-danger'>SQL注入检测</span>", "xss_scan": "<span class='label label-warning'>XSS漏洞扫描</span>", "csrf_check": "<span class='label label-info'>CSRF检测</span>", "file_upload": "<span class='label label-primary'>文件上传漏洞</span>", "auth_bypass": "<span class='label label-success'>权限绕过检测</span>"}}, {"name": "status", "label": "执行状态", "type": "mapping", "map": {"running": "<span class='label label-info'>执行中</span>", "completed": "<span class='label label-success'>已完成</span>", "pending": "<span class='label label-warning'>待执行</span>", "failed": "<span class='label label-danger'>执行失败</span>"}}, {"name": "priority", "label": "优先级", "type": "mapping", "map": {"high": "<span class='label label-danger'>高</span>", "medium": "<span class='label label-warning'>中</span>", "low": "<span class='label label-info'>低</span>"}}, {"name": "progress", "label": "执行进度", "type": "tpl", "tpl": "<div class='progress' style='margin-bottom: 0;'><div class='progress-bar progress-bar-info' style='width: ${progress}%;'>${progress}%</div></div>"}, {"name": "vulnerabilityCount", "label": "发现漏洞", "type": "tpl", "tpl": "<span class='badge badge-${vulnerabilityCount > 0 ? \"danger\" : \"success\"}'>${vulnerabilityCount}</span>"}, {"name": "createTime", "label": "创建时间", "type": "datetime", "format": "YYYY-MM-DD HH:mm:ss"}, {"type": "operation", "label": "操作", "width": 200, "buttons": [{"type": "button", "label": "查看详情", "level": "link", "icon": "fa fa-eye", "actionType": "url", "url": "/task-detail?taskId=${id}"}, {"type": "button", "label": "执行", "level": "link", "icon": "fa fa-play", "actionType": "ajax", "api": "post:/api/tasks/${id}/execute", "confirmText": "确定要执行这个任务吗？", "visibleOn": "${status === 'pending'}"}, {"type": "button", "label": "停止", "level": "link", "className": "text-warning", "icon": "fa fa-stop", "actionType": "ajax", "api": "post:/api/tasks/${id}/stop", "confirmText": "确定要停止这个任务吗？", "visibleOn": "${status === 'running'}"}, {"type": "button", "label": "删除", "level": "link", "className": "text-danger", "icon": "fa fa-trash", "actionType": "ajax", "api": "delete:/api/tasks/${id}", "confirmText": "确定要删除这个任务吗？"}]}], "affixHeader": true, "columnsTogglable": false, "placeholder": "暂无数据", "tableClassName": "table-db table-striped", "headerClassName": "crud-table-header", "footerClassName": "crud-table-footer", "toolbarClassName": "crud-table-toolbar", "bodyClassName": "panel-default", "defaultParams": {"perPage": 10}, "perPageAvailable": [10, 20, 50, 100], "messages": {"fetchFailed": "获取数据失败", "saveOrderFailed": "保存顺序失败", "saveOrderSuccess": "保存顺序成功", "quickSaveSuccess": "保存成功", "quickSaveFailed": "保存失败"}}]}