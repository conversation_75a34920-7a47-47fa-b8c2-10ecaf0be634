{"type": "page", "title": "缺陷管理", "remark": null, "name": "defect-management-page", "initApi": "/pages/vulnerability/vulnerability-data.json", "body": [{"type": "container", "className": "bg-white p-3 m-b-sm", "body": [{"type": "grid", "columns": [{"type": "card", "className": "m-b-none", "style": {"border": "1px solid #e8e8e8", "borderRadius": "8px", "padding": "20px", "backgroundColor": "#fff", "boxShadow": "0 2px 8px rgba(0,0,0,0.1)"}, "body": [{"type": "flex", "justify": "space-between", "align": "center", "items": [{"type": "container", "body": [{"type": "tpl", "tpl": "高危", "style": {"fontSize": "14px", "color": "#666", "marginBottom": "8px"}}, {"type": "tpl", "tpl": "${stats.high || 140}", "style": {"fontSize": "32px", "fontWeight": "bold", "color": "#ff4d4f", "lineHeight": "1"}}, {"type": "tpl", "tpl": "高危占比 30%", "style": {"fontSize": "12px", "color": "#999", "marginTop": "4px"}}]}, {"type": "icon", "icon": "fa fa-exclamation-triangle", "style": {"fontSize": "48px", "color": "#ff4d4f", "opacity": "0.8"}}]}]}, {"type": "card", "className": "m-b-none", "style": {"border": "1px solid #e8e8e8", "borderRadius": "8px", "padding": "20px", "backgroundColor": "#fff", "boxShadow": "0 2px 8px rgba(0,0,0,0.1)"}, "body": [{"type": "flex", "justify": "space-between", "align": "center", "items": [{"type": "container", "body": [{"type": "tpl", "tpl": "中危", "style": {"fontSize": "14px", "color": "#666", "marginBottom": "8px"}}, {"type": "tpl", "tpl": "${stats.medium || 109}", "style": {"fontSize": "32px", "fontWeight": "bold", "color": "#fa8c16", "lineHeight": "1"}}, {"type": "tpl", "tpl": "中危占比 30%", "style": {"fontSize": "12px", "color": "#999", "marginTop": "4px"}}]}, {"type": "icon", "icon": "fa fa-exclamation-circle", "style": {"fontSize": "48px", "color": "#fa8c16", "opacity": "0.8"}}]}]}, {"type": "card", "className": "m-b-none", "style": {"border": "1px solid #e8e8e8", "borderRadius": "8px", "padding": "20px", "backgroundColor": "#fff", "boxShadow": "0 2px 8px rgba(0,0,0,0.1)"}, "body": [{"type": "flex", "justify": "space-between", "align": "center", "items": [{"type": "container", "body": [{"type": "tpl", "tpl": "低危", "style": {"fontSize": "14px", "color": "#666", "marginBottom": "8px"}}, {"type": "tpl", "tpl": "${stats.low || 265}", "style": {"fontSize": "32px", "fontWeight": "bold", "color": "#52c41a", "lineHeight": "1"}}, {"type": "tpl", "tpl": "低危占比 40%", "style": {"fontSize": "12px", "color": "#999", "marginTop": "4px"}}]}, {"type": "icon", "icon": "fa fa-info-circle", "style": {"fontSize": "48px", "color": "#52c41a", "opacity": "0.8"}}]}]}]}]}, {"type": "container", "className": "bg-white p-3 m-b-sm", "body": [{"type": "flex", "justify": "space-between", "items": [{"type": "container", "body": [{"type": "input-text", "name": "keywords", "placeholder": "搜索缺陷名称", "clearable": true, "className": "w-md", "addOn": {"type": "button", "icon": "fa fa-search", "level": "light"}}]}, {"type": "button-group", "buttons": [{"type": "button", "label": "导出报告", "level": "primary", "icon": "fa fa-download", "actionType": "ajax", "api": "post:/api/vulnerabilities/export", "confirmText": "确定要导出缺陷报告吗？"}, {"type": "button", "label": "批量修复", "level": "info", "icon": "fa fa-wrench", "actionType": "ajax", "api": "post:/api/vulnerabilities/batch-fix", "confirmText": "确定要批量修复选中的缺陷吗？"}, {"type": "button", "label": "忽略缺陷", "level": "warning", "icon": "fa fa-eye-slash", "actionType": "ajax", "api": "post:/api/vulnerabilities/batch-ignore", "confirmText": "确定要忽略选中的缺陷吗？"}]}]}]}, {"type": "crud", "api": "/pages/vulnerability/vulnerability-data.json", "className": "bg-white", "headerToolbar": [{"type": "tpl", "tpl": "缺陷列表", "className": "text-lg font-bold"}, "bulkActions", "pagination"], "bulkActions": [{"label": "批量修复", "actionType": "ajax", "api": "post:/api/vulnerabilities/batch-fix", "confirmText": "确定要批量修复选中的缺陷吗？", "type": "button", "level": "info"}, {"label": "批量忽略", "actionType": "ajax", "api": "post:/api/vulnerabilities/batch-ignore", "confirmText": "确定要忽略选中的缺陷吗？", "type": "button", "level": "warning"}], "columns": [{"name": "sequence", "label": "序号", "type": "text", "width": 80}, {"name": "title", "label": "缺陷名称", "type": "text", "searchable": true, "width": 200}, {"name": "type", "label": "漏洞类型", "type": "text", "width": 120}, {"name": "description", "label": "问题描述", "type": "text", "width": 300}, {"name": "filePath", "label": "文件路径", "type": "text", "width": 250}, {"name": "line", "label": "缺陷行号", "type": "text", "width": 100}, {"name": "severity", "label": "危险等级", "type": "mapping", "width": 100, "map": {"high": "<span class='label label-danger'>高危</span>", "medium": "<span class='label label-warning'>中危</span>", "low": "<span class='label label-success'>低危</span>"}}, {"name": "discoveredTime", "label": "发现时间", "type": "datetime", "format": "YYYY-MM-DD HH:mm:ss", "width": 160}, {"name": "status", "label": "状态", "type": "mapping", "width": 100, "map": {"open": "<span class='label label-info'>待处理</span>", "fixing": "<span class='label label-warning'>修复中</span>", "fixed": "<span class='label label-success'>已修复</span>", "ignored": "<span class='label label-default'>已忽略</span>"}}, {"type": "operation", "label": "操作", "width": 150, "buttons": [{"type": "button", "label": "查看详情", "actionType": "dialog", "level": "link", "dialog": {"title": "缺陷详情", "body": [{"type": "form", "mode": "horizontal", "disabled": true, "body": [{"type": "input-text", "name": "title", "label": "缺陷名称"}, {"type": "input-text", "name": "type", "label": "漏洞类型"}, {"type": "textarea", "name": "description", "label": "问题描述"}, {"type": "input-text", "name": "filePath", "label": "文件路径"}, {"type": "input-text", "name": "line", "label": "缺陷行号"}, {"type": "select", "name": "severity", "label": "危险等级", "options": [{"label": "高危", "value": "high"}, {"label": "中危", "value": "medium"}, {"label": "低危", "value": "low"}]}, {"type": "datetime", "name": "discoveredTime", "label": "发现时间", "format": "YYYY-MM-DD HH:mm:ss"}, {"type": "select", "name": "status", "label": "状态", "options": [{"label": "待处理", "value": "open"}, {"label": "修复中", "value": "fixing"}, {"label": "已修复", "value": "fixed"}, {"label": "已忽略", "value": "ignored"}]}]}]}}, {"type": "button", "label": "修复", "actionType": "ajax", "level": "primary", "size": "sm", "api": "post:/api/vulnerabilities/${id}/fix", "confirmText": "确定要修复这个缺陷吗？", "visibleOn": "${status !== 'fixed'}"}, {"type": "button", "label": "忽略", "actionType": "ajax", "level": "default", "size": "sm", "api": "post:/api/vulnerabilities/${id}/ignore", "confirmText": "确定要忽略这个缺陷吗？", "visibleOn": "${status !== 'ignored'}"}]}]}]}